package com.mylog.controller;

import com.mylog.service.MessageService;
import com.mylog.service.ProjectService;
import com.mylog.service.TaskService;
import com.mylog.service.WorkflowInstanceService;
import com.mylog.service.EventCheckInService;
import com.mylog.service.RewardPenaltyRecordService;
import com.mylog.model.workflow.WorkflowInstance;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;
import java.util.List;

/**
 * 侧边栏数据API控制器
 * 用于提供侧边栏徽章数据的动态更新
 */
@RestController
@RequestMapping("/api/sidebar")
@CrossOrigin(origins = "*")
public class SidebarApiController {
    @Autowired
    private com.mylog.service.SubTaskService subTaskService;

    private static final Logger logger = LoggerFactory.getLogger(SidebarApiController.class);

    @Autowired
    private TaskService taskService;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private MessageService messageService;

    @Autowired
    private WorkflowInstanceService workflowInstanceService;
    @Autowired
    private EventCheckInService eventCheckInService;

    @Autowired
    private RewardPenaltyRecordService rewardPenaltyRecordService;

    /**
     * 获取侧边栏所有徽章数据
     * 
     * @return 包含所有徽章数据的JSON响应
     */
    @GetMapping("/badges")
    public ResponseEntity<Map<String, Object>> getSidebarBadges() {
        Map<String, Object> badges = new HashMap<>();
        // logger.info("开始获取侧边栏徽章数据");

        try {
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            if (auth != null && auth.isAuthenticated() && !auth.getName().equals("anonymousUser")) {
                String username = auth.getName();
                // logger.info("用户 {} 请求侧边栏徽章数据", username); //
                // 获取当前用户相关的数据（与GlobalModelInterceptor保持一致）
                // 获取进行中的订单任务数量

                badges.put("inProgressTaskCount", taskService.countOrderTasksByResponsibleAndStatus(username, "进行中"));
                badges.put("inProgressDifficultTaskCount",
                        taskService.countInProgressDifficultTasksByResponsible(username));
                badges.put("inProgressSpecialTaskCount",
                        taskService.countInProgressSpecialTasksByResponsible(username));
                badges.put("inProgressDelegatedTaskCount", taskService.countInProgressDelegatedTasks(username));
                badges.put("inProgressTrainingTaskCount", taskService.countInProgressTrainingTasks(username));
                badges.put("myInProgressProjectCount",
                        projectService.countProjectsByResponsibleAndStatus(username, "进行中"));

                // 评论管理进行中子任务数量
                badges.put("inProgressSubTaskCount", subTaskService.countInProgressSubTasks());
                logger.info("test{}", subTaskService.countInProgressSubTasks());
                // 获取未读消息数量
                Long unreadCount = messageService.countUnreadMessagesByReceiver(username);
                logger.info("unreadMessageCount: {}", unreadCount);
                badges.put("unreadMessageCount", unreadCount);

                // 获取待审批任务数量
                badges.put("todoTasksCount", (long) workflowInstanceService.findTodoTasks(username).size());
                // 获取我发起的处理中流程数量
                badges.put("myProcessingWorkflowCount",
                        (long) (workflowInstanceService
                                .findInstancesByInitiatorAndStatus(username, WorkflowInstance.WorkflowStatus.PROCESSING)
                                .size()
                                + workflowInstanceService.findInstancesByInitiatorAndStatus(username,
                                        WorkflowInstance.WorkflowStatus.DRAFT).size()));
                // 获取待签到数量（如果有这个功能）
                try {
                    List<EventCheckInService.PendingCheckInDTO> pendingCheckIns = eventCheckInService
                            .getPendingCheckIns(username);
                    badges.put("pendingCheckInCount", (long) pendingCheckIns.size());
                } catch (Exception e) {
                    badges.put("pendingCheckInCount", 0L);
                }

                // 获取最近奖罚记录数量
                try {
                    badges.put("recentRewardPenaltyCount", rewardPenaltyRecordService.countRecentRecords(username));
                } catch (Exception e) {
                    badges.put("recentRewardPenaltyCount", 0L);
                }
                // 获取全局数据（管理员可见）
                badges.put("inProgressProjectCount", projectService.countProjectsByStatus("进行中"));
                badges.put("allInProgressTaskCount", taskService.countNonArchivedInProgressTasks());
                badges.put("processingInstanceCount", (long) workflowInstanceService
                        .findInstancesByStatus(WorkflowInstance.WorkflowStatus.PROCESSING).size());
                badges.put("success", true);
                badges.put("timestamp", System.currentTimeMillis());

                // logger.info("成功获取侧边栏徽章数据，用户: {}, 数据: {}", username, badges);

            } else {
                badges.put("success", false);
                badges.put("message", "用户未认证");
                logger.warn("用户未认证，无法获取侧边栏徽章数据");
            }
        } catch (Exception e) {
            badges.put("success", false);
            badges.put("message", "获取侧边栏数据失败: " + e.getMessage());
            logger.error("获取侧边栏数据失败", e);
        }

        return ResponseEntity.ok(badges);
    }
}
