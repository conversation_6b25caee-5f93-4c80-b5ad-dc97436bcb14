﻿<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head th:replace="~{fragments/layout :: head('我的消息')}">
    <meta charset="UTF-8">
    <title>我的消息</title>
    <style>
        .message-item {
            border-left: 3px solid #dee2e6;
            transition: all 0.2s;
        }
        .message-item:hover {
            border-left-color: #0d6efd;
            background-color: #f1f8ff;
        }
        .message-item.unread {
            border-left-color: #0d6efd;
            background-color: #f0f7ff;
        }
        .message-item.unread .message-title {
            font-weight: bold;
        }
        
        .search-highlight {
            background-color: #fff3cd;
            padding: 1px 3px;
            border-radius: 3px;
        }
        
        .search-form {
            background: linear-gradient(135deg, #f8f9ff 0%, #e8f4f8 100%);
            border-radius: 10px;
            border: 1px solid #e3f2fd;
        }
        
        .pagination .page-link {
            border-radius: 6px;
            margin: 0 2px;
            border: 1px solid #dee2e6;
        }
        
        .pagination .page-item.active .page-link {
            background: linear-gradient(135deg, #0d6efd, #6610f2);
            border-color: #0d6efd;
        }
        
        .page-jump-form {
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }
        
        .page-jump-input {
            width: 60px;
            height: 32px;
            text-align: center;
            border: 1px solid #dee2e6;
            border-radius: 4px;
        }
    </style>
</head>
<body th:replace="~{fragments/layout :: body(~{::div.content-wrapper}, ~{})}">
    <div class="content-wrapper">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2">
                我的消息 
                <span th:if="${isSearching}" class="badge bg-primary">搜索结果</span>
                <span th:if="${!isSearching}">(<span class="text-danger" th:text="${unreadMessageCount}">0</span>/<span th:text="${messagePage.totalElements}">0</span>)</span>
                <span th:if="${isSearching}" class="text-muted">
                    (找到 <span th:text="${messagePage.totalElements}">0</span> 条匹配记录)
                </span>
            </h1>
            <div class="btn-toolbar mb-2 mb-md-0">
                <form th:action="@{/messages/mark-all-read}" method="post" class="me-2">
                    <button type="submit" class="btn btn-sm btn-outline-secondary">
                        <i class="bi bi-check-all me-1"></i>全部标为已读
                    </button>
                </form>
                <a th:href="@{/messages(onlyUnread=true)}" class="btn btn-sm btn-outline-primary ms-2" title="只显示未读取消息">
                    <i class="bi bi-eye-slash me-1"></i>显示未读取消息
                </a>
            </div>
        </div>

                <!-- 搜索表单 -->
                <div class="card mb-3 search-form">
                    <div class="card-body">
                        <form th:action="@{/messages}" method="get" class="row g-3">
                            <div class="col-md-8">
                                <div class="input-group">
                                    <input type="text" 
                                           name="keyword" 
                                           th:value="${keyword}"
                                           class="form-control" 
                                           placeholder="搜索消息标题或内容..."
                                           aria-label="搜索关键词"
                                           id="searchInput"
                                           autocomplete="off">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-search me-1"></i>搜索
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-4 d-flex align-items-center">
                                <a th:href="@{/messages}" class="btn btn-outline-secondary" th:if="${isSearching}">
                                    <i class="bi bi-x-circle me-1"></i>清除搜索
                                </a>
                                <small class="text-muted ms-2" th:if="${!isSearching}">
                                    <i class="bi bi-info-circle me-1"></i>支持搜索标题和内容
                                </small>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 消息提示 -->
                <div th:if="${message}" class="alert alert-success alert-dismissible fade show" role="alert">
                    <span th:text="${message}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>

                <div th:if="${error}" class="alert alert-danger alert-dismissible fade show" role="alert">
                    <span th:text="${error}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>

                <!-- 消息列表 -->
                <div class="card">
                    <div class="card-body p-0">
                        <div th:if="${messagePage.empty}" class="p-4 text-center text-muted">
                            <i class="bi bi-envelope-open fs-1" th:if="${!isSearching}"></i>
                            <i class="bi bi-search fs-1 text-warning" th:if="${isSearching}"></i>
                            <p class="mt-3" th:if="${!isSearching}">暂无消息</p>
                            <div th:if="${isSearching}" class="mt-3">
                                <p>没有找到包含 "<span th:text="${keyword ?: ''}" class="fw-bold text-primary"></span>" 的消息</p>
                                <a th:href="@{/messages}" class="btn btn-outline-primary btn-sm mt-2">
                                    <i class="bi bi-arrow-left me-1"></i>返回所有消息
                                </a>
                            </div>
                        </div>

                        <div th:unless="${messagePage.empty}" class="list-group list-group-flush">
                            <div th:each="msg : ${messagePage.content}"
                                 th:class="${msg.read ? 'list-group-item message-item' : 'list-group-item message-item unread'}">
                                <div class="d-flex w-100 justify-content-between align-items-center">
                                    <div class="d-flex align-items-center">
                                        <a th:href="@{/messages/{id}(id=${msg.messageId})}" class="text-decoration-none text-dark">
                                            <h5 class="mb-1 message-title" th:text="${msg.messageTitle}">消息标题</h5>
                                        </a>
                                    </div>
                                    <div class="d-flex align-items-center gap-2">
                                        <small th:text="${msg.createdDateStr}">2023-01-01 12:00:00</small>
                                        <form th:action="@{/messages/delete}" method="post" class="d-inline ms-2">
                                            <input type="hidden" name="messageId" th:value="${msg.messageId}">
                                            <button type="submit" class="btn btn-sm btn-outline-danger" onclick="return confirm('确定要删除这条消息吗？')">
                                                <i class="bi bi-trash"></i> 删除
                                            </button>
                                        </form>
                                    </div>
                                </div>
                                <p class="mb-1" style="white-space: pre-line;" th:utext="${msg.messageContent}">消息内容...</p>
                                <div class="d-flex justify-content-between align-items-center mt-2">
                                    <div>
                                        <form th:if="${!msg.read}" th:action="@{/messages/mark-read}" method="post" class="d-inline">
                                            <input type="hidden" name="messageId" th:value="${msg.messageId}">
                                            <button type="submit" class="btn btn-sm btn-outline-primary">
                                                <i class="bi bi-check"></i> 标为已读
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 分页控件 -->
                <div class="mt-4" th:if="${messagePage.totalPages > 0}">
                    <nav aria-label="Page navigation" class="d-flex justify-content-center flex-column">
                        <ul class="pagination mb-2 justify-content-center">
                            <!-- 首页按钮 -->
                            <li class="page-item" th:classappend="${messagePage.first ? 'disabled' : ''}" th:if="${messagePage.totalPages > 7}">
                                <a class="page-link" 
                                   th:href="@{/messages(page=0, size=${messagePage.size}, keyword=${keyword})}" 
                                   aria-label="First">
                                    <span aria-hidden="true">&laquo;&laquo;</span>
                                </a>
                            </li>
                            
                            <!-- 上一页 -->
                            <li class="page-item" th:classappend="${messagePage.first ? 'disabled' : ''}">
                                <a class="page-link" 
                                   th:href="@{/messages(page=${messagePage.first ? 0 : messagePage.number - 1}, size=${messagePage.size}, keyword=${keyword})}"
                                   aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            
                            <!-- 页码显示逻辑 -->
                            <th:block th:with="totalPages=${messagePage.totalPages}, currentPage=${messagePage.number}">
                                <!-- 当总页数不超过7页时，显示所有页码 -->
                                <th:block th:if="${totalPages <= 7}">
                                    <li class="page-item" th:each="i : ${#numbers.sequence(0, totalPages - 1)}"
                                        th:classappend="${i == currentPage ? 'active' : ''}">
                                        <a class="page-link" 
                                           th:href="@{/messages(page=${i}, size=${messagePage.size}, keyword=${keyword})}" 
                                           th:text="${i + 1}">1</a>
                                    </li>
                                </th:block>
                                
                                <!-- 当总页数超过7页时，使用智能分页显示 -->
                                <th:block th:if="${totalPages > 7}">
                                    <!-- 当前页在前3页时：显示 1 2 3 4 5 ... 最后页 -->
                                    <th:block th:if="${currentPage <= 2}">
                                        <li class="page-item" th:each="i : ${#numbers.sequence(0, 4)}"
                                            th:classappend="${i == currentPage ? 'active' : ''}">
                                            <a class="page-link" 
                                               th:href="@{/messages(page=${i}, size=${messagePage.size}, keyword=${keyword})}" 
                                               th:text="${i + 1}">1</a>
                                        </li>
                                        <li class="page-item disabled" th:if="${totalPages > 6}">
                                            <span class="page-link">...</span>
                                        </li>
                                        <li class="page-item" th:if="${totalPages > 5}">
                                            <a class="page-link" 
                                               th:href="@{/messages(page=${totalPages - 1}, size=${messagePage.size}, keyword=${keyword})}" 
                                               th:text="${totalPages}">最后页</a>
                                        </li>
                                    </th:block>
                                    
                                    <!-- 当前页在后3页时：显示 1 ... 倒数第5页 倒数第4页 倒数第3页 倒数第2页 最后页 -->
                                    <th:block th:if="${currentPage >= totalPages - 3}">
                                        <li class="page-item">
                                            <a class="page-link" 
                                               th:href="@{/messages(page=0, size=${messagePage.size}, keyword=${keyword})}" 
                                               th:text="1">1</a>
                                        </li>
                                        <li class="page-item disabled" th:if="${totalPages > 6}">
                                            <span class="page-link">...</span>
                                        </li>
                                        <li class="page-item" th:each="i : ${#numbers.sequence(totalPages - 5, totalPages - 1)}"
                                            th:if="${i >= 0}"
                                            th:classappend="${i == currentPage ? 'active' : ''}">
                                            <a class="page-link" 
                                               th:href="@{/messages(page=${i}, size=${messagePage.size}, keyword=${keyword})}" 
                                               th:text="${i + 1}">1</a>
                                        </li>
                                    </th:block>
                                    
                                    <!-- 当前页在中间时：显示 1 ... 当前页-1 当前页 当前页+1 ... 最后页 -->
                                    <th:block th:if="${currentPage > 2 && currentPage < totalPages - 3}">
                                        <li class="page-item">
                                            <a class="page-link" 
                                               th:href="@{/messages(page=0, size=${messagePage.size}, keyword=${keyword})}" 
                                               th:text="1">1</a>
                                        </li>
                                        <li class="page-item disabled">
                                            <span class="page-link">...</span>
                                        </li>
                                        <li class="page-item" th:each="i : ${#numbers.sequence(currentPage - 1, currentPage + 1)}"
                                            th:classappend="${i == currentPage ? 'active' : ''}">
                                            <a class="page-link" 
                                               th:href="@{/messages(page=${i}, size=${messagePage.size}, keyword=${keyword})}" 
                                               th:text="${i + 1}">1</a>
                                        </li>
                                        <li class="page-item disabled">
                                            <span class="page-link">...</span>
                                        </li>
                                        <li class="page-item">
                                            <a class="page-link" 
                                               th:href="@{/messages(page=${totalPages - 1}, size=${messagePage.size}, keyword=${keyword})}" 
                                               th:text="${totalPages}">最后页</a>
                                        </li>
                                    </th:block>
                                </th:block>
                            </th:block>
                            
                            <!-- 下一页 -->
                            <li class="page-item" th:classappend="${messagePage.last ? 'disabled' : ''}">
                                <a class="page-link" 
                                   th:href="@{/messages(page=${messagePage.last ? messagePage.number : messagePage.number + 1}, size=${messagePage.size}, keyword=${keyword})}"
                                   aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            
                            <!-- 末页按钮 -->
                            <li class="page-item" th:classappend="${messagePage.last ? 'disabled' : ''}" th:if="${messagePage.totalPages > 7}">
                                <a class="page-link" 
                                   th:href="@{/messages(page=${messagePage.totalPages - 1}, size=${messagePage.size}, keyword=${keyword})}" 
                                   aria-label="Last">
                                    <span aria-hidden="true">&raquo;&raquo;</span>
                                </a>
                            </li>
                        </ul>
                        
                        <!-- 页面信息 -->
                        <div class="text-center text-muted mb-2">
                            <span th:if="${isSearching}" 
                                  th:text="'搜索结果 - 第 ' + (${messagePage.number} + 1) + ' 页 / 共 ' + ${messagePage.totalPages} + ' 页 (' + ${messagePage.totalElements} + ' 条记录)'">搜索结果</span>
                            <span th:unless="${isSearching}" 
                                  th:text="'第 ' + (${messagePage.number} + 1) + ' 页 / 共 ' + ${messagePage.totalPages} + ' 页 (' + ${messagePage.totalElements} + ' 条记录)'">页面信息</span>
                        </div>
                        
                        <!-- 页面跳转 -->
                        <div class="text-center" th:if="${messagePage.totalPages > 10}">
                            <form class="page-jump-form" th:action="@{/messages}" method="get">
                                <input type="hidden" name="size" th:value="${messagePage.size}">
                                <input type="hidden" name="keyword" th:value="${keyword}" th:if="${keyword != null and keyword != ''}">
                                <small class="text-muted">跳转到</small>
                                <input type="number" 
                                       name="jumpPage" 
                                       class="page-jump-input form-control form-control-sm"
                                       min="1" 
                                       th:max="${messagePage.totalPages}"
                                       th:placeholder="'1-' + ${messagePage.totalPages}"
                                       th:value="${messagePage.number + 1}">
                                <small class="text-muted">页</small>
                                <button type="submit" class="btn btn-sm btn-outline-primary">跳转</button>
                            </form>
                        </div>
                    </nav>
                </div>
    </div>

    <!-- 搜索功能增强脚本 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('searchInput');
            
            // 如果当前正在搜索，自动聚焦到搜索框
            if (searchInput && searchInput.value.trim() !== '') {
                searchInput.focus();
                // 将光标移动到末尾
                searchInput.setSelectionRange(searchInput.value.length, searchInput.value.length);
            }
            
            // 添加键盘快捷键支持 (Ctrl+F 或 Ctrl+K)
            document.addEventListener('keydown', function(e) {
                if ((e.ctrlKey || e.metaKey) && (e.key === 'f' || e.key === 'k')) {
                    e.preventDefault();
                    if (searchInput) {
                        searchInput.focus();
                        searchInput.select();
                    }
                }
            });
            
            // 搜索框的回车键支持
            if (searchInput) {
                searchInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        e.target.closest('form').submit();
                    }
                });
            }
            
            // 页面跳转验证
            const pageJumpForm = document.querySelector('.page-jump-form');
            if (pageJumpForm) {
                pageJumpForm.addEventListener('submit', function(e) {
                    const pageInput = this.querySelector('input[name="jumpPage"]');
                    const pageValue = parseInt(pageInput.value);
                    const maxPage = parseInt(pageInput.max);
                    
                    if (isNaN(pageValue) || pageValue < 1 || pageValue > maxPage) {
                        e.preventDefault();
                        alert('请输入有效的页码 (1-' + maxPage + ')');
                        pageInput.focus();
                        return false;
                    }
                    
                    // Controller会处理jumpPage参数的转换，这里不需要额外处理
                });
                
                // 页面跳转输入框的回车支持
                const pageInput = pageJumpForm.querySelector('input[name="jumpPage"]');
                if (pageInput) {
                    pageInput.addEventListener('keypress', function(e) {
                        if (e.key === 'Enter') {
                            e.preventDefault();
                            pageJumpForm.dispatchEvent(new Event('submit'));
                        }
                    });
                }
            }
        });
    </script>
</body>
</html>