package com.mylog.repository;

import com.mylog.model.Message;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface MessageRepository extends JpaRepository<Message, Long> {
    
    @Query(value = "SELECT * FROM Messages WHERE receiver = ?1 ORDER BY CreatedDate DESC", nativeQuery = true)
    List<Message> findByReceiverOrderByCreatedDateStrDesc(String receiver);
    
    @Query(value = "SELECT * FROM Messages WHERE receiver = ?1 ORDER BY CreatedDate DESC", 
           countQuery = "SELECT COUNT(*) FROM Messages WHERE receiver = ?1",
           nativeQuery = true)
    Page<Message> findByReceiverOrderByCreatedDateStrDesc(String receiver, Pageable pageable);
    
    @Query(value = "SELECT * FROM Messages WHERE receiver = ?1 AND isRead = ?2 ORDER BY CreatedDate DESC", nativeQuery = true)
    List<Message> findByReceiverAndIsReadOrderByCreatedDateStrDesc(String receiver, boolean isRead);

    @Query(value = "SELECT * FROM Messages WHERE receiver = ?1 AND isRead = ?2 ORDER BY CreatedDate DESC",
           countQuery = "SELECT COUNT(*) FROM Messages WHERE receiver = ?1 AND isRead = ?2",
           nativeQuery = true)
    Page<Message> findByReceiverAndIsReadOrderByCreatedDateStrDesc(String receiver, boolean isRead, Pageable pageable);
    
    @Query("SELECT COUNT(m) FROM Message m WHERE m.receiver = :receiver AND m.isRead = false")
    Long countUnreadMessagesByReceiver(@Param("receiver") String receiver);
    
    List<Message> findByRelatedTypeAndRelatedId(String relatedType, Long relatedId);
    
    @Query(value = "SELECT * FROM Messages WHERE receiver = ?1 AND (messageTitle LIKE CONCAT('%', ?2, '%') OR messageContent LIKE CONCAT('%', ?2, '%')) ORDER BY CreatedDate DESC", 
           countQuery = "SELECT COUNT(*) FROM Messages WHERE receiver = ?1 AND (messageTitle LIKE CONCAT('%', ?2, '%') OR messageContent LIKE CONCAT('%', ?2, '%'))",
           nativeQuery = true)
    Page<Message> findByReceiverAndContentContaining(String receiver, String keyword, Pageable pageable);
} 