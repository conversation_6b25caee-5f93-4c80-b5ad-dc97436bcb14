package com.mylog.controller.api;

import com.mylog.controller.BaseController;
import com.mylog.dto.SubmitRequest;
import com.mylog.model.ProjectTask;
import com.mylog.model.Project;
import com.mylog.model.SubTask;
import com.mylog.model.Submit2;
import com.mylog.service.ProjectService;
import com.mylog.service.SubTaskService;
import com.mylog.service.Submit2Service;
import com.mylog.service.TaskService;
import com.mylog.service.UserActivityLogService;
import com.mylog.service.UserService;
import com.mylog.util.SubmitFileUtils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 任务提交API控制器
 * 用于处理AJAX任务提交
 */
@RestController
@RequestMapping(value = "/api/submits", produces = MediaType.APPLICATION_JSON_VALUE)
public class SubmitApiController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(SubmitApiController.class);

    @Autowired
    private Submit2Service submit2Service;

    @Autowired
    private TaskService taskService;

    @Autowired
    private UserActivityLogService activityLogService;

    @Autowired
    private UserService userService;

    @Autowired
    private ProjectService projectService;

    @Autowired
    private SubTaskService subTaskService;

    @Autowired
    private com.mylog.util.WeixinMessageUtil weixinMessageUtil;

    @Value("${mylog.data.path:data}")
    private String dataPath;

    /**
     * 使用临时文件路径提交任务
     *
     * @param taskId        任务ID
     * @param remarks       备注
     * @param submitName    提交名称
     * @param tempFilePath1 临时文件1路径
     * @param tempFilePath2 临时文件2路径
     * @param fileName1     文件1名称
     * @param fileName2     文件2名称
     * @return 提交结果
     */
    @PostMapping("/save-with-temp")
    public ResponseEntity<Map<String, Object>> saveSubmitWithTemp(@RequestBody SubmitRequest request) {
        // 设置响应头，确保返回JSON格式
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        logger.info("接收到使用临时文件的任务提交请求: {}", request);

        // 输出请求对象的详细信息
        if (request != null) {
            logger.info("请求参数 - taskId: {}, submitName: {}, remarks: {}",
                    request.getTaskId(), request.getSubmitName(), request.getRemarks());
            logger.info("请求参数 - tempFilePath1: {}, fileName1: {}",
                    request.getTempFilePath1(), request.getFileName1());
            logger.info("请求参数 - tempFilePath2: {}, fileName2: {}",
                    request.getTempFilePath2(), request.getFileName2());
        } else {
            logger.warn("请求对象为空");
        }

        Map<String, Object> response = new HashMap<>();

        // 检查请求对象是否为空
        if (request == null) {
            response.put("success", Boolean.FALSE);
            response.put("message", "请求对象为空");
            return new ResponseEntity<>(response, headers, HttpStatus.OK);
        }

        // 获取请求参数
        Long taskId = request.getTaskId();
        String remarks = request.getRemarks();
        String submitName = request.getSubmitName();
        String tempFilePath1 = request.getTempFilePath1();
        String tempFilePath2 = request.getTempFilePath2();
        String tempFilePath3 = request.getTempFilePath3();
        String tempFilePath4 = request.getTempFilePath4();
        String fileName1 = request.getFileName1();
        String fileName2 = request.getFileName2();
        String fileName3 = request.getFileName3();
        String fileName4 = request.getFileName4();

        // 验证备注是否为空
        if (remarks == null || remarks.trim().isEmpty()) {
            response.put("success", Boolean.FALSE);
            response.put("message", "提交备注不能为空");
            return new ResponseEntity<>(response, headers, HttpStatus.OK);
        }

        if (taskId == null) {
            response.put("success", Boolean.FALSE);
            response.put("message", "缺少任务ID");
            return new ResponseEntity<>(response, headers, HttpStatus.OK);
        }

        // 获取当前用户
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String currentUsername = authentication.getName();

        // 检查任务是否存在
        Optional<ProjectTask> taskOpt = taskService.findTaskById(taskId);
        if (!taskOpt.isPresent()) {
            response.put("success", Boolean.FALSE);
            response.put("message", "无法找到指定任务");
            return new ResponseEntity<>(response, headers, HttpStatus.OK);
        }

        ProjectTask task = taskOpt.get();

        // 验证任务状态 - 已完成或已暂停的任务不能提交
        if ("已完成".equals(task.getStatus()) || "已暂停".equals(task.getStatus())) {
            response.put("success", Boolean.FALSE);
            response.put("message", "无法提交已完成或已暂停的任务");
            return new ResponseEntity<>(response, headers, HttpStatus.OK);
        }

        // 获取项目信息
        String projectCode = "unknown";
        String projectName = "未知项目";
        try {
            // 首先尝试从task中获取project
            Project project = task.getProject();
            if (project != null) {
                projectCode = project.getProjectCode();
                projectName = project.getProjectName();
            } else {
                // 如果延迟加载失败，则直接从数据库查询项目信息
                Long projectId = task.getProjectId();
                Optional<Project> projectOpt = projectService.findProjectById(projectId);
                if (projectOpt.isPresent()) {
                    projectCode = projectOpt.get().getProjectCode();
                    projectName = projectOpt.get().getProjectName();
                }
            }
        } catch (Exception e) {
            logger.warn("获取项目信息时出错: {}", e.getMessage());
            // 继续使用默认值
        }

        // 如果提交名称使用的是旧的默认值，则替换为任务名称
        if (submitName == null || "提交方案书和规格书".equals(submitName)) {
            submitName = task.getTaskName();
        }

        String taskName = task.getTaskName();

        Submit2 submit = new Submit2();
        submit.setTaskId(taskId);
        submit.setRemarks(remarks);
        submit.setSubmitName(submitName);
        submit.setShowAttachments(request.getShowAttachments() != null ? request.getShowAttachments() : "是");
        submit.setSubmitter(currentUsername);
        submit.setSubmitDateTime(LocalDateTime.now());

        // 根据任务名称确定提交目录
        String submitDir = SubmitFileUtils.determineSubmitDirectory(submitName);

        try {
            // 创建提交文件目录
            Path submitDirPath = Paths.get(dataPath, submitDir);
            if (!Files.exists(submitDirPath)) {
                Files.createDirectories(submitDirPath);
                logger.info("创建提交文件目录: {}", submitDirPath);
            }

            // 处理临时文件1
            if (tempFilePath1 != null && !tempFilePath1.isEmpty()) {
                try {
                    Path tempPath = Paths.get(tempFilePath1);
                    logger.info("尝试访问临时文件1路径: {}", tempPath.toAbsolutePath());

                    if (Files.exists(tempPath)) {
                        String fileExtension = "";
                        if (fileName1 != null && fileName1.contains(".")) {
                            fileExtension = fileName1.substring(fileName1.lastIndexOf("."));
                        }

                        // 根据格式创建新文件名
                        Path filePath = SubmitFileUtils.getSubmitFilePath(dataPath, submitDir,
                                projectCode, projectName, taskName,
                                1, fileExtension);
                        Files.copy(tempPath, filePath);
                        submit.setFilePath1(filePath.toString());
                        logger.info("从临时文件复制到提交文件1: {} -> {}", tempPath, filePath);

                        // 删除临时文件
                        try {
                            Files.deleteIfExists(tempPath);
                            logger.info("删除临时文件1: {}", tempPath);
                        } catch (IOException e) {
                            logger.warn("删除临时文件1失败: {}", e.getMessage());
                        }
                    } else {
                        logger.warn("临时文件1不存在: {}", tempPath);
                    }
                } catch (Exception e) {
                    logger.error("处理临时文件1时出错: {}", e.getMessage());
                }
            }

            // 处理临时文件2
            if (tempFilePath2 != null && !tempFilePath2.isEmpty()) {
                try {
                    Path tempPath = Paths.get(tempFilePath2);
                    logger.info("尝试访问临时文件2路径: {}", tempPath.toAbsolutePath());

                    if (Files.exists(tempPath)) {
                        String fileExtension = "";
                        if (fileName2 != null && fileName2.contains(".")) {
                            fileExtension = fileName2.substring(fileName2.lastIndexOf("."));
                        }

                        // 根据格式创建新文件名
                        Path filePath = SubmitFileUtils.getSubmitFilePath(dataPath, submitDir,
                                projectCode, projectName, taskName,
                                2, fileExtension);
                        Files.copy(tempPath, filePath);
                        submit.setFilePath2(filePath.toString());
                        logger.info("从临时文件复制到提交文件2: {} -> {}", tempPath, filePath);

                        // 删除临时文件
                        try {
                            Files.deleteIfExists(tempPath);
                            logger.info("删除临时文件2: {}", tempPath);
                        } catch (IOException e) {
                            logger.warn("删除临时文件2失败: {}", e.getMessage());
                        }
                    } else {
                        logger.warn("临时文件2不存在: {}", tempPath);
                    }
                } catch (Exception e) {
                    logger.error("处理临时文件2时出错: {}", e.getMessage());
                }
            }

            // 处理临时文件3
            if (tempFilePath3 != null && !tempFilePath3.isEmpty()) {
                try {
                    Path tempPath = Paths.get(tempFilePath3);
                    logger.info("尝试访问临时文件3路径: {}", tempPath.toAbsolutePath());

                    if (Files.exists(tempPath)) {
                        String fileExtension = "";
                        if (fileName3 != null && fileName3.contains(".")) {
                            fileExtension = fileName3.substring(fileName3.lastIndexOf("."));
                        }

                        // 根据格式创建新文件名
                        Path filePath = SubmitFileUtils.getSubmitFilePath(dataPath, submitDir,
                                projectCode, projectName, taskName,
                                3, fileExtension);
                        Files.copy(tempPath, filePath);
                        submit.setFilePath3(filePath.toString());
                        logger.info("从临时文件复制到提交文件3: {} -> {}", tempPath, filePath);

                        // 删除临时文件
                        try {
                            Files.deleteIfExists(tempPath);
                            logger.info("删除临时文件3: {}", tempPath);
                        } catch (IOException e) {
                            logger.warn("删除临时文件3失败: {}", e.getMessage());
                        }
                    } else {
                        logger.warn("临时文件3不存在: {}", tempPath);
                    }
                } catch (Exception e) {
                    logger.error("处理临时文件3时出错: {}", e.getMessage());
                }
            }

            // 处理临时文件4
            if (tempFilePath4 != null && !tempFilePath4.isEmpty()) {
                try {
                    Path tempPath = Paths.get(tempFilePath4);
                    logger.info("尝试访问临时文件4路径: {}", tempPath.toAbsolutePath());

                    if (Files.exists(tempPath)) {
                        String fileExtension = "";
                        if (fileName4 != null && fileName4.contains(".")) {
                            fileExtension = fileName4.substring(fileName4.lastIndexOf("."));
                        }

                        // 根据格式创建新文件名
                        Path filePath = SubmitFileUtils.getSubmitFilePath(dataPath, submitDir,
                                projectCode, projectName, taskName,
                                4, fileExtension);
                        Files.copy(tempPath, filePath);
                        submit.setFilePath4(filePath.toString());
                        logger.info("从临时文件复制到提交文件4: {} -> {}", tempPath, filePath);

                        // 删除临时文件
                        try {
                            Files.deleteIfExists(tempPath);
                            logger.info("删除临时文件4: {}", tempPath);
                        } catch (IOException e) {
                            logger.warn("删除临时文件4失败: {}", e.getMessage());
                        }
                    } else {
                        logger.warn("临时文件4不存在: {}", tempPath);
                    }
                } catch (Exception e) {
                    logger.error("处理临时文件4时出错: {}", e.getMessage());
                }
            }

            // 根据选择的状态完成或暂停任务
            String taskCompletionStatus = request.getTaskCompletionStatus();

            // 根据任务完成状态设置不同的备注前缀
            String remarkPrefix = "paused".equals(taskCompletionStatus) ? "提交[暂停]，备注：" : "提交[完成]，备注：";
            String ExtRemarks = remarks;

            ExtRemarks = remarkPrefix + remarks;

            submit.setRemarks(ExtRemarks);

            // 保存提交记录
            Submit2 savedSubmit = submit2Service.saveSubmit(submit);
            logger.info("成功保存提交记录: {}", savedSubmit.getSubmitId());

            // 检查是否需要发送微信群通知
            boolean hasAttachment = hasAnyAttachment(savedSubmit);
            boolean isReleaseProject = projectName.contains("发布");
            
            logger.info("微信通知检查 - 项目名称: {}, 是否包含'发布': {}", projectName, isReleaseProject);
            logger.info("微信通知检查 - 有附件: {}", hasAttachment);
            
            if (hasAttachment && isReleaseProject) {
                logger.info("满足发送微信通知条件，开始发送通知...");
                sendWeixinNotificationForReleaseSubmit(task, projectName, currentUsername, savedSubmit);
            } else {
                logger.info("不满足发送微信通知条件 - 有附件: {}, 发布项目: {}", hasAttachment, isReleaseProject);
            }

            // 如果有备注，自动创建一条评论
            if (remarks != null && !remarks.trim().isEmpty()) {
                SubTask commentTask = new SubTask();
                commentTask.setTaskId(taskId);

                commentTask.setLogContent(ExtRemarks);

                commentTask.setCreatedDateTime(LocalDateTime.now());
                commentTask.setCreatedBy(currentUsername);

                // 保存评论
                subTaskService.saveSubTask(commentTask);
                logger.info("已添加提交备注作为评论");
            }

            // 记录活动
            String ipAddress = getClientIpAddress();

            // 获取当前用户ID
            Long userId = null;
            try {
                com.mylog.model.user.User user = userService.findUserByUsername(currentUsername).orElse(null);
                if (user != null) {
                    userId = user.getUserId();
                }
            } catch (Exception e) {
                logger.error("获取用户ID时出错: {}", e.getMessage());
            }

            activityLogService.logCreate(
                    userId,
                    currentUsername,
                    "为任务 " + task.getTaskName() + " 添加了提交记录",
                    ipAddress,
                    "Task",
                    task.getTaskId(),
                    getAccessType());

            // 处理审批流程选项
            Boolean needApproval = request.getNeedApproval();
            if (needApproval != null && needApproval) {
                // 更新任务审批状态为"审批中"
                // taskService.updateTaskApprovalStatus(taskId, 1, null);

                // 返回需要跳转到审批流程页面的标志
                response.put("success", Boolean.TRUE);
                response.put("message", "提交记录已保存，正在创建并跳转到审批流程页面");
                response.put("taskId", task.getTaskId());
                response.put("needApproval", Boolean.TRUE);
                response.put("taskCompletionStatus", taskCompletionStatus);
                // 对任务名称进行URL编码，避免特殊字符导致问题
                String encodedTaskName;
                try {
                    encodedTaskName = java.net.URLEncoder
                            .encode(task.getTaskName() + "/" + task.getProject().getProjectName(), "UTF-8");
                } catch (Exception e) {
                    logger.warn("任务名称URL编码失败: {}", e.getMessage());
                    encodedTaskName = task.getTaskName(); // 如果编码失败，使用原始名称
                }

                // 对备注进行URL编码
                String encodedRemarks = "";
                if (ExtRemarks != null && !ExtRemarks.trim().isEmpty()) {
                    try {
                        encodedRemarks = java.net.URLEncoder.encode(ExtRemarks, "UTF-8");
                    } catch (Exception e) {
                        logger.warn("备注URL编码失败: {}", e.getMessage());
                        encodedRemarks = ""; // 如果编码失败，使用空字符串
                    }
                }

                // 直接跳转到自动创建流程并提交的端点，包含备注参数
                String redirectUrl = "/workflow/instances/auto-create-for-task?businessId=" + taskId + "&title="
                        + encodedTaskName;
                if (!encodedRemarks.isEmpty()) {
                    redirectUrl += "&remarks=" + encodedRemarks;
                }
                // 添加任务完成状态参数
                if (taskCompletionStatus != null && !taskCompletionStatus.trim().isEmpty()) {
                    try {
                        String encodedTaskCompletionStatus = java.net.URLEncoder.encode(taskCompletionStatus, "UTF-8");
                        redirectUrl += "&taskCompletionStatus=" + encodedTaskCompletionStatus;
                    } catch (Exception e) {
                        logger.warn("任务完成状态URL编码失败: {}", e.getMessage());
                    }
                }
                response.put("redirectUrl", redirectUrl);
            } else {
                // 不需要审批时，直接更新任务状态
                updateTaskStatusByCompletionStatus(taskId, taskCompletionStatus);

                response.put("success", Boolean.TRUE);
                response.put("message", "提交记录已保存");
                response.put("taskId", task.getTaskId());
                response.put("needApproval", Boolean.FALSE);
            }

            return new ResponseEntity<>(response, headers, HttpStatus.OK);

        } catch (IOException e) {
            logger.error("保存提交文件时出错", e);

            response.put("success", Boolean.FALSE);
            response.put("message", "提交记录保存失败: " + e.getMessage());

            return new ResponseEntity<>(response, headers, HttpStatus.OK);
        }
    }

    /**
     * 根据完成状态更新任务状态
     *
     * @param taskId 任务ID
     * @param taskCompletionStatus 任务完成状态 ("paused" 表示暂停，其他表示完成)
     */
    private void updateTaskStatusByCompletionStatus(Long taskId, String taskCompletionStatus) {
        try {
            if ("paused".equals(taskCompletionStatus)) {
                // 暂停任务
                taskService.pauseTask(taskId);
                logger.info("已将任务 {} 标记为已暂停", taskId);
            } else {
                // 默认完成任务（向后兼容）
                taskService.completeTask(taskId);
                logger.info("已将任务 {} 标记为已完成", taskId);
            }
        } catch (Exception e) {
            logger.error("更新任务状态时出错: {}", e.getMessage());
            // 提交记录已保存，即使任务状态更新失败也继续执行
        }
    }

    /**
     * 检查提交记录是否有任何附件
     */
    private boolean hasAnyAttachment(Submit2 submit) {
        return (submit.getFilePath1() != null && !submit.getFilePath1().isEmpty()) ||
               (submit.getFilePath2() != null && !submit.getFilePath2().isEmpty()) ||
               (submit.getFilePath3() != null && !submit.getFilePath3().isEmpty()) ||
               (submit.getFilePath4() != null && !submit.getFilePath4().isEmpty());
    }

    /**
     * 发送发布项目提交附件的微信群通知
     */
    private void sendWeixinNotificationForReleaseSubmit(ProjectTask task, String projectName, String submitter, Submit2 submit) {
        try {
            // 构建通知消息
            String title = "📋 发布项目提交通知";
            StringBuilder content = new StringBuilder();
               content.append("<@all>\n");
            content.append("**项目名称**: ").append(projectName).append("\n");
            content.append("**任务名称**: ").append(task.getTaskName()).append("\n");
            content.append("**提交人**: ").append(submitter).append("\n");
            content.append("**提交时间**: ").append(submit.getSubmitDateTime().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\n\n");
            
            // 添加附件信息
            content.append("**附件信息**:\n");
            int attachmentCount = 0;
            if (submit.getFilePath1() != null && !submit.getFilePath1().isEmpty()) {
                attachmentCount++;
                content.append("- 附件1: ").append(extractFileName(submit.getFilePath1())).append("\n");
            }
            if (submit.getFilePath2() != null && !submit.getFilePath2().isEmpty()) {
                attachmentCount++;
                content.append("- 附件2: ").append(extractFileName(submit.getFilePath2())).append("\n");
            }
            if (submit.getFilePath3() != null && !submit.getFilePath3().isEmpty()) {
                attachmentCount++;
                content.append("- 附件3: ").append(extractFileName(submit.getFilePath3())).append("\n");
            }
            if (submit.getFilePath4() != null && !submit.getFilePath4().isEmpty()) {
                attachmentCount++;
                content.append("- 附件4: ").append(extractFileName(submit.getFilePath4())).append("\n");
            }
            
            content.append("\n共 ").append(attachmentCount).append(" 个附件\n\n");
            
            if (submit.getRemarks() != null && !submit.getRemarks().trim().isEmpty()) {
                content.append("**备注**: ").append(submit.getRemarks()).append("\n\n");
            }
            
            content.append("请相关人员及时查看和处理。");
            
            // 发送微信群通知，@所有人
            java.util.List<String> mentionList = new java.util.ArrayList<>();
            mentionList.add("@all"); // @所有人
            
            weixinMessageUtil.sendWeixinMessage(title, content.toString(), mentionList);
            
            logger.info("已发送发布项目提交的微信群通知: 项目={}, 任务={}, 提交人={}", 
                       projectName, task.getTaskName(), submitter);
            
        } catch (Exception e) {
            logger.error("发送发布项目提交的微信群通知失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 从文件路径中提取文件名
     */
    private String extractFileName(String filePath) {
        if (filePath == null || filePath.isEmpty()) {
            return "未知文件";
        }
        try {
            java.nio.file.Path path = java.nio.file.Paths.get(filePath);
            return path.getFileName().toString();
        } catch (Exception e) {
            logger.warn("提取文件名失败: {}", filePath);
            return "文件名获取失败";
        }
    }
}
