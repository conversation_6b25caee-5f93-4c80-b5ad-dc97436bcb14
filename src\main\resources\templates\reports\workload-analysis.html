<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head th:replace="~{fragments/layout :: head('人员工作量报表')}">
    <meta charset="UTF-8">
    <title>人员工作量报表</title>
    <!-- 引入Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body th:replace="~{fragments/layout :: body(~{::div.content-wrapper}, ~{::script})}">
    <div class="content-wrapper">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <div class="d-flex align-items-center">
                        <h1 class="h2 mb-0">人员工作量报表</h1>
                        <span style="font-size: 1.25rem; color: #6c757d; margin-left: 1rem;">按任务创建时间统计</span>
                    </div>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportToPDF()">导出PDF</button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportToExcel()">导出Excel</button>
                        </div>
                        <a th:href="@{/reports}" class="btn btn-sm btn-outline-primary">
                            <i class="bi bi-arrow-left"></i> 返回
                        </a>
                    </div>
                </div>

                <!-- 筛选条件 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">筛选条件</h5>
                    </div>
                    <div class="card-body">
                        <form th:action="@{/reports/workload-analysis}" method="get" class="row g-3">
                            <div class="col-md-4">
                                <label for="startDate" class="form-label">开始日期</label>
                                <input type="date" class="form-control" id="startDate" name="startDate" th:value="${startDate}">
                            </div>
                            <div class="col-md-4">
                                <label for="endDate" class="form-label">结束日期</label>
                                <input type="date" class="form-control" id="endDate" name="endDate" th:value="${endDate}">
                            </div>
                            <div class="col-md-4 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary w-100">应用筛选</button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 图表展示区域 -->
                <div class="row">
                    <!-- 各负责人任务数量统计 -->
                    <div class="col-md-6 mb-4">
                        <div class="card h-100">
                            <div class="card-header">
                                <h5 class="mb-0">各负责人任务数量统计</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="taskCountChart"></canvas>
                            </div>
                        </div>
                    </div>

                    <!-- 人员效率对比 -->
                    <div class="col-md-6 mb-4">
                        <div class="card h-100">
                            <div class="card-header">
                                <h5 class="mb-0">人员效率对比</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="efficiencyChart"></canvas>
                            </div>
                        </div>
                    </div>

                    <!-- 各负责人工作量分析 -->
                    <div class="col-md-12 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">各负责人工作量分析</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="workloadChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 工作量分析表格 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">工作量分析表格</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>负责人</th>
                                        <th>总任务数</th>
                                        <th>进行中</th>
                                        <th>已完成</th>
                                        <th>已暂停</th>
                                        <th>完成率</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:each="person : ${personnelList}">
                                        <td th:text="${person}">负责人</td>
                                        <td th:with="workloadData = ${responsibleWorkloadData.get(person)},
                                                   notStarted = ${workloadData != null ? (workloadData.get('未开始') ?: 0) : 0},
                                                   inProgress = ${workloadData != null ? (workloadData.get('进行中') ?: 0) : 0},
                                                   completed = ${workloadData != null ? (workloadData.get('已完成') ?: 0) : 0},
                                                   cancelled = ${workloadData != null ? (workloadData.get('已暂停') ?: 0) : 0},
                                                   total = ${notStarted + inProgress + completed + cancelled}"
                                            th:text="${total}">总任务数</td>
                                        <td th:with="workloadData = ${responsibleWorkloadData.get(person)}"
                                            th:text="${workloadData != null ? (workloadData.get('进行中') ?: 0) : 0}">进行中</td>
                                        <td th:with="workloadData = ${responsibleWorkloadData.get(person)}"
                                            th:text="${workloadData != null ? (workloadData.get('已完成') ?: 0) : 0}">已完成</td>
                                        <td th:with="workloadData = ${responsibleWorkloadData.get(person)}"
                                            th:text="${workloadData != null ? (workloadData.get('已暂停') ?: 0) : 0}">已暂停</td>
                                        <td th:with="efficiency = ${responsibleEfficiencyData.get(person) ?: 0}"
                                            th:text="${#numbers.formatPercent(efficiency, 0, 0)}">完成率</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 工作量优化建议 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">工作量优化建议</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="bi bi-lightbulb me-2"></i>工作量优化建议</h6>
                            <ul>
                                <li>根据人员效率和当前工作量，合理分配新任务</li>
                                <li>对于工作量过大的人员，考虑适当减轻负担或提供支持</li>
                                <li>对于效率较低的人员，提供必要的培训和指导</li>
                                <li>定期评估工作量分配情况，确保团队整体效率最大化</li>
                            </ul>
                        </div>
                    </div>
                </div>
    </div>
    <script th:inline="javascript">
        // 从后端获取数据
        const responsibleTaskCountData = /*[[${responsibleTaskCountData}]]*/ {};
        const responsibleWorkloadData = /*[[${responsibleWorkloadData}]]*/ {};
        const responsibleEfficiencyData = /*[[${responsibleEfficiencyData}]]*/ {};
        const personnelList = /*[[${personnelList}]]*/ [];

        // 为所有人员准备数据（包括没有任务的人员）
        function preparePersonnelData(dataMap, defaultValue) {
            const result = {};
            personnelList.forEach(person => {
                result[person] = dataMap[person] || defaultValue;
            });
            return result;
        }

        // 准备图表数据
        const allPersonnelTaskCounts = preparePersonnelData(responsibleTaskCountData, 0);
        const allPersonnelEfficiency = preparePersonnelData(responsibleEfficiencyData, 0);

        // 各负责人任务数量统计图表
        const taskCountCtx = document.getElementById('taskCountChart').getContext('2d');
        new Chart(taskCountCtx, {
            type: 'bar',
            data: {
                labels: personnelList,
                datasets: [{
                    label: '任务数量',
                    data: personnelList.map(person => allPersonnelTaskCounts[person]),
                    backgroundColor: '#0d6efd'
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: '各负责人任务数量'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                }
            }
        });

        // 人员效率对比图表
        const efficiencyCtx = document.getElementById('efficiencyChart').getContext('2d');

        // 按完成率从高到低排序的处理
        const sortedByEfficiency = [...personnelList].sort((a, b) => {
            const efficiencyA = allPersonnelEfficiency[a] || 0;
            const efficiencyB = allPersonnelEfficiency[b] || 0;
            return efficiencyB - efficiencyA; // 从高到低排序
        });

        new Chart(efficiencyCtx, {
            type: 'bar',
            data: {
                labels: sortedByEfficiency,
                datasets: [{
                    label: '完成率',
                    data: sortedByEfficiency.map(person => allPersonnelEfficiency[person] * 100),
                    backgroundColor: '#20c997'
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: '人员效率对比 (%)'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.parsed.y.toFixed(1) + '%';
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    }
                }
            }
        });

        // 各负责人工作量分析图表
        const workloadCtx = document.getElementById('workloadChart').getContext('2d');

        // 处理工作量数据
        const statusTypes = [ '进行中', '已完成', '已暂停'];
        const workloadDatasets = statusTypes.map((status, index) => {
            const colors = [ '#0d6efd', '#198754', '#dc3545'];
            return {
                label: status,
                data: personnelList.map(person => {
                    const statusData = responsibleWorkloadData[person] || {};
                    return statusData[status] || 0;
                }),
                backgroundColor: colors[index]
            };
        });

        new Chart(workloadCtx, {
            type: 'bar',
            data: {
                labels: personnelList,
                datasets: workloadDatasets
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: true,
                        text: '各负责人工作量分析'
                    }
                },
                scales: {
                    x: {
                        stacked: true,
                    },
                    y: {
                        stacked: true,
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                }
            }
        });

        // 导出PDF函数
        function exportToPDF() {
            alert('导出PDF功能将在后续实现');
        }

        // 导出Excel函数
        function exportToExcel() {
            alert('导出Excel功能将在后续实现');
        }
    </script>
</body>
</html>