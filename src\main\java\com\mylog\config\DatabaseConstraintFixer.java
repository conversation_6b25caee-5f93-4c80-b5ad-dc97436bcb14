package com.mylog.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.Statement;

/**
 * 数据库约束修复器
 * 在应用启动时自动修复用户活动日志表的活动类型约束
 */
@Component
@Order(1) // 确保在其他组件之前运行
public class DatabaseConstraintFixer implements CommandLineRunner {
    
    @Autowired
    private DataSource dataSource;
    
    @Override
    public void run(String... args) throws Exception {
        fixActivityTypeConstraintIfNeeded();
    }
    
    private void fixActivityTypeConstraintIfNeeded() {
        try (Connection conn = dataSource.getConnection()) {
            
            // 测试是否需要修复约束
            if (needsConstraintFix(conn)) {
                System.out.println("检测到用户活动日志表需要修复约束，开始修复...");
                fixActivityTypeConstraint(conn);
                System.out.println("用户活动日志表约束修复完成！");
            } else {
                System.out.println("用户活动日志表约束检查通过，无需修复。");
            }
            
        } catch (Exception e) {
            System.err.println("数据库约束检查/修复过程中发生错误: " + e.getMessage());
            // 不抛出异常，避免影响应用启动
        }
    }
    
    private boolean needsConstraintFix(Connection conn) {
        try (Statement stmt = conn.createStatement()) {
            // 尝试插入一个DOWNLOAD类型的测试记录
            stmt.execute("INSERT INTO user_activity_logs (userId, username, activityType, description, createdDate) " +
                        "VALUES (-999, 'constraint_test', 'DOWNLOAD', '约束测试记录', datetime('now', 'localtime'))");
            
            // 如果成功插入，删除测试记录并返回false（不需要修复）
            stmt.execute("DELETE FROM user_activity_logs WHERE userId = -999 AND username = 'constraint_test'");
            return false;
            
        } catch (Exception e) {
            // 如果插入失败，说明需要修复约束
            return e.getMessage().contains("CHECK constraint failed");
        }
    }
    
    private void fixActivityTypeConstraint(Connection conn) throws Exception {
        Statement stmt = conn.createStatement();
        
        // 禁用外键约束
        stmt.execute("PRAGMA foreign_keys=off");
        
        // 开始事务
        stmt.execute("BEGIN TRANSACTION");
        
        try {
            // 创建临时表，具有正确的CHECK约束
            String createTempTable = """
                CREATE TABLE user_activity_logs_temp (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    userId INTEGER NOT NULL,
                    username TEXT NOT NULL,
                    activityType TEXT NOT NULL CHECK (activityType IN ('LOGIN', 'LOGOUT', 'CREATE', 'UPDATE', 'DELETE', 'VIEW', 'DOWNLOAD', 'EXPORT', 'SETTINGS_CHANGE', 'OTHER')),
                    description TEXT NOT NULL,
                    ipAddress TEXT,
                    entityType TEXT,
                    entityId INTEGER,
                    accessType TEXT,
                    createdDate TEXT NOT NULL
                )
                """;
            stmt.execute(createTempTable);
            
            // 将原表数据复制到临时表
            String copyData = """
                INSERT INTO user_activity_logs_temp (id, userId, username, activityType, description, ipAddress, entityType, entityId, accessType, createdDate)
                SELECT id, userId, username, activityType, description, ipAddress, entityType, entityId, accessType, createdDate
                FROM user_activity_logs
                """;
            stmt.execute(copyData);
            
            // 删除原表
            stmt.execute("DROP TABLE user_activity_logs");
            
            // 重命名临时表为原表名
            stmt.execute("ALTER TABLE user_activity_logs_temp RENAME TO user_activity_logs");
            
            // 提交事务
            stmt.execute("COMMIT");
            
        } catch (Exception e) {
            // 回滚事务
            stmt.execute("ROLLBACK");
            throw e;
        } finally {
            // 重新启用外键约束
            stmt.execute("PRAGMA foreign_keys=on");
            stmt.close();
        }
    }
}
