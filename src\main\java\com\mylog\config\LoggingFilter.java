package com.mylog.config;

import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.filter.Filter;
import ch.qos.logback.core.spi.FilterReply;

import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 自定义日志过滤器，用于过滤不必要的调试信息
 */
public class LoggingFilter extends Filter<ILoggingEvent> {

    // 需要排除的包或类的列表
    private static final List<String> EXCLUDED_LOGGERS = Arrays.asList(
            "org.apache.catalina",
            "org.apache.coyote",
            "org.apache.tomcat",
            "org.hibernate.validator",
            // "org.hibernate.engine", // Keep this if specific engine logs are noisy but not SQL
            "org.hibernate.SQL",    // Explicitly target SQL logger
            "org.hibernate.type",   // Hibernate type logging
            "org.hibernate.orm.jdbc", // More specific Hibernate JDBC logging
            "org.hibernate",        // General Hibernate, if too broad, can be refined
            "org.springframework.boot.autoconfigure",
            "org.springframework.context.support",
            "org.springframework.jdbc.datasource"
    );

    // 需要排除的包含特定文本的消息列表
    private static final List<String> EXCLUDED_MESSAGES = Arrays.asList(
            "HikariPool",
            "connection established",
            "connection pool",
            "Initializing Spring",
            "Started Application",
            "Hibernate:", // Catch messages starting with "Hibernate:"
            "binding parameter" // Catch parameter binding logs if they are separate
    );
    
    // SQL语句正则表达式模式
    private static final Pattern SQL_PATTERN = Pattern.compile(
            // More precise regex to catch typical SQL statements, case-insensitive
            "^\\\\s*(select|insert|update|delete|create|alter|drop|truncate|call|exec|set|declare|with)\\\\b.*",
            Pattern.CASE_INSENSITIVE | Pattern.DOTALL
    );

    @Override
    public FilterReply decide(ILoggingEvent event) {
        String loggerName = event.getLoggerName();
        String formattedMessage = event.getFormattedMessage();

        // 检查日志来源是否在排除列表中
        for (String excludedLogger : EXCLUDED_LOGGERS) {
            if (loggerName != null && loggerName.startsWith(excludedLogger)) {
                // Specifically for Hibernate SQL or type loggers, deny if message also indicates SQL
                if (loggerName.startsWith("org.hibernate.SQL") || loggerName.startsWith("org.hibernate.type")) {
                    if (formattedMessage != null && (formattedMessage.toLowerCase().contains("select ") || SQL_PATTERN.matcher(formattedMessage).matches())) {
                        return FilterReply.DENY;
                    }
                } else if (loggerName.startsWith("org.hibernate")) { // General Hibernate logger
                     if (formattedMessage != null && (formattedMessage.startsWith("Hibernate: select") || SQL_PATTERN.matcher(formattedMessage).matches())) {
                        return FilterReply.DENY;
                     }
                }
                // For other excluded loggers, deny directly if they are too verbose
                // return FilterReply.DENY; // Re-evaluate if this is too broad for non-SQL hibernate loggers
            }
        }

        if (formattedMessage == null) {
            return FilterReply.NEUTRAL; // No message to evaluate
        }
        
        // 检查是否为明确的SQL语句模式
        if (formattedMessage.startsWith("Hibernate: select") || // Common Hibernate SQL prefix
            SQL_PATTERN.matcher(formattedMessage).matches() ||
            (loggerName != null && loggerName.equals("org.hibernate.SQL") && !formattedMessage.contains("binding parameter"))) { // Deny org.hibernate.SQL unless it's just params
            return FilterReply.DENY; 
        }

        // 检查日志消息是否包含需要排除的特定文本
        for (String excludedMessage : EXCLUDED_MESSAGES) {
            if (formattedMessage.contains(excludedMessage)) {
                 // If it's a Hibernate message, deny it.
                if (excludedMessage.equals("Hibernate:") || excludedMessage.equals("binding parameter")) {
                    return FilterReply.DENY;
                }
                // For other general excluded messages, also deny.
                // return FilterReply.DENY; // This might be too aggressive for non-SQL messages.
            }
        }

        // 如果此过滤器不拒绝，则传递给下一个过滤器或配置
        return FilterReply.NEUTRAL;
    }
}