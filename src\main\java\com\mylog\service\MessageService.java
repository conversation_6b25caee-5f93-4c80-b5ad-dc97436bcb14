package com.mylog.service;

import com.mylog.model.Message;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;

public interface MessageService {
    
    List<Message> findAllMessages();
    
    Optional<Message> findMessageById(Long id);
    
    List<Message> findMessagesByReceiver(String receiver);
    
    Page<Message> findMessagesByReceiver(String receiver, Pageable pageable);
    
    List<Message> findUnreadMessagesByReceiver(String receiver);
    Page<Message> findUnreadMessagesByReceiver(String receiver, Pageable pageable);
    
    Long countUnreadMessagesByReceiver(String receiver);
    
    Message saveMessage(Message message);
    
    void deleteMessage(Long id);
    
    void markAsRead(Long id);
    
    void markAllAsRead(String receiver);
    
    List<Message> findMessagesByRelatedTypeAndId(String relatedType, Long relatedId);
    
    /**
     * 统计消息总数
     * @return 消息总数
     */
    Long countTotalMessages();
    
    /**
     * 按内容模糊搜索消息
     * @param receiver 接收者
     * @param keyword 搜索关键词
     * @param pageable 分页参数
     * @return 匹配的消息页面
     */
    Page<Message> searchMessagesByContent(String receiver, String keyword, Pageable pageable);
} 