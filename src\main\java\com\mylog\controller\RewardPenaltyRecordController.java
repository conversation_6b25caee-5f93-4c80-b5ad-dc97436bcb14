package com.mylog.controller;

import com.mylog.model.RewardPenaltyRecord;
import com.mylog.repository.RewardPenaltyRecordRepository;
import com.mylog.service.RewardPenaltyRecordService;
import com.mylog.service.UserActivityLogService;
import com.mylog.service.UserService;
import com.mylog.service.OptionsService;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.security.Principal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpSession;

/**
 * 奖罚记录控制器
 * 处理积分管理相关的请求
 */
@Controller
public class RewardPenaltyRecordController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(RewardPenaltyRecordController.class);
    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");    @Autowired
    private RewardPenaltyRecordService rewardPenaltyRecordService;
    
    @Autowired
    private RewardPenaltyRecordRepository rewardPenaltyRecordRepository;
      @Autowired
    private UserActivityLogService activityLogService;
    
    @Autowired
    private UserService userService;    

    @Autowired
    private OptionsService optionsService;
    /**
     * 显示当前用户的积分记录（我的积分）
     */
    @GetMapping("/my-points")
    public String showMyPoints(Model model, Principal principal, HttpServletRequest request) {
        try {
            String currentUsername = principal.getName();
            // 获取当前用户的所有奖罚记录，并按创建时间降序排序
            List<RewardPenaltyRecord> records = rewardPenaltyRecordService.findByName(currentUsername);
            // 计算当前用户的总积分
            Integer totalPoints = rewardPenaltyRecordService.getLatestTotalPoints(currentUsername);
            
            model.addAttribute("records", records);
            model.addAttribute("totalPoints", totalPoints);
            model.addAttribute("currentUsername", currentUsername);
            model.addAttribute("activeMenu", "my-points");
            
            // 清除session中的徽章计数，因为用户已经查看了积分页面
            HttpSession session = request.getSession();
            session.removeAttribute("recentRewardPenaltyCount");
            
            logger.info("用户 {} 查看自己的积分记录，共 {} 条记录，总积分：{}", 
                    currentUsername, records.size(), totalPoints);

            // 记录用户活动日志
            userService.findUserByUsername(currentUsername).ifPresent(user -> {
                activityLogService.logView(
                    user.getUserId(),
                    currentUsername,
                    "查看自己的积分记录",
                    getClientIpAddress(),
                    "RewardPenalty",
                    null,
                    getAccessType()
                );
            });


            
            return "reward-penalty/my-points";
        } catch (Exception e) {
            logger.error("显示用户积分记录时出错", e);
            model.addAttribute("errorMessage", "加载积分记录失败: " + e.getMessage());
            return "reward-penalty/my-points";
        }
    }    /**
     * 显示所有积分记录（积分管理）- 仅管理员可访问
     */
    @GetMapping("/points-management")
    @PreAuthorize("hasRole('ADMIN')")
    public String showPointsManagement(
            Model model, 
            Principal principal,
            @RequestParam(name = "name", required = false) String name,
            @RequestParam(name = "type", required = false) String type,
            @RequestParam(name = "startDate", required = false) String startDate,
            @RequestParam(name = "endDate", required = false) String endDate,
            @RequestParam(name = "page", defaultValue = "1") int page,
            @RequestParam(name = "size", defaultValue = "20") int size) {
        try {
            String currentUsername = principal.getName();
            
            // 创建分页对象
            org.springframework.data.domain.Pageable pageable = 
                org.springframework.data.domain.PageRequest.of(page - 1, size, 
                    org.springframework.data.domain.Sort.by("id").descending());
            
            // 根据查询条件获取奖罚记录
            org.springframework.data.domain.Page<RewardPenaltyRecord> recordsPage;
            if (name != null && !name.trim().isEmpty() && (startDate != null && !startDate.trim().isEmpty() || endDate != null && !endDate.trim().isEmpty())) {
                // 按姓名(模糊匹配)和时间范围查询
                String startDateTime = (startDate != null && !startDate.trim().isEmpty()) ? startDate + " 00:00:00" : "1900-01-01 00:00:00";
                String endDateTime = (endDate != null && !endDate.trim().isEmpty()) ? endDate + " 23:59:59" : "2999-12-31 23:59:59";
                if (type != null && !type.trim().isEmpty()) {
                    // 按姓名+类型(模糊)+时间范围
                    recordsPage = rewardPenaltyRecordRepository.findByNameContainingAndTypeContainingAndOccurTimeBetween(name, type, startDateTime, endDateTime, pageable);
                } else {
                    recordsPage = rewardPenaltyRecordRepository.findByNameContainingAndOccurTimeBetween(name, startDateTime, endDateTime, pageable);
                }
                logger.info("管理员 {} 按姓名(模糊匹配)、类型(模糊)和发生时间范围查询积分记录：姓名={}, 类型={}, 发生时间范围={} 至 {}", currentUsername, name, type, startDateTime, endDateTime);
            } else if (name != null && !name.trim().isEmpty()) {
                // 仅按姓名查询(模糊匹配)
                if (type != null && !type.trim().isEmpty()) {
                    recordsPage = rewardPenaltyRecordRepository.findByNameContainingAndTypeContaining(name, type, pageable);
                } else {
                    recordsPage = rewardPenaltyRecordRepository.findByNameContaining(name, pageable);
                }
                logger.info("管理员 {} 按姓名(模糊匹配)和类型(模糊)查询积分记录：姓名={}, 类型={}", currentUsername, name, type);
            } else if (type != null && !type.trim().isEmpty() && (startDate != null && !startDate.trim().isEmpty() || endDate != null && !endDate.trim().isEmpty())) {
                // 仅按类型(模糊)和时间范围
                String startDateTime = (startDate != null && !startDate.trim().isEmpty()) ? startDate + " 00:00:00" : "1900-01-01 00:00:00";
                String endDateTime = (endDate != null && !endDate.trim().isEmpty()) ? endDate + " 23:59:59" : "2999-12-31 23:59:59";
                recordsPage = rewardPenaltyRecordRepository.findByTypeContainingAndOccurTimeBetween(type, startDateTime, endDateTime, pageable);
                logger.info("管理员 {} 按类型(模糊)和发生时间范围查询积分记录：类型={}, 发生时间范围={} 至 {}", currentUsername, type, startDateTime, endDateTime);
            } else if (type != null && !type.trim().isEmpty()) {
                // 仅按类型(模糊)
                recordsPage = rewardPenaltyRecordRepository.findByTypeContaining(type, pageable);
                logger.info("管理员 {} 按类型(模糊)查询积分记录：类型={}", currentUsername, type);
            } else if (startDate != null && !startDate.trim().isEmpty() || endDate != null && !endDate.trim().isEmpty()) {
                // 仅按发生时间范围查询（允许开始时间或结束时间为空）
                String startDateTime = (startDate != null && !startDate.trim().isEmpty()) ? startDate + " 00:00:00" : "1900-01-01 00:00:00";
                String endDateTime = (endDate != null && !endDate.trim().isEmpty()) ? endDate + " 23:59:59" : "2999-12-31 23:59:59";
                recordsPage = rewardPenaltyRecordRepository.findByOccurTimeBetween(startDateTime, endDateTime, pageable);
                logger.info("管理员 {} 按发生时间范围查询积分记录：发生时间范围={} 至 {}，查询结果数量={}", currentUsername, startDateTime, endDateTime, recordsPage.getTotalElements());
                
                // 添加调试：打印查询到的记录的发生时间
                if (logger.isDebugEnabled()) {
                    recordsPage.getContent().forEach(record -> 
                        logger.debug("查询到记录：姓名={}, 发生时间={}, 创建时间={}, 事由={}", 
                            record.getName(), record.getOccurTime(), record.getCreateTime(), record.getReason())
                    );
                }
            } else {
                // 查询所有记录
                recordsPage = rewardPenaltyRecordRepository.findAll(pageable);
                logger.info("管理员 {} 查看所有积分记录，第 {} 页，每页 {} 条", currentUsername, page, size);
            }
            
            model.addAttribute("records", recordsPage.getContent());
            model.addAttribute("currentPage", page);
            model.addAttribute("totalPages", recordsPage.getTotalPages());
            model.addAttribute("totalItems", recordsPage.getTotalElements());
            model.addAttribute("name", name);
            model.addAttribute("type", type);
            model.addAttribute("startDate", startDate);
            model.addAttribute("endDate", endDate);
            model.addAttribute("currentUsername", currentUsername);
            model.addAttribute("activeMenu", "points-management");
            // 将人员列表添加到模型，用于姓名下拉选择
            model.addAttribute("personnel", optionsService.getPersonnel());
            
            return "reward-penalty/points-management";
        } catch (Exception e) {
            logger.error("显示积分管理页面时出错", e);
            model.addAttribute("errorMessage", "加载积分记录失败: " + e.getMessage());
            return "reward-penalty/points-management";
        }
    }

    /**
     * 查询所有人员最新存量积分并显示到积分管理列表
     */
    @GetMapping("/points-management/latest-points")
    @PreAuthorize("hasRole('ADMIN')")
    public String showLatestPointsForAllPersons(Model model, Principal principal) {
        String currentUsername = principal.getName();
        List<RewardPenaltyRecord> latestRecords = rewardPenaltyRecordRepository.findLatestPointsForAllPersons();
        model.addAttribute("records", latestRecords);
        model.addAttribute("currentPage", 1);
        model.addAttribute("totalPages", 1);
        model.addAttribute("totalItems", latestRecords.size());
        model.addAttribute("name", null);
        model.addAttribute("type", null);
        model.addAttribute("startDate", null);
        model.addAttribute("endDate", null);
        model.addAttribute("currentUsername", currentUsername);
        model.addAttribute("activeMenu", "points-management");
        model.addAttribute("personnel", optionsService.getPersonnel());
            // ...无需isAdmin变量，前端直接用Spring Security扩展判断角色...
        return "reward-penalty/points-management";
    }

    /**
     * 显示创建新奖罚记录的表单
     */
    @GetMapping("/reward-penalty/create")
    @PreAuthorize("hasRole('ADMIN')")
    public String showCreateForm(Model model, Principal principal) {
        model.addAttribute("record", new RewardPenaltyRecord());
        model.addAttribute("currentUsername", principal.getName());
        model.addAttribute("action", "create");
        model.addAttribute("personnel", optionsService.getPersonnel());
        return "reward-penalty/form";
    }

    /**
     * 保存新的奖罚记录
     */
    @PostMapping("/reward-penalty/create")
    @PreAuthorize("hasRole('ADMIN')")
    public String createRecord(@ModelAttribute RewardPenaltyRecord record,
                              @RequestParam(required = false) String currentUsername,
                              RedirectAttributes redirectAttributes) {
        try {
            // 设置创建时间和发生时间
            String currentTime = LocalDateTime.now().format(FORMATTER);
            if (record.getCreateTime() == null || record.getCreateTime().isEmpty()) {
                record.setCreateTime(currentTime);
            }
            if (record.getOccurTime() == null || record.getOccurTime().isEmpty()) {
                record.setOccurTime(currentTime);
            }

            // 处理创建时间格式
            if (record.getCreateTime() == null || record.getCreateTime().isEmpty()) {
                // 如果没有设置创建时间，则使用当前时间
                record.setCreateTime(LocalDateTime.now().format(FORMATTER));
                logger.info("创建时间为空，设置为当前时间: {}", record.getCreateTime());
            } else if (record.getCreateTime().contains("T")) {
                // 如果是 datetime-local 格式（包含T），转换为标准格式
                try {
                    // 解析 ISO 格式并转换
                    LocalDateTime dateTime = LocalDateTime.parse(record.getCreateTime());
                    String formattedTime = dateTime.format(FORMATTER);
                    record.setCreateTime(formattedTime);
                    logger.info("创建时间格式转换: {} -> {}", record.getCreateTime(), formattedTime);
                } catch (Exception e) {
                    logger.error("创建时间格式转换失败: {}, 使用当前时间", record.getCreateTime(), e);
                    record.setCreateTime(LocalDateTime.now().format(FORMATTER));
                }
            }

            // 使用服务方法自动计算存量积分
            RewardPenaltyRecord savedRecord = rewardPenaltyRecordService.createRecordWithTotalPoints(record);
            
            redirectAttributes.addFlashAttribute("successMessage", 
                    String.format("奖罚记录创建成功！为 %s %s %d 积分", 
                            record.getName(), 
                            record.getPoints() > 0 ? "奖励" : "扣除",
                            Math.abs(record.getPoints())));
            
            logger.info("管理员 {} 创建了新的奖罚记录：{}", currentUsername, savedRecord);
            
            return "redirect:/points-management";
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("errorMessage", "创建奖罚记录失败: " + e.getMessage());
            logger.error("创建奖罚记录失败", e);
            return "redirect:/reward-penalty/create";
        }
    }

    /**
     * 显示编辑奖罚记录的表单
     */
    @GetMapping("/reward-penalty/edit/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public String showEditForm(@PathVariable Long id, Model model, Principal principal,
                              RedirectAttributes redirectAttributes) {
        Optional<RewardPenaltyRecord> recordOpt = rewardPenaltyRecordService.findById(id);
        if (recordOpt.isPresent()) {
            model.addAttribute("record", recordOpt.get());
            model.addAttribute("currentUsername", principal.getName());
            model.addAttribute("action", "edit");
            model.addAttribute("personnel", optionsService.getPersonnel());
            return "reward-penalty/form";
        } else {
            redirectAttributes.addFlashAttribute("errorMessage", "找不到ID为 " + id + " 的奖罚记录");
            return "redirect:/points-management";
        }
    }

    /**
     * 更新奖罚记录
     */
    @PostMapping("/reward-penalty/update/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public String updateRecord(@PathVariable Long id,
                              @ModelAttribute RewardPenaltyRecord record,
                              @RequestParam(required = false) String currentUsername,
                              RedirectAttributes redirectAttributes) {
        try {
            record.setId(id);
            
            // 调试日志：打印接收到的数据
            logger.info("更新记录接收到的数据: createTime={}, occurTime={}", 
                    record.getCreateTime(), record.getOccurTime());
            
            // 确保时间格式正确
            if (record.getCreateTime() == null || record.getCreateTime().isEmpty()) {
                // 如果没有设置创建时间，则使用当前时间
                record.setCreateTime(LocalDateTime.now().format(FORMATTER));
                logger.info("创建时间为空，设置为当前时间: {}", record.getCreateTime());
            }
            
            // 处理发生时间格式
            if (record.getOccurTime() == null || record.getOccurTime().isEmpty()) {
                // 如果没有设置发生时间，则使用当前时间
                record.setOccurTime(LocalDateTime.now().format(FORMATTER));
                logger.info("发生时间为空，设置为当前时间: {}", record.getOccurTime());
            } else if (record.getOccurTime().contains("T")) {
                // 如果是 datetime-local 格式（包含T），转换为标准格式
                try {
                    // 解析 ISO 格式并转换
                    LocalDateTime dateTime = LocalDateTime.parse(record.getOccurTime());
                    String formattedTime = dateTime.format(FORMATTER);
                    record.setOccurTime(formattedTime);
                    logger.info("发生时间格式转换: {} -> {}", record.getOccurTime(), formattedTime);
                } catch (Exception e) {
                    logger.error("发生时间格式转换失败: {}, 使用当前时间", record.getOccurTime(), e);
                    record.setOccurTime(LocalDateTime.now().format(FORMATTER));
                }
            }
            
            RewardPenaltyRecord updatedRecord = rewardPenaltyRecordService.updateRecord(record);
            
            redirectAttributes.addFlashAttribute("successMessage", "奖罚记录更新成功");
            logger.info("管理员 {} 更新了奖罚记录：{}", currentUsername, updatedRecord);
            
            return "redirect:/points-management";
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("errorMessage", "更新奖罚记录失败: " + e.getMessage());
            logger.error("更新奖罚记录失败", e);
            return "redirect:/reward-penalty/edit/" + id;
        }
    }

    /**
     * 删除奖罚记录
     */
    @PostMapping("/reward-penalty/delete/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public String deleteRecord(@PathVariable Long id,
                              @RequestParam(required = false) String currentUsername,
                              RedirectAttributes redirectAttributes) {
        try {
            Optional<RewardPenaltyRecord> recordOpt = rewardPenaltyRecordService.findById(id);
            
            if (recordOpt.isPresent()) {
                RewardPenaltyRecord record = recordOpt.get();
                rewardPenaltyRecordService.deleteRecord(id);
                
                redirectAttributes.addFlashAttribute("successMessage", "奖罚记录删除成功");
                logger.info("管理员 {} 删除了奖罚记录：{}", currentUsername, record);
            } else {
                redirectAttributes.addFlashAttribute("errorMessage", "找不到ID为 " + id + " 的奖罚记录");
            }
            
            return "redirect:/points-management";
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("errorMessage", "删除奖罚记录失败: " + e.getMessage());
            logger.error("删除奖罚记录失败", e);
            return "redirect:/points-management";
        }
    }
}
