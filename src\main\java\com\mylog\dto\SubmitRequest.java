package com.mylog.dto;

/**
 * 任务提交请求DTO
 */
public class SubmitRequest {

    /**
     * 默认构造函数
     */
    public SubmitRequest() {
        // 默认构造函数
    }
    private Long taskId;
    private String remarks;
    private String submitName;
    private String tempFilePath1;
    private String tempFilePath2;
    private String tempFilePath3;
    private String tempFilePath4;
    private String fileName1;
    private String fileName2;
    private String fileName3;
    private String fileName4;
    private Boolean needApproval;
    private String taskCompletionStatus; // 新增字段：任务完成状态 ("completed" 或 "paused")
    private String showAttachments; // 新增字段：是否显示附件 ("是" 或 "否")

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getSubmitName() {
        return submitName;
    }

    public void setSubmitName(String submitName) {
        this.submitName = submitName;
    }

    public String getTempFilePath1() {
        return tempFilePath1;
    }

    public void setTempFilePath1(String tempFilePath1) {
        this.tempFilePath1 = tempFilePath1;
    }

    public String getTempFilePath2() {
        return tempFilePath2;
    }

    public void setTempFilePath2(String tempFilePath2) {
        this.tempFilePath2 = tempFilePath2;
    }

    public String getFileName1() {
        return fileName1;
    }

    public void setFileName1(String fileName1) {
        this.fileName1 = fileName1;
    }

    public String getFileName2() {
        return fileName2;
    }

    public void setFileName2(String fileName2) {
        this.fileName2 = fileName2;
    }

    public String getTempFilePath3() {
        return tempFilePath3;
    }

    public void setTempFilePath3(String tempFilePath3) {
        this.tempFilePath3 = tempFilePath3;
    }

    public String getTempFilePath4() {
        return tempFilePath4;
    }

    public void setTempFilePath4(String tempFilePath4) {
        this.tempFilePath4 = tempFilePath4;
    }

    public String getFileName3() {
        return fileName3;
    }

    public void setFileName3(String fileName3) {
        this.fileName3 = fileName3;
    }

    public String getFileName4() {
        return fileName4;
    }

    public void setFileName4(String fileName4) {
        this.fileName4 = fileName4;
    }

    public Boolean getNeedApproval() {
        return needApproval;
    }

    public void setNeedApproval(Boolean needApproval) {
        this.needApproval = needApproval;
    }

    public String getTaskCompletionStatus() {
        return taskCompletionStatus;
    }

    public void setTaskCompletionStatus(String taskCompletionStatus) {
        this.taskCompletionStatus = taskCompletionStatus;
    }

    public String getShowAttachments() {
        return showAttachments;
    }

    public void setShowAttachments(String showAttachments) {
        this.showAttachments = showAttachments;
    }

    @Override
    public String toString() {
        return "SubmitRequest{" +
                "taskId=" + taskId +
                ", remarks='" + remarks + '\'' +
                ", submitName='" + submitName + '\'' +
                ", tempFilePath1='" + tempFilePath1 + '\'' +
                ", tempFilePath2='" + tempFilePath2 + '\'' +
                ", tempFilePath3='" + tempFilePath3 + '\'' +
                ", tempFilePath4='" + tempFilePath4 + '\'' +
                ", fileName1='" + fileName1 + '\'' +
                ", fileName2='" + fileName2 + '\'' +
                ", fileName3='" + fileName3 + '\'' +
                ", fileName4='" + fileName4 + '\'' +
                ", needApproval=" + needApproval +
                '}';
    }
}
