<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head th:replace="~{fragments/layout :: head('任务评论表单')}">
    <meta charset="UTF-8">
    <meta http-equiv="Cache-Control" content="no-store, no-cache, must-revalidate, max-age=0">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>任务评论表单</title>
    <style>
        /* @联想功能样式 */
        .mention-dropdown {
            position: absolute;
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            max-height: 300px;
            overflow-y: auto;
            z-index: 1000;
            min-width: 250px;
            margin-top: 2px;
        }
        
        .mention-search {
            padding: 8px;
            border-bottom: 1px solid #eee;
        }
        
        .mention-search input {
            width: 100%;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 6px 10px;
            font-size: 14px;
            outline: none;
        }
        
        .mention-search input:focus {
            border-color: #007bff;
        }
        
        .mention-list {
            max-height: 200px;
            overflow-y: auto;
        }
        
        .mention-item {
            padding: 10px 15px;
            cursor: pointer;
            border-bottom: 1px solid #f5f5f5;
            display: flex;
            align-items: center;
            transition: background-color 0.2s;
        }
        
        .mention-item:hover,
        .mention-item.active {
            background-color: #f8f9fa;
        }
        
        .mention-item:last-child {
            border-bottom: none;
        }
        
        .mention-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #007bff;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            font-size: 14px;
            font-weight: bold;
        }
        
        .mention-name {
            font-size: 14px;
            color: #333;
        }
        

    </style>
    <script>
        // 页面加载前执行
        window.onpageshow = function(event) {
            // 如果是从bfcache中加载的页面，则立即跳转到来源页面
            if (event.persisted) {
                returnToReferer();
            }
        };

        // 防止在浏览器历史中记录该页面
        history.replaceState(null, document.title, window.location.href);

        // 强制禁用浏览器的后退功能，直接返回到引用页面
        window.addEventListener('popstate', function() {
            returnToReferer();
        });

        // 返回到引用页面的函数
        function returnToReferer() {
            var referer = sessionStorage.getItem('subTaskReferer');
            if (referer) {
                window.location.replace(referer);
            } else {
                // 如果没有保存的引用页面，尝试从表单获取
                var refererInput = document.querySelector('input[name="referer"]');
                if (refererInput && refererInput.value) {
                    window.location.replace(refererInput.value);
                } else {
                    // 如果都没有，返回到任务列表
                    window.location.replace('/tasks/order-tasks');
                }
            }
        }
    </script>
</head>
<body th:replace="~{fragments/layout :: body(~{::div.content-wrapper}, ~{::script})}">
    <div class="content-wrapper">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2">添加任务评论</h1>
        </div>

        <div class="row">
            <div class="col-md-12">
                <form th:action="@{/subtasks/save}" method="post" th:object="${subTask}" class="needs-validation" novalidate>
                    <input type="hidden" th:field="*{subTaskId}" />
                    <input type="hidden" th:field="*{taskId}" />
                    <input type="hidden" th:field="*{businessType}" />
                    <input type="hidden" th:field="*{sequenceNumber}" />
                    <input type="hidden" name="referer" th:value="${referer}" id="refererInput" />

                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">任务评论信息</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-12">
                                    <label for="logContent" class="form-label">内容<span class="text-danger">*</span></label>
                                    <div class="position-relative">
                                        <textarea class="form-control" id="logContent" th:field="*{logContent}" rows="6" required 
                                                  placeholder="输入评论内容，使用@符号可以提及相关人员..."></textarea>
                                        <div id="mentionDropdown" class="mention-dropdown" style="display: none;">
                                            <div class="mention-search">
                                                <input type="text" id="mentionSearch" placeholder="搜索人员...">
                                            </div>
                                            <div class="mention-list" id="mentionList"></div>
                                        </div>
                                    </div>
                                    <div class="invalid-feedback">请输入任务评论内容</div>
                                    <small class="form-text text-muted">提示：输入@符号可以选择抄送人员</small>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <label for="status" class="form-label">状态<span class="text-danger">*</span></label>
                                    <select class="form-select" id="status" th:field="*{status}" required>
                                        <option th:value="未开始" selected>未开始</option>
                                        <option th:value="进行中">进行中</option>                                        
                                    </select>
                                    <div class="invalid-feedback">请选择状态</div>
                                    <small class="form-text text-muted">如果需要把本评论作为一个子任务来跟进和处理时，状态切换到“进行中”，否则保持“未开始”状态。</small>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary">保存</button>
                                    <a href="#" onclick="returnToReferer(); return false;" class="btn btn-secondary">取消</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // @联想功能相关变量
        let personnelList = [];
        let currentMentionStart = -1;
        let selectedIndex = -1;

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 保存引用页面到会话存储
            var refererInput = document.getElementById('refererInput');
            if (refererInput && refererInput.value) {
                sessionStorage.setItem('subTaskReferer', refererInput.value);
            }

            // 禁用浏览器缓存
            window.addEventListener('beforeunload', function() {
                // 不显示提示，但可以确保页面在卸载后不会被缓存
            });

            // 初始化@联想功能
            initMentionFeature();

            // 表单验证
            var forms = document.querySelectorAll('.needs-validation');
            Array.prototype.slice.call(forms).forEach(function(form) {
                form.addEventListener('submit', function(event) {
                    if (!form.checkValidity()) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    form.classList.add('was-validated');
                }, false);
            });
        });

        // 检测历史状态变化
        (function() {
            // 标记当前页面，使其不会被添加到历史记录
            sessionStorage.setItem('noHistoryPage', window.location.href);

            // 创建一个检测函数，确保用户不能通过后退按钮返回到此页面
            function checkBrowserNavigation() {
                if (performance && performance.navigation) {
                    // 如果是通过后退按钮访问的
                    if (performance.navigation.type === 2) {
                        returnToReferer();
                    }
                }
            }

            // 每隔一段时间检查一次导航类型
            setInterval(checkBrowserNavigation, 200);
        })();

        // 初始化@联想功能
        function initMentionFeature() {
            console.log('正在初始化@联想功能...');
            
            // 获取人员列表
            fetchPersonnelList();
            
            const textarea = document.getElementById('logContent');
            const dropdown = document.getElementById('mentionDropdown');
            const searchInput = document.getElementById('mentionSearch');
            const mentionList = document.getElementById('mentionList');
            
            // 检查元素是否存在
            if (!textarea) {
                console.error('未找到logContent元素');
                return;
            }
            if (!dropdown) {
                console.error('未找到mentionDropdown元素');
                return;
            }
            
            console.log('DOM元素绑定成功，开始监听事件...');
            
            // 监听textarea的输入事件
            textarea.addEventListener('input', function(e) {
                console.log('检测到输入事件:', e.target.value);
                handleTextareaInput(e);
            });
            
            // 监听键盘事件
            textarea.addEventListener('keydown', function(e) {
                if (dropdown.style.display !== 'none') {
                    handleDropdownKeydown(e);
                }
            });
            
            // 监听搜索框输入
            searchInput.addEventListener('input', function(e) {
                filterPersonnelList(e.target.value);
            });
            
            // 点击其他地方关闭下拉列表
            document.addEventListener('click', function(e) {
                if (!dropdown.contains(e.target) && e.target !== textarea) {
                    hideDropdown();
                }
            });
        }

        // 获取人员列表
        function fetchPersonnelList() {
            console.log('开始获取人员列表...');
            fetch('/subtasks/api/personnel')
                .then(response => {
                    console.log('API响应状态:', response.status);
                    if (!response.ok) {
                        throw new Error('API请求失败: ' + response.status);
                    }
                    return response.json();
                })
                .then(data => {
                    personnelList = data;
                    console.log('获取到人员列表:', personnelList.length, '人', personnelList);
                })
                .catch(error => {
                    console.error('获取人员列表失败:', error);
                    personnelList = ['张三', '李四', '王五']; // 提供测试数据
                    console.log('使用测试数据:', personnelList);
                });
        }

        // 处理textarea输入
        function handleTextareaInput(e) {
            const textarea = e.target;
            const value = textarea.value;
            const cursorPos = textarea.selectionStart;
            
            console.log('处理输入:', value, '光标位置:', cursorPos);
            
            // 查找最后一个@符号的位置
            const lastAtIndex = value.lastIndexOf('@', cursorPos - 1);
            console.log('最后@位置:', lastAtIndex);
            
            if (lastAtIndex !== -1) {
                // 检查@后面是否只有字母、数字、中文字符
                const afterAt = value.substring(lastAtIndex + 1, cursorPos);
                const isValidQuery = /^[\u4e00-\u9fa5a-zA-Z0-9]*$/.test(afterAt);
                
                console.log('@后内容:', afterAt, '是否有效:', isValidQuery);
                
                if (isValidQuery && (lastAtIndex === 0 || /\s/.test(value.charAt(lastAtIndex - 1)))) {
                    // 显示下拉列表
                    currentMentionStart = lastAtIndex;
                    console.log('显示下拉列表，查询:', afterAt);
                    showDropdown(textarea, afterAt);
                    return;
                }
            }
            
            // 隐藏下拉列表
            hideDropdown();
        }

        // 显示下拉列表
        function showDropdown(textarea, query) {
            const dropdown = document.getElementById('mentionDropdown');
            const searchInput = document.getElementById('mentionSearch');
            
            // 计算下拉列表位置
            const rect = textarea.getBoundingClientRect();
            dropdown.style.display = 'block';
            dropdown.style.left = rect.left + 'px';
            dropdown.style.top = (rect.bottom + window.scrollY) + 'px';
            dropdown.style.width = Math.max(250, rect.width) + 'px';
            
            // 设置搜索框值并过滤
            searchInput.value = query;
            filterPersonnelList(query);
            
            // 聚焦搜索框
            setTimeout(() => searchInput.focus(), 10);
        }

        // 隐藏下拉列表
        function hideDropdown() {
            const dropdown = document.getElementById('mentionDropdown');
            dropdown.style.display = 'none';
            currentMentionStart = -1;
            selectedIndex = -1;
        }

        // 过滤人员列表
        function filterPersonnelList(query) {
            const mentionList = document.getElementById('mentionList');
            const filteredList = personnelList.filter(person => 
                person.toLowerCase().includes(query.toLowerCase())
            );
            
            console.log('过滤人员列表 - 查询:', query, '原列表:', personnelList.length, '过滤后:', filteredList.length, filteredList);
            
            mentionList.innerHTML = '';
            selectedIndex = -1;
            
            if (filteredList.length === 0) {
                mentionList.innerHTML = '<div class="mention-item" style="color: #999;">未找到匹配的人员</div>';
                return;
            }
            
            filteredList.forEach((person, index) => {
                const item = document.createElement('div');
                item.className = 'mention-item';

    // 创建名称部分
    const nameDiv = document.createElement('div');
    nameDiv.className = 'mention-name';
    nameDiv.textContent = person; // 使用 textContent 更安全
    
    item.appendChild(nameDiv);

                // item.innerHTML = `
                //     <div class="mention-avatar">${person.charAt(0)}</div>
                //     <div class="mention-name">${person}</div>
                // `;
                
                item.addEventListener('click', function() {
                    selectPerson(person);
                });
                
                mentionList.appendChild(item);
            });
        }

        // 处理下拉列表键盘事件
        function handleDropdownKeydown(e) {
            const items = document.querySelectorAll('.mention-item');
            const validItems = Array.from(items).filter(item => 
                !item.textContent.includes('未找到匹配的人员')
            );
            
            if (validItems.length === 0) return;
            
            switch (e.key) {
                case 'ArrowDown':
                    e.preventDefault();
                    selectedIndex = Math.min(selectedIndex + 1, validItems.length - 1);
                    updateSelection(validItems);
                    break;
                case 'ArrowUp':
                    e.preventDefault();
                    selectedIndex = Math.max(selectedIndex - 1, 0);
                    updateSelection(validItems);
                    break;
                case 'Enter':
                    e.preventDefault();
                    if (selectedIndex >= 0 && selectedIndex < validItems.length) {
                        const selectedPerson = validItems[selectedIndex].querySelector('.mention-name').textContent;
                        selectPerson(selectedPerson);
                    }
                    break;
                case 'Escape':
                    e.preventDefault();
                    hideDropdown();
                    document.getElementById('logContent').focus();
                    break;
            }
        }

        // 更新选中状态
        function updateSelection(items) {
            items.forEach((item, index) => {
                item.classList.toggle('active', index === selectedIndex);
            });
            
            // 滚动到选中项
            if (selectedIndex >= 0 && selectedIndex < items.length) {
                items[selectedIndex].scrollIntoView({ block: 'nearest' });
            }
        }

        // 选择人员
        function selectPerson(person) {
            const textarea = document.getElementById('logContent');
            const value = textarea.value;
            const cursorPos = textarea.selectionStart;
            
            // 替换@查询为选中的人员
            const beforeMention = value.substring(0, currentMentionStart);
            const afterCursor = value.substring(cursorPos);
            const newValue = beforeMention + '@' + person + ' ' + afterCursor;
            
            textarea.value = newValue;
            
            // 设置光标位置
            const newCursorPos = beforeMention.length + person.length + 2;
            textarea.setSelectionRange(newCursorPos, newCursorPos);
            
            // 隐藏下拉列表
            hideDropdown();
            
            // 聚焦回textarea
            textarea.focus();
        }
    </script>
</body>
</html>
