﻿# 基础配置
spring.application.name=mylog-web
server.port=8080

# HTTPS 强制重定向配置（生产环境建议启用）
# server.ssl.enabled=true
# server.require-ssl=true
# 开发环境可以通过环境变量或profile来启用HTTPS强制

# 时区配置
spring.jpa.properties.hibernate.jdbc.time_zone=Asia/Shanghai
server.servlet.encoding.charset=UTF-8
spring.jackson.time-zone=Asia/Shanghai
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.serialization.write-dates-as-timestamps=false

# 应用版本信息 (编译时可替换)
app.name=MyLog Web
app.version=@project.version@
app.description=@project.description@
# 编译时版本号格式：1.YYMMDD.HHMM (使用北京时间)
app.build.version=<EMAIL>@
app.build.timestamp=@build.timestamp.display@

# 数据库配置
spring.datasource.url=***********************************
spring.datasource.driver-class-name=org.sqlite.JDBC

# HikariCP 连接池配置
spring.datasource.hikari.maximum-pool-size=10
spring.datasource.hikari.minimum-idle=2
spring.datasource.hikari.idle-timeout=300000
spring.datasource.hikari.pool-name=LogManagementPool
spring.datasource.hikari.max-lifetime=1800000
spring.datasource.hikari.connection-timeout=20000
spring.datasource.hikari.leak-detection-threshold=60000
spring.datasource.hikari.validation-timeout=5000
spring.datasource.hikari.connection-test-query=SELECT 1

# JPA配置
spring.jpa.database-platform=org.hibernate.community.dialect.SQLiteDialect
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.open-in-view=false
# 彻底禁用所有Hibernate SQL日志输出
spring.jpa.properties.hibernate.show_sql=false
spring.jpa.properties.hibernate.use_sql_comments=false
spring.jpa.properties.hibernate.jdbc.log.statements=false
spring.jpa.properties.hibernate.generate_statistics=false
spring.jpa.properties.hibernate.SQL=FATAL

# 编码配置
spring.http.encoding.charset=UTF-8
spring.http.encoding.enabled=true
spring.http.encoding.force=true
server.servlet.encoding.charset=UTF-8
server.servlet.encoding.enabled=true
server.servlet.encoding.force=true

# 用户数据库
spring.user-datasource.url=*******************************************************

# 内容协商配置
spring.mvc.contentnegotiation.favor-parameter=true
spring.mvc.contentnegotiation.default-content-type=application/json
spring.mvc.contentnegotiation.media-types.json=application/json
spring.mvc.contentnegotiation.parameter-name=format

# 日志配置
logging.level.root=INFO
logging.level.com.mylog=DEBUG
# Spring Web 路径映射调试
logging.level.org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping=DEBUG
logging.level.org.springframework.web.servlet.DispatcherServlet=DEBUG
# 禁用 Hibernate SQL 日志
logging.level.org.hibernate.SQL=OFF
logging.level.org.hibernate=ERROR
logging.level.org.hibernate.type.descriptor.sql=OFF
logging.level.org.hibernate.orm.jdbc.bind=OFF
logging.level.org.hibernate.type=OFF
logging.level.org.hibernate.stat=OFF
logging.level.org.hibernate.SQL_SLOW=OFF
logging.level.org.hibernate.engine.jdbc=OFF
logging.level.org.hibernate.orm.jdbc=OFF
# 日志文件配置已移至 logback-spring.xml
# logging.file.name=./logs/mylog-web.log

# 会话配置
server.servlet.session.timeout=480m

# 文件上传配置 - 增加到50MB以支持大文件上传
spring.servlet.multipart.max-file-size=50MB
spring.servlet.multipart.max-request-size=50MB

# 数据路径配置
data.path=./data

# HTTPS强制配置（生产环境启用）
# 设置为true时，所有HTTP请求将重定向到HTTPS
# 可以通过环境变量MYLOG_FORCE_HTTPS=true来覆盖
mylog.force.https=false

# 云存储配置（用于Office Web Viewer预览）
# 支持的服务类型：aliyun-oss, tencent-cos, aws-s3, disabled
cloud.storage.type=tencent-cos
cloud.storage.bucket.name=prj-1376818295
cloud.storage.region=ap-guangzhou
cloud.storage.endpoint=
cloud.storage.access.key.id=AKIDieRtFPM8g18lHbjjWL0ShleLvISP9g2D
cloud.storage.access.key.secret=2sreGgttiKp8FAwWu53bw4Sggv31mKm0
# 临时文件过期时间（小时），默认24小时
cloud.storage.temp.expire.hours=24
# 文件URL前缀（可选，用于自定义域名）
cloud.storage.url.prefix=

# cpolar隧道穿透配置（用于Office Web Viewer预览）
# 启用cpolar预览功能
cpolar.preview.enabled=true
# cpolar公网域名（需要购买专业版获得固定域名）
cpolar.public.domain=ppp.cpolar.cn
# 预览URL的token有效期（分钟），默认60分钟
cpolar.preview.token.expire.minutes=60

# 机器码API配置
machine.code.api.url=http://localhost:5000/api/machinecode
machine.code.api.timeout=10
