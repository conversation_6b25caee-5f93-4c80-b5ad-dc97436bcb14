<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head th:fragment="head(title)">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <meta name="_csrf" th:content="${_csrf?.token}">
    <meta name="_csrf_header" th:content="${_csrf?.headerName}">
    <title th:text="${title} + ' - 项目管理系统'">项目管理系统</title>
    <link rel="icon" th:href="@{/images/logo.svg}" type="image/svg+xml">
    <link rel="shortcut icon" th:href="@{/favicon.ico}" type="image/x-icon">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">    <link rel="stylesheet" th:href="@{/css/style.css}">
    <link rel="stylesheet" th:href="@{/css/themes.css}">
    <link rel="stylesheet" th:href="@{/css/search-condition.css}">
    <link rel="stylesheet" th:href="@{/css/table-sort.css}">
    <th:block th:replace="${extraStyles} ?: ~{}"></th:block>
</head>
<body th:fragment="body(content, customScript)" th:class="${userTheme}">
    <script>
        // 在页面加载前立即应用侧边栏状态，避免闪烁
        (function() {
            const sidebarState = localStorage.getItem('sidebarCollapsed');
            if (sidebarState === 'true') {
                document.documentElement.classList.add('sidebar-init-collapsed');
            }
        })();
    </script>
    <div class="container-fluid">
        <div class="row">
            <!-- 引入通用侧边栏 -->
            <div th:replace="~{fragments/sidebar :: sidebar}"></div>

            <!-- 主内容区 -->
            <main class="col-md-10 ms-sm-auto col-lg-11 px-md-4">
                <!-- 移动端导航切换按钮 -->
                <button class="btn btn-link d-md-none mt-3 mb-2 p-0 fs-4" id="sidebarToggle">
                    <i class="bi bi-list"></i>
                </button>
                <th:block th:replace="${content}"></th:block>
            </main>
        </div>
    </div>    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script th:src="@{/js/sidebar.js}"></script>
    <script th:src="@{/js/sidebar-refresh.js}"></script>
    <script th:src="@{/js/tooltip-init.js}"></script>
    <th:block th:replace="${extraScripts} ?: ~{}"></th:block>
    <th:block th:replace="${customScript} ?: ~{}"></th:block>
</body>
</html>