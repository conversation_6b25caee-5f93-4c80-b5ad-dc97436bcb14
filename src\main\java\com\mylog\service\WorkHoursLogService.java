package com.mylog.service;

import com.mylog.model.WorkHoursLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import java.util.List;
import java.util.Optional;

/**
 * 工期登记服务接口
 */
public interface WorkHoursLogService {
    
    /**
     * 保存工期记录
     * @param workHoursLog 工期记录
     * @return 保存后的工期记录
     */
    WorkHoursLog saveWorkHoursLog(WorkHoursLog workHoursLog);
    
    /**
     * 根据ID查找工期记录
     * @param id 记录ID
     * @return 工期记录
     */
    Optional<WorkHoursLog> findWorkHoursLogById(Long id);
    
    /**
     * 删除工期记录
     * @param id 记录ID
     */
    void deleteWorkHoursLog(Long id);
    
    /**
     * 查找所有工期记录
     * @return 工期记录列表
     */
    List<WorkHoursLog> findAllWorkHoursLogs();
    
    /**
     * 查找所有工期记录（分页）
     * @param pageable 分页参数
     * @return 工期记录分页列表
     */
    Page<WorkHoursLog> findAllWorkHoursLogs(Pageable pageable);
    
    /**
     * 根据业务类型查找工期记录
     * @param businessType 业务类型
     * @return 工期记录列表
     */
    List<WorkHoursLog> findWorkHoursLogsByBusinessType(String businessType);
    
    /**
     * 根据业务类型查找工期记录（分页）
     * @param businessType 业务类型
     * @param pageable 分页参数
     * @return 工期记录分页列表
     */
    Page<WorkHoursLog> findWorkHoursLogsByBusinessType(String businessType, Pageable pageable);
    
    /**
     * 根据业务ID查找工期记录
     * @param businessId 业务ID
     * @return 工期记录列表
     */
    List<WorkHoursLog> findWorkHoursLogsByBusinessId(Integer businessId);
    
    /**
     * 根据业务类型和业务ID查找工期记录
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @return 工期记录列表
     */
    List<WorkHoursLog> findWorkHoursLogsByBusinessTypeAndBusinessId(String businessType, Integer businessId);
    
    /**
     * 根据业务类型和业务ID查找工期记录（分页）
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @param pageable 分页参数
     * @return 工期记录分页列表
     */
    Page<WorkHoursLog> findWorkHoursLogsByBusinessTypeAndBusinessId(String businessType, Integer businessId, Pageable pageable);
    
    /**
     * 统计指定业务类型和业务ID的工期记录数量
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @return 记录数量
     */
    Long countWorkHoursLogsByBusinessTypeAndBusinessId(String businessType, Integer businessId);
    
    /**
     * 根据时间范围查找工期记录
     * @param startTime 开始时间（格式：2025-03-03 08:07:05）
     * @param endTime 结束时间（格式：2025-03-03 08:07:05）
     * @return 工期记录列表
     */
    List<WorkHoursLog> findWorkHoursLogsByTimeRange(String startTime, String endTime);
    
    /**
     * 根据时间范围查找工期记录（分页）
     * @param startTime 开始时间（格式：2025-03-03 08:07:05）
     * @param endTime 结束时间（格式：2025-03-03 08:07:05）
     * @param pageable 分页参数
     * @return 工期记录分页列表
     */
    Page<WorkHoursLog> findWorkHoursLogsByTimeRange(String startTime, String endTime, Pageable pageable);
    
    /**
     * 根据业务类型和时间范围查找工期记录
     * @param businessType 业务类型
     * @param startTime 开始时间（格式：2025-03-03 08:07:05）
     * @param endTime 结束时间（格式：2025-03-03 08:07:05）
     * @return 工期记录列表
     */
    List<WorkHoursLog> findWorkHoursLogsByBusinessTypeAndTimeRange(String businessType, String startTime, String endTime);
    
    /**
     * 获取某个业务的最新累计工期
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @return 最新的累计工期，如果没有记录则返回0.0
     */
    Double getLatestHoursInventory(String businessType, Integer businessId);
    
    /**
     * 计算某个业务的工期变化总和
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @return 工期变化总和
     */
    Double calculateTotalHoursChange(String businessType, Integer businessId);
    
    /**
     * 添加工期记录（自动计算存量，包含额定天数、创建人、备注和责任人）
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @param hoursChange 工期变化（正数为增加，负数为减少）
     * @param daysRated 额定天数
     * @param reason 原因
     * @param creator 创建人
     * @param remark 备注
     * @param responsiblePerson 责任人
     * @return 保存后的工期记录
     */
    WorkHoursLog addWorkHours(String businessType, Integer businessId, Double hoursChange, Double daysRated, String reason, String creator, String remark, String responsiblePerson, String startTime, String endTime);

    /**
     * 添加工期记录（自动计算存量，包含所有字段）
     * @param businessType 业务类型
     * @param businessId 业务ID
     * @param hoursChange 工期变化（正数为增加，负数为减少）
     * @param daysRated 额定天数
     * @param reason 原因
     * @param creator 创建人
     * @param remark 备注
     * @param bonus 奖金
     * @param responsiblePerson 责任人
     * @return 保存后的工期记录
     */
    WorkHoursLog addWorkHours(String businessType, Integer businessId, Double hoursChange, Double daysRated, String reason, String creator, String remark, Double bonus, String responsiblePerson);
        /**
     * 记录项目额定工期变更日志
     * @param projectId 项目ID
     * @param oldRatedDuration 原额定工期
     * @param newRatedDuration 新额定工期
     * @param username 操作用户
     * @param responsiblePerson 责任人
     */
    /**
     * 记录项目额定工期变更日志（支持开始/结束时间）
     * @param projectId 项目ID
     * @param oldRatedDuration 原额定工期
     * @param newRatedDuration 新额定工期
     * @param username 操作用户
     * @param responsiblePerson 责任人
     * @param startTime 开始时间（yyyy-MM-dd HH:mm:ss）
     * @param endTime 结束时间（yyyy-MM-dd HH:mm:ss）
     */
    void logProjectRatedDurationChange(Long projectId, java.math.BigDecimal oldRatedDuration, java.math.BigDecimal newRatedDuration, String username, String responsiblePerson, String startTime, String endTime);


    /**
     * 记录项目状态变更工期日志（支持开始/结束时间）
     * @param projectId 项目ID
     * @param oldStatus 原状态
     * @param newStatus 新状态
     * @param projectDurationDays 项目实际工期
     * @param ratedDurationDays 项目额定工期
     * @param username 操作用户
     * @param responsiblePerson 责任人
     * @param startTime 开始时间（yyyy-MM-dd HH:mm:ss）
     * @param endTime 结束时间（yyyy-MM-dd HH:mm:ss）
     */
        public void logProjectStatusChange(Long projectId, String oldStatus, String newStatus, 
            java.math.BigDecimal projectDurationDays, java.math.BigDecimal ratedDurationDays, String username,String responsiblePerson,String startTime,String endTime);

}
