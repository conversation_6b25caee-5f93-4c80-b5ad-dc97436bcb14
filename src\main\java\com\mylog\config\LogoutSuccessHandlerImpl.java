package com.mylog.config;

import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.logout.LogoutSuccessHandler;
import org.springframework.stereotype.Component;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.mylog.service.UserActivityLogService;
import com.mylog.service.UserService;
import com.mylog.util.UserAgentUtils;

import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 登出成功处理器，用于处理登出后的操作
 */
@Component
public class LogoutSuccessHandlerImpl implements LogoutSuccessHandler {
    
    private static final Logger logger = LoggerFactory.getLogger(LogoutSuccessHandlerImpl.class);
    
    private final UserActivityLogService activityLogService;
    private final UserService userService;
    
    public LogoutSuccessHandlerImpl(UserActivityLogService activityLogService, UserService userService) {
        this.activityLogService = activityLogService;
        this.userService = userService;
    }
    
    @Override
    public void onLogoutSuccess(HttpServletRequest request, HttpServletResponse response, 
                               Authentication authentication) throws IOException, ServletException {
        
        if (authentication != null) {
            String username = authentication.getName();
            logger.info("用户 {} 登出成功", username);
            
            try {
                // 记录用户登出日志
                userService.findUserByUsername(username).ifPresent(user -> {
                    String ipAddress = getClientIpAddress(request);
                    String accessType = UserAgentUtils.getAccessType(request);
                    activityLogService.logLogout(user.getUserId(), username, ipAddress, accessType);
                    logger.info("已记录用户 {} 的登出日志，IP: {}，设备类型: {}", username, ipAddress, accessType);
                });
            } catch (Exception e) {
                logger.error("登出处理过程中发生错误: {}", e.getMessage(), e);
            }
        }
        
        // 重定向到登录页
        response.sendRedirect(request.getContextPath() + "/login?logout");
    }
    
    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }
}