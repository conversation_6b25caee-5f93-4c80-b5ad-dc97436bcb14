package com.mylog.service;

import com.mylog.model.RewardPenaltyRecord;
import com.mylog.repository.RewardPenaltyRecordRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;

/**
 * 奖罚记录服务类
 * 提供对奖罚记录的业务操作
 */
@Service
public class RewardPenaltyRecordService {

    private static final Logger logger = LoggerFactory.getLogger(RewardPenaltyRecordService.class);
    // 标准日期时间格式
    public static final DateTimeFormatter STANDARD_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    @Autowired
    private RewardPenaltyRecordRepository rewardPenaltyRecordRepository;
    
    /**
     * 创建新的奖罚记录
     * 
     * @param record 奖罚记录对象
     * @return 保存后的奖罚记录对象
     */
    @Transactional
    public RewardPenaltyRecord createRecord(RewardPenaltyRecord record) {
        // 如果没有设置创建时间，则使用当前时间
        if (record.getCreateTime() == null || record.getCreateTime().isEmpty()) {
            record.setCreateTime(LocalDateTime.now().format(STANDARD_FORMATTER));
        }
        
        // 如果没有设置发生时间，则使用当前时间
        if (record.getOccurTime() == null || record.getOccurTime().isEmpty()) {
            record.setOccurTime(LocalDateTime.now().format(STANDARD_FORMATTER));
        }
        
        logger.info("创建奖罚记录：姓名={}, 类型={}, 积分={}", record.getName(), record.getType(), record.getPoints());
        return rewardPenaltyRecordRepository.save(record);
    }
    
    /**
     * 创建新的奖罚记录，自动计算存量积分
     * 
     * @param record 奖罚记录对象（不需要设置存量积分）
     * @return 保存后的奖罚记录对象
     */
    @Transactional
    public RewardPenaltyRecord createRecordWithTotalPoints(RewardPenaltyRecord record) {
        // 如果没有设置创建时间，则使用当前时间
        if (record.getCreateTime() == null || record.getCreateTime().isEmpty()) {
            record.setCreateTime(LocalDateTime.now().format(STANDARD_FORMATTER));
        }
        
        // 如果没有设置发生时间，则使用当前时间
        if (record.getOccurTime() == null || record.getOccurTime().isEmpty()) {
            record.setOccurTime(LocalDateTime.now().format(STANDARD_FORMATTER));
        }
        
        
        // 获取用户当前的总积分
        Integer currentTotalPoints = getLatestTotalPoints(record.getName());
        
        // 计算新的存量积分 = 当前总积分 + 变化积分
        Integer newTotalPoints = currentTotalPoints + record.getPoints();
        record.setTotalPoints(newTotalPoints);
        
        logger.info("创建奖罚记录：姓名={}, 类型={}, 变化积分={}, 存量积分={}", 
                record.getName(), record.getType(), record.getPoints(), record.getTotalPoints());
        
        return rewardPenaltyRecordRepository.save(record);
    }
    
    /**
     * 根据ID查询奖罚记录
     * 
     * @param id 奖罚记录ID
     * @return 奖罚记录对象
     */
    public Optional<RewardPenaltyRecord> findById(Long id) {
        return rewardPenaltyRecordRepository.findById(id);
    }
    
    /**
     * 查询所有奖罚记录
     * 
     * @return 所有奖罚记录列表
     */
    public List<RewardPenaltyRecord> findAll() {
        return rewardPenaltyRecordRepository.findAll();
    }
    /**
     * 根据姓名查询奖罚记录，按创建时间降序排列（最新的在前）
     * 
     * @param name 姓名
     * @return 该用户的所有奖罚记录，按创建时间从新到旧排序
     */
    public List<RewardPenaltyRecord> findByName(String name) {
        return rewardPenaltyRecordRepository.findByNameOrderByCreateTimeDesc(name);
    }
    
    /**
     * 根据奖罚类型查询记录
     * 
     * @param type 奖罚类型
     * @return 该类型的所有奖罚记录
     */
    public List<RewardPenaltyRecord> findByType(String type) {
        return rewardPenaltyRecordRepository.findByType(type);
    }
    
    /**
     * 查询某个用户的奖励记录（积分为正数）
     * 
     * @param name 用户姓名
     * @return 该用户的所有奖励记录
     */
    public List<RewardPenaltyRecord> findRewardsByName(String name) {
        return rewardPenaltyRecordRepository.findByNameAndPointsGreaterThan(name, 0);
    }
    
    /**
     * 查询某个用户的惩罚记录（积分为负数或零）
     * 
     * @param name 用户姓名
     * @return 该用户的所有惩罚记录
     */
    public List<RewardPenaltyRecord> findPenaltiesByName(String name) {
        return rewardPenaltyRecordRepository.findByNameAndPointsLessThanEqual(name, 0);
    }
    
    /**
     * 按照创建时间范围查询记录
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 符合时间范围的记录列表
     */
    public List<RewardPenaltyRecord> findByCreateTimeBetween(String startTime, String endTime) {
        return rewardPenaltyRecordRepository.findByCreateTimeBetween(startTime, endTime);
    }
    
    /**
     * 按照发生时间范围查询记录
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 符合时间范围的记录列表
     */
    public List<RewardPenaltyRecord> findByOccurTimeBetween(String startTime, String endTime) {
        return rewardPenaltyRecordRepository.findByOccurTimeBetween(startTime, endTime);
    }
    
    /**
     * 计算用户的总积分
     * 
     * @param name 用户姓名
     * @return 用户的总积分，如果没有记录则返回0
     */
    public Integer calculateTotalPointsByName(String name) {
        Integer totalPoints = rewardPenaltyRecordRepository.calculateTotalPointsByName(name);
        return totalPoints != null ? totalPoints : 0;
    }
    
    /**
     * 查询所有奖励记录（积分为正数）
     * 
     * @return 所有奖励记录列表
     */
    public List<RewardPenaltyRecord> findAllRewards() {
        return rewardPenaltyRecordRepository.findByPointsGreaterThan(0);
    }
    
    /**
     * 查询所有惩罚记录（积分为负数或零）
     * 
     * @return 所有惩罚记录列表
     */
    public List<RewardPenaltyRecord> findAllPenalties() {
        return rewardPenaltyRecordRepository.findByPointsLessThanEqual(0);
    }
    
    /**
     * 更新奖罚记录
     * 
     * @param record 要更新的奖罚记录对象
     * @return 更新后的奖罚记录对象
     */
    @Transactional
    public RewardPenaltyRecord updateRecord(RewardPenaltyRecord record) {
        if (record.getId() == null) {
            throw new IllegalArgumentException("更新奖罚记录时ID不能为空");
        }

        // 添加简单的重试机制，应对数据库锁定问题
        int maxRetries = 3;
        int retryCount = 0;
        RewardPenaltyRecord existing = null;
        while (retryCount < maxRetries) {
            try {
                Optional<RewardPenaltyRecord> existingRecord = rewardPenaltyRecordRepository.findById(record.getId());
                if (!existingRecord.isPresent()) {
                    throw new IllegalArgumentException("找不到ID为 " + record.getId() + " 的奖罚记录");
                }

                existing = existingRecord.get();

                // 保留原有不在表单中提交的非空字段，防止更新时将其置为 null
                if (record.getCreateTime() == null || record.getCreateTime().isEmpty()) {
                    record.setCreateTime(existing.getCreateTime());
                }
                if (record.getOccurTime() == null || record.getOccurTime().isEmpty()) {
                    record.setOccurTime(existing.getOccurTime());
                }

     
        // 获取用户当前第二新的总积分
        Integer currentTotalPoints = getSecondLatestTotalPoints(record.getName());
        
        // 计算新的存量积分 = 当前总积分 + 变化积分
        Integer newTotalPoints = currentTotalPoints + record.getPoints();
        record.setTotalPoints(newTotalPoints);

                // if (record.getTotalPoints() == null) {
                //     record.setTotalPoints(existing.getTotalPoints());
                // }
                
                // 成功执行后跳出循环
                break;
            } catch (Exception e) {
                // 判断是否是数据库锁定异常
                if (e instanceof org.springframework.orm.jpa.JpaSystemException && 
                    e.getCause() instanceof org.hibernate.TransactionException && 
                    e.getCause().getCause() instanceof org.sqlite.SQLiteException &&
                    e.getCause().getCause().getMessage().contains("database is locked")) {
                    retryCount++;
                    if (retryCount >= maxRetries) {
                        logger.error("多次尝试更新奖罚记录失败: " + e.getMessage(), e);
                        throw e;
                    }
                    try {
                        // 等待一小段时间后重试
                        Thread.sleep(100 * retryCount);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("线程被中断", ie);
                    }
                } else {
                    // 非数据库锁定异常，直接抛出
                    throw e;
                }
            }
        }
        if (record.getPoints() == null) {
            record.setPoints(existing.getPoints());
        }
        if (record.getName() == null || record.getName().isEmpty()) {
            record.setName(existing.getName());
        }
        if (record.getType() == null || record.getType().isEmpty()) {
            record.setType(existing.getType());
        }
        if (record.getReason() == null || record.getReason().isEmpty()) {
            record.setReason(existing.getReason());
        }
        if (record.getRemarks() == null) {
            record.setRemarks(existing.getRemarks());
        }
        
        // 验证日期格式是否正确
        if (record.getCreateTime() != null && !record.getCreateTime().isEmpty()) {
            try {
                LocalDateTime.parse(record.getCreateTime(), STANDARD_FORMATTER);
            } catch (Exception e) {
                throw new IllegalArgumentException("创建时间格式不正确，应为 yyyy-MM-dd HH:mm:ss", e);
            }
        }
        
        if (record.getOccurTime() != null && !record.getOccurTime().isEmpty()) {
            try {
                LocalDateTime.parse(record.getOccurTime(), STANDARD_FORMATTER);
            } catch (Exception e) {
                throw new IllegalArgumentException("发生时间格式不正确，应为 yyyy-MM-dd HH:mm:ss", e);
            }
        }

        logger.info("更新奖罚记录：ID={}, 姓名={}, 类型={}, 积分={}", 
                record.getId(), record.getName(), record.getType(), record.getPoints());
        return rewardPenaltyRecordRepository.save(record);
    }
    
    /**
     * 删除奖罚记录
     * 
     * @param id 要删除的奖罚记录ID
     */
    @Transactional
    public void deleteRecord(Long id) {
        logger.info("删除奖罚记录：ID={}", id);
        rewardPenaltyRecordRepository.deleteById(id);
    }
    
    /**
     * 获取用户的最新奖罚记录
     * 
     * @param name 用户姓名
     * @return 用户的最新奖罚记录，如果没有则返回空
     */
    public Optional<RewardPenaltyRecord> findLatestRecordByName(String name) {
        List<RewardPenaltyRecord> records = rewardPenaltyRecordRepository
                .findLatestRecordByName(name, org.springframework.data.domain.PageRequest.of(0, 1));
        return records.isEmpty() ? Optional.empty() : Optional.of(records.get(0));
    }
    
    /**
     * 获取用户的最新存量积分
     * 
     * @param name 用户姓名
     * @return 用户的最新存量积分，如果没有记录则返回0
     */
    public Integer getLatestTotalPoints(String name) {
        List<Integer> totalPointsList = rewardPenaltyRecordRepository
                .findLatestTotalPointsByName(name, org.springframework.data.domain.PageRequest.of(0, 1));
        return totalPointsList.isEmpty() ? 0 : totalPointsList.get(0);
    }

    /**
     * 获取用户的第二新存量积分
     *
     * @param name 用户姓名
     * @return 用户的第二新存量积分，如果没有记录则返回0
     */
    public Integer getSecondLatestTotalPoints(String name) {
        List<Integer> totalPointsList = rewardPenaltyRecordRepository
                .findLatestTotalPointsByName(name, org.springframework.data.domain.PageRequest.of(0, 2));
        return totalPointsList.size() < 2 ? 0 : totalPointsList.get(1);
    }
    
    /**
     * 统计用户最近3天的奖罚记录数量
     * 
     * @param name 用户姓名
     * @return 最近3天的记录数量
     */
    public Long countRecentRecords(String name) {
        // 计算7天前的时间
        LocalDateTime sevenDaysAgo = LocalDateTime.now().minusDays(3);
        String startTime = sevenDaysAgo.format(STANDARD_FORMATTER);
        
        logger.debug("统计用户 {} 从 {} 开始的奖罚记录数量", name, startTime);
        
        Long count = rewardPenaltyRecordRepository.countByNameAndOccurTimeAfter(name, startTime);
        return count != null ? count : 0L;
    }
}
