package com.mylog.controller.workflow;

import com.mylog.controller.BaseController;
import com.mylog.model.RewardPenaltyRecord;
import com.mylog.model.user.User;
import com.mylog.model.workflow.WorkflowInstance;
import com.mylog.service.ApprovalRecordService;
import com.mylog.service.MessageService;
import com.mylog.service.OptionsService;
import com.mylog.service.TaskService;
import com.mylog.service.UserActivityLogService;
import com.mylog.service.UserService;
import com.mylog.service.WorkflowInstanceService;
import com.mylog.service.WorkflowStepService;
import com.mylog.service.UserSessionTempFileTracker;
import com.mylog.util.SubmitFileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 审批操作控制器
 */
@Controller
@RequestMapping("/workflow/approval")
public class ApprovalController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(ApprovalController.class);

    @Autowired
    private WorkflowInstanceService instanceService;

    @Autowired
    private ApprovalRecordService recordService;

    @Autowired
    private WorkflowStepService stepService;

    @Autowired
    private UserService userService;

    @Autowired
    private UserActivityLogService activityLogService;

    @Autowired
    private TaskService taskService;

    @Autowired
    private MessageService messageService;

    @Autowired
    private OptionsService optionsService;

    @Autowired
    private com.mylog.service.RewardPenaltyRecordService rewardPenaltyRecordService;

    @Autowired
    private UserSessionTempFileTracker sessionTracker;

    @Autowired
    private com.mylog.util.WeixinMessageUtil weixinMessageUtil;



    @Value("${mylog.data.path:data}")
    private String dataPath;

    /**
     * 处理templateId参数，避免转换错误
     * 这个接口用于兼容请求中包含templateId参数但不需要使用的情况
     */
    @GetMapping("/handle-template")
    public String handleTemplateId(
            @RequestParam(value = "templateId", required = false) String templateId,
            @RequestParam(value = "instanceId", required = false) Long instanceId,
            RedirectAttributes redirectAttributes) {

        logger.info("收到包含templateId参数的请求，值为: {}, 实例ID: {}", templateId, instanceId);

        if (instanceId != null) {
            return "redirect:/workflow/approval/" + instanceId;
        } else {
            return "redirect:/workflow/instances";
        }
    }

    /**
     * 显示审批表单
     */
    @GetMapping("/{instanceId}")
    public String showApprovalForm(@PathVariable("instanceId") Long instanceId,
            @RequestParam(value = "templateId", required = false) Long templateId,
            Model model, RedirectAttributes redirectAttributes) {
        try {
            // 记录额外参数的存在
            if (templateId != null) {
                logger.debug("收到额外的templateId参数: {}", templateId);
            }

            Optional<WorkflowInstance> instanceOpt = instanceService.findInstanceById(instanceId);
            if (instanceOpt.isPresent()) {
                WorkflowInstance instance = instanceOpt.get();

                // 获取下一步骤信息
                // 根据流程实例判断下一步是否需要动态指定审批人
                boolean nextStepNeedsDynamicApprover = instanceService.isNextStepDynamicApprover(instance);

                // 判断提交状态下第一步是否需要动态指定审批人
                boolean firstStepNeedsDynamicApprover = false;
                if (instance.getStatus() == WorkflowInstance.WorkflowStatus.DRAFT) {
                    // 如果是草稿状态，查找第一个步骤的审批人配置
                    var steps = stepService.findStepsByTemplateId(instance.getTemplate().getTemplateId());
                    if (!steps.isEmpty()) {
                        // 按照顺序排序
                        steps.sort((s1, s2) -> s1.getStepOrder().compareTo(s2.getStepOrder()));
                        // 获取第一个步骤
                        var firstStep = steps.get(0);

                        // 判断第一步是否需要动态指定审批人
                        firstStepNeedsDynamicApprover = firstStep.getApproverConfig() == null
                                || firstStep.getApproverConfig().isEmpty()
                                || firstStep.getApproverConfig().equals("DYNAMIC");
                        logger.info("第一步是否需要动态指定审批人: {}", firstStepNeedsDynamicApprover);
                    }
                }

                // 计算当前步骤和总步骤数
                int currentStep = 0;
                int totalSteps = 0;

                // 直接从实例对象中读取总步骤数
                totalSteps = instance.getStepCount();
                logger.info("从数据库读取的步骤总数: {}", totalSteps);

                // 根据流程状态和审批记录计算当前步骤
                if (instance.getStatus() == WorkflowInstance.WorkflowStatus.DRAFT) {
                    currentStep = 0;
                } else {
                    // 计算已经完成的步骤数（提交 + 审批记录中的审批/拒绝操作）
                    var records = recordService.findRecordsByInstanceId(instanceId);
                    boolean hasSubmit = false;
                    int approvalSteps = 0;

                    for (var record : records) {
                        if (record.getAction() == com.mylog.model.workflow.ApprovalRecord.ApprovalAction.SUBMIT) {
                            hasSubmit = true;
                        } else if (record.getAction() == com.mylog.model.workflow.ApprovalRecord.ApprovalAction.APPROVE
                                || record
                                        .getAction() == com.mylog.model.workflow.ApprovalRecord.ApprovalAction.REJECT) {
                            approvalSteps++;
                        }
                    }

                    // 如果已经提交，当前步骤至少是1
                    currentStep = hasSubmit ? 1 : 0;
                    // 加上审批步骤数
                    currentStep += approvalSteps;
                }

                // 添加调试日志，输出流程撤回所需的条件信息
                logger.info("流程撤回调试信息 - 实例ID: {}, 状态: {}, 发起人: {}, 当前用户: {}, 当前步骤: {}",
                        instanceId,
                        instance.getStatus().name(),
                        instance.getInitiator(),
                        SecurityContextHolder.getContext().getAuthentication().getName(),
                        currentStep);

                // 将实例添加到模型
                model.addAttribute("instance", instance);
                model.addAttribute("activeMenu", "workflow");
                model.addAttribute("currentStep", currentStep);
                model.addAttribute("totalSteps", totalSteps);

                // 创建步骤标签映射，第1步是提交步骤，后续是审批步骤
                Map<Integer, String> stepLabels = new HashMap<>();

                // 第1步是提交步骤，标签为"提交"
                stepLabels.put(1, "提交: " + instance.getInitiator());

                // 获取完整步骤列表
                List<com.mylog.model.workflow.WorkflowStep> allSteps = new ArrayList<>();
                if (instance.getTemplate() != null) {
                    allSteps = stepService.findStepsByTemplateId(instance.getTemplate().getTemplateId());
                    allSteps.sort((s1, s2) -> s1.getStepOrder().compareTo(s2.getStepOrder()));
                }

                // 从审批记录中提取步骤审批人信息
                var records = recordService.findRecordsByInstanceId(instanceId);
                for (var record : records) {
                    if (record.getAction() == com.mylog.model.workflow.ApprovalRecord.ApprovalAction.APPROVE
                            || record.getAction() == com.mylog.model.workflow.ApprovalRecord.ApprovalAction.REJECT) {
                        // 找到对应的步骤序号
                        int stepIndex = 2; // 默认为1
                        if (record.getStep() != null) {
                            for (int i = 0; i < allSteps.size(); i++) {
                                if (allSteps.get(i).getStepId().equals(record.getStep().getStepId())) {
                                    stepIndex = i + 2; // +2是因为第1步是提交步骤
                                    break;
                                }
                            }
                        }

                        // 将审批人添加到步骤标签中
                        stepLabels.put(stepIndex,
                                record.getStep() != null ? record.getStep().getStepName() + ": " + record.getApprover()
                                        : "审批: " + record.getApprover());
                    }
                }

                // 对于还未完成的步骤，使用步骤名称作为标签
                for (int i = 0; i < allSteps.size(); i++) {
                    int stepIndex = i + 2; // +2是因为第1步是提交步骤
                    if (!stepLabels.containsKey(stepIndex) && stepIndex <= totalSteps) {
                        var step = allSteps.get(i);
                        String approverInfo = "";

                        if (step.getStepId().equals(instance.getCurrentStepId())) {
                            // 如果步骤是实例中的当前步骤，则从实例中读取当前审批人
                            approverInfo = ": "
                                    + (instance.getCurrentApprover() != null ? instance.getCurrentApprover() : "待定");
                        } else {
                            // 如果步骤不是实例中的当前步骤，则从步骤配置中读取审批人
                            approverInfo = ": "
                                    + ((step.getApproverConfig() != null && !step.getApproverConfig().trim().isEmpty())
                                            ? step.getApproverConfig()
                                            : "待定");
                        }

                        stepLabels.put(stepIndex, step.getStepName() + approverInfo);
                    }
                }

                // 创建步骤时间信息映射
                Map<Integer, String> stepTimes = new HashMap<>();
                // 创建步骤意见信息映射
                Map<Integer, String> stepComments = new HashMap<>();

                // 为提交步骤添加时间（步骤1）
                if (instance.getSubmittedDateTime() != null) {
                    stepTimes.put(1,
                            instance.getSubmittedDateTime().format(DateTimeFormatter.ofPattern("MM-dd HH:mm")));
                }

                // 查找提交记录的意见
                for (var record : records) {
                    if (record.getAction() == com.mylog.model.workflow.ApprovalRecord.ApprovalAction.SUBMIT) {
                        if (record.getComment() != null && !record.getComment().trim().isEmpty()) {
                            stepComments.put(1, record.getComment());
                        }
                        break;
                    }
                }

                // 为审批步骤添加时间和意见
                for (var record : records) {
                    if (record.getAction() == com.mylog.model.workflow.ApprovalRecord.ApprovalAction.APPROVE
                            || record.getAction() == com.mylog.model.workflow.ApprovalRecord.ApprovalAction.REJECT) {
                        // 找到对应的步骤序号
                        int stepIndex = 2; // 默认为2
                        if (record.getStep() != null) {
                            for (int i = 0; i < allSteps.size(); i++) {
                                if (allSteps.get(i).getStepId().equals(record.getStep().getStepId())) {
                                    stepIndex = i + 2; // +2是因为第1步是提交步骤
                                    break;
                                }
                            }
                        }

                        // 添加审批时间
                        if (record.getCreatedDateTime() != null) {
                            stepTimes.put(stepIndex,
                                    record.getCreatedDateTime().format(DateTimeFormatter.ofPattern("MM-dd HH:mm")));
                        }

                        // 添加审批意见
                        if (record.getComment() != null && !record.getComment().trim().isEmpty()) {
                            stepComments.put(stepIndex, record.getComment());
                        }
                    }
                }

                // 添加步骤标签、时间和意见到模型
                model.addAttribute("stepLabels", stepLabels);
                model.addAttribute("stepTimes", stepTimes);
                model.addAttribute("stepComments", stepComments);

                // 添加是否需要动态指定审批人的标志
                model.addAttribute("nextStepNeedsDynamicApprover", nextStepNeedsDynamicApprover);
                model.addAttribute("firstStepNeedsDynamicApprover", firstStepNeedsDynamicApprover);

                // 添加人员列表数据，用于审批人选择
                List<String> personnelList = optionsService.getPersonnel();
                model.addAttribute("personnel", personnelList);

                // 判断是否为任务类型的最后一个审批步骤
                boolean isTaskLastStep = false;
                if ("任务".equals(instance.getBusinessType()) &&
                        instance.getStatus() == WorkflowInstance.WorkflowStatus.PROCESSING) {
                    // 检查当前步骤是否为最后一步
                    if (currentStep == totalSteps - 1) {
                        isTaskLastStep = true;
                        logger.info("当前为任务类型的最后一个审批步骤，实例ID: {}", instanceId);
                    }
                }
                model.addAttribute("isTaskLastStep", isTaskLastStep);

                // 调试信息：输出人员列表
                logger.info("========== 调试信息 ==========");
                logger.info("传递人员列表数据到视图，数量: {}", personnelList.size());
                int count = 0;
                for (String person : personnelList) {
                    if (count < 10) { // 只显示前10个，避免日志过长
                        logger.info("人员[{}]: {}", count, person);
                    }
                    count++;
                }
                logger.info("===============================");

                return "workflow/approval/form";
            } else {
                redirectAttributes.addFlashAttribute("error", "流程实例不存在");
                return "redirect:/workflow/instances/todo";
            }
        } catch (Exception e) {
            logger.error("显示审批表单时出错: {}", e.getMessage(), e);
            redirectAttributes.addFlashAttribute("error", "显示审批表单失败: " + e.getMessage());
            return "redirect:/workflow/instances/todo";
        }
    }

    /**
     * 提交流程实例
     */
    @PostMapping("/submit")
    public String submitInstance(
            @RequestParam("instanceId") Long instanceId,
            @RequestParam("comment") String comment,
            @RequestParam(value = "nextApprover", required = false) String nextApprover,
            RedirectAttributes redirectAttributes) {

        try {
            // 获取当前登录用户
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String currentUsername = authentication.getName();

            // 提交流程
            WorkflowInstance instance = instanceService.submitInstance(instanceId, currentUsername, comment,
                    null, nextApprover);

            // nextApprover 参数现在由服务层处理，不需要在控制器层手动更新
            if (nextApprover != null && !nextApprover.isEmpty()) {
                logger.info("提交流程时指定了第一步审批人: {}，已由服务层处理", nextApprover);
            }

            // 记录活动日志
            Optional<User> currentUser = userService.findUserByUsername(currentUsername);
            if (currentUser.isPresent()) {
                String ipAddress = getClientIpAddress();
                activityLogService.logUpdate(
                        currentUser.get().getUserId(),
                        currentUsername,
                        "提交流程: " + instance.getTitle(),
                        ipAddress,
                        "WorkflowInstance",
                        instanceId,
                        getAccessType());
            }

            redirectAttributes.addFlashAttribute("message", "流程提交成功");
            return "redirect:/workflow/instances/" + instanceId;
        } catch (Exception e) {
            logger.error("提交流程实例时出错: {}", e.getMessage(), e);
            redirectAttributes.addFlashAttribute("error", "提交流程失败: " + e.getMessage());
            return "redirect:/workflow/instances/" + instanceId;
        }
    }

    /**
     * 审批流程实例
     */
    @PostMapping("/approve")
    public String approveInstance(
            @RequestParam("instanceId") Long instanceId,
            @RequestParam("comment") String comment,
            @RequestParam(value = "nextApprover", required = false) String nextApprover,
            @RequestParam(value = "completeTaskAfterApproval", required = false, defaultValue = "false") boolean completeTaskAfterApproval,
            @RequestParam(value = "tempFilePath1", required = false) String tempFilePath1,
            @RequestParam(value = "tempFilePath2", required = false) String tempFilePath2,
            @RequestParam(value = "fileName1", required = false) String fileName1,
            @RequestParam(value = "fileName2", required = false) String fileName2,
            HttpServletRequest request,
            RedirectAttributes redirectAttributes) {

        try {
            // 获取当前登录用户
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String currentUsername = authentication.getName();

            // 获取流程实例以获取标题
            Optional<WorkflowInstance> instanceOpt = instanceService.findInstanceById(instanceId);
            if (!instanceOpt.isPresent()) {
                redirectAttributes.addFlashAttribute("error", "流程实例不存在");
                return "redirect:/workflow/instances/" + instanceId;
            }

            WorkflowInstance workflowInstance = instanceOpt.get();
            String instanceTitle = workflowInstance.getTitle() != null ? workflowInstance.getTitle() : "未知流程";

            // 记录审批请求的详细信息
            logger.info("审批请求详情 - 实例ID: {}, 标题: {}, 审批人: {}", instanceId, instanceTitle, currentUsername);
            logger.info("文件参数 - tempFilePath1: {}, fileName1: {}, tempFilePath2: {}, fileName2: {}",
                    tempFilePath1, fileName1, tempFilePath2, fileName2);

            // 检查是否有文件需要处理
            boolean hasFiles = (tempFilePath1 != null && !tempFilePath1.isEmpty() && fileName1 != null
                    && !fileName1.isEmpty()) ||
                    (tempFilePath2 != null && !tempFilePath2.isEmpty() && fileName2 != null && !fileName2.isEmpty());

            if (hasFiles) {
                logger.info("发现需要处理的审批附件，开始处理...");
            } else {
                logger.info("此次审批没有附件文件，跳过文件处理");
            }

            // 获取会话ID
            String sessionId = request.getSession().getId();

            // 处理审批附件
            String attachments = processApprovalAttachments(tempFilePath1, tempFilePath2,
                    fileName1, fileName2,
                    instanceTitle, instanceId.toString(), sessionId);

            if (hasFiles) {
                logger.info("文件处理完成，附件路径: {}", attachments);
            }

            // 审批流程
            WorkflowInstance instance = instanceService.approveInstance(instanceId, currentUsername, comment,
                    attachments, nextApprover);

            // 记录活动日志
            Optional<User> currentUser = userService.findUserByUsername(currentUsername);
            if (currentUser.isPresent()) {
                String ipAddress = getClientIpAddress();
                activityLogService.logUpdate(
                        currentUser.get().getUserId(),
                        currentUsername,
                        "审批通过流程: " + instance.getTitle(),
                        ipAddress,
                        "WorkflowInstance",
                        instanceId,
                        getAccessType());
            }

            // 如果是任务审批流程，更新任务的审批状态
            if ("任务".equals(instance.getBusinessType()) && instance.getBusinessId() != null) {
                try {
                    // 检查当前是否为最后一步审批
                    boolean isLastStep = false;

                    // 判断当前步骤是否为最后一步
                    if (instance.getStatus() == WorkflowInstance.WorkflowStatus.APPROVED) {
                        isLastStep = true;
                    }

                    // 只有在最后一步时才更新任务审批状态
                    if (isLastStep) {
                        // 根据开关状态决定审批状态参数
                        int approvalStatus = completeTaskAfterApproval ? 2 : 4;
                        String statusDescription = completeTaskAfterApproval ? "审批通过并完成" : "审批通过";

                        // 更新任务审批状态
                        taskService.updateTaskApprovalStatus(instance.getBusinessId(), approvalStatus, instanceId,
                                currentUsername, comment);
                        logger.info("已更新任务 {} 的审批状态为 {} (状态码: {})",
                                instance.getBusinessId(), statusDescription, approvalStatus);
                    }
                } catch (Exception e) {
                    logger.error("更新任务审批状态时出错: {}", e.getMessage(), e);
                    // 继续执行，不影响流程审批
                }
            }

            // 给流程发起人发送消息
            try {

                // 如果流程实例的标题中包含“费用”,“奖励”,"惩罚"，则创建penaltyRecord并保存。
                // 判断是最后一步审批时才执行

                if (instance.getStatus() == WorkflowInstance.WorkflowStatus.APPROVED) {
                    if (instance.getTitle() != null && (instance.getTitle().contains("费用") ||
                            instance.getTitle().contains("奖励") ||
                            instance.getTitle().contains("惩罚"))) {
                        // 处理惩罚相关的逻辑
                        // 这里可以添加处理惩罚的代码

                        int penaltyPoints;
                        // 从流程实例的备注3中解析扣分数量保存到penaltyPoints中
                        if (instance.getRemark3() != null && !instance.getRemark3().isEmpty()) {
                            try {
                                // 使用正则表达式提取第一组出现的数字（支持正负数和最多2位小数）
                                String remark3 = instance.getRemark3();
                                java.util.regex.Pattern pattern = java.util.regex.Pattern
                                        .compile("([+-]?\\d+(?:\\.\\d{1,2})?)");
                                java.util.regex.Matcher matcher = pattern.matcher(remark3);

                                if (matcher.find()) {
                                    String numberStr = matcher.group(1);
                                    // 如果是小数，先转换为double再转为int（四舍五入）
                                    if (numberStr.contains(".")) {
                                        double doubleValue = Double.parseDouble(numberStr);
                                        penaltyPoints = (int) Math.round(doubleValue);
                                    } else {
                                        penaltyPoints = Integer.parseInt(numberStr);
                                    }
                                    logger.info("从备注3 '{}' 中解析出数字: {} -> 积分: {}", remark3, numberStr, penaltyPoints);
                                } else {
                                    logger.warn("备注3 '{}' 中未找到有效数字，使用默认值0", remark3);
                                    penaltyPoints = 0;
                                }
                            } catch (NumberFormatException e) {
                                logger.error("解析扣分数量失败，使用默认值0: {}", e.getMessage());
                                penaltyPoints = 0; // 如果解析失败，使用默认值0
                            }
                        } else {
                            penaltyPoints = 0; // 如果备注3为空，使用默认值0
                        }

                        // 处理人员字段，支持多人情况
                        String staff = instance.getStaff();
                        List<String> staffList = new ArrayList<>();

                        if (staff != null && !staff.trim().isEmpty()) {
                            // 按分号或逗号分割人员名单
                            String[] staffArray = staff.split("[;,]");
                            for (String person : staffArray) {
                                String trimmedPerson = person.trim();
                                if (!trimmedPerson.isEmpty()) {
                                    staffList.add(trimmedPerson);
                                }
                            }
                        }

                        // 如果没有解析到有效人员，跳过记录创建
                        if (staffList.isEmpty()) {
                            logger.warn("流程实例 {} 的人员字段为空或无效，跳过奖罚记录创建", instance.getInstanceId());
                        } else {
                            // 计算每个人员的均分积分（总积分除以人数）
                            int personCount = staffList.size();
                            int pointsPerPerson = penaltyPoints / personCount;
                            int remainder = penaltyPoints % personCount; // 余数

                            logger.info("总积分: {}, 人员数量: {}, 每人分配: {}, 余数: {}",
                                    penaltyPoints, personCount, pointsPerPerson, remainder);

                            // 为每个人员创建单独的奖罚记录
                            for (int i = 0; i < staffList.size(); i++) {
                                String personName = staffList.get(i);
                                RewardPenaltyRecord penaltyRecord = new RewardPenaltyRecord();
                                penaltyRecord.setName(personName);
                                penaltyRecord.setType(instance.getTitle());

                                // 构建原因信息：在原因前面加上所在地和相关人员信息
                                String baseReason = instance.getRemark4() != null && !instance.getRemark4().isEmpty()
                                        ? instance.getRemark4()
                                        : "无原因";

                                String locationInfo = instance.getStartLocation() != null
                                        && !instance.getStartLocation().isEmpty()
                                                ? instance.getStartLocation()
                                                : "";

                                String staffInfo = instance.getStaff() != null && !instance.getStaff().isEmpty()
                                        ? instance.getStaff()
                                        : "";

                                // 构建完整的原因信息
                                StringBuilder reasonBuilder = new StringBuilder();

                                if (!locationInfo.isEmpty()) {
                                    reasonBuilder.append(locationInfo);
                                }

                                if (!staffInfo.isEmpty()) {
                                    if (reasonBuilder.length() > 0) {
                                        reasonBuilder.append(" - ");
                                    }
                                    reasonBuilder.append(staffInfo);
                                }

                                if (reasonBuilder.length() > 0) {
                                    reasonBuilder.append(" - ");
                                }
                                reasonBuilder.append(baseReason);
                                // 在原因最后增加流程ID
                                reasonBuilder.append(" - 流程ID：").append(instance.getInstanceId());
                                String reason = reasonBuilder.toString();

                                penaltyRecord.setReason(reason);
                                penaltyRecord.setRemarks("通过流程自动创建积分记录(均分)，发起人：" + instance.getInitiator());

                                // 计算当前人员的积分：基础积分 + （如果是第一个人则加上余数）
                                int currentPersonPoints = pointsPerPerson + (i == 0 ? remainder : 0);
                                penaltyRecord.setPoints(currentPersonPoints);

                                // 使用服务保存记录（自动计算存量积分）
                                rewardPenaltyRecordService.createRecordWithTotalPoints(penaltyRecord);

                                logger.info("为人员 {} 创建了费用记录，类型: {}, 积分: {} (均分后)", personName, instance.getTitle(),
                                        currentPersonPoints);
                            }

                            // 如果流程标题包含"费用"，额外创建一个积分为0-penaltyPoints的奖罚记录
                            if (instance.getTitle().contains("费用")) {
                                try {
                                    // 添加1秒延时再创建附加记录
                                    Thread.sleep(1000);
                                } catch (InterruptedException e) {
                                    Thread.currentThread().interrupt();
                                }

                                RewardPenaltyRecord additionalRecord = new RewardPenaltyRecord();
                                // 使用流程发起人作为记录对象
                                additionalRecord.setName(instance.getInitiator());
                                additionalRecord.setType(instance.getTitle() + "(费用对冲)");

                                // 构建原因信息：在原因前面加上所在地和相关人员信息
                                String baseReason = instance.getRemark4() != null && !instance.getRemark4().isEmpty()
                                        ? instance.getRemark4()
                                        : "无原因";

                                String locationInfo = instance.getStartLocation() != null
                                        && !instance.getStartLocation().isEmpty()
                                                ? instance.getStartLocation()
                                                : "";

                                String staffInfo = instance.getStaff() != null && !instance.getStaff().isEmpty()
                                        ? instance.getStaff()
                                        : "";

                                // 构建完整的原因信息
                                StringBuilder reasonBuilder = new StringBuilder();

                                if (!locationInfo.isEmpty()) {
                                    reasonBuilder.append(locationInfo);
                                }

                                if (!staffInfo.isEmpty()) {
                                    if (reasonBuilder.length() > 0) {
                                        reasonBuilder.append(" - ");
                                    }
                                    reasonBuilder.append(staffInfo);
                                }

                                if (reasonBuilder.length() > 0) {
                                    reasonBuilder.append(" - ");
                                }
                                reasonBuilder.append(baseReason);

                                String reason = reasonBuilder.toString();

                                additionalRecord.setReason(reason);
                                additionalRecord.setRemarks("通过流程自动创建费用对冲记录");

                                // 设置积分为0-penaltyPoints（即负的总积分）
                                int offsetPoints = 0 - penaltyPoints;
                                additionalRecord.setPoints(offsetPoints);

                                // 使用服务保存记录（自动计算存量积分）
                                rewardPenaltyRecordService.createRecordWithTotalPoints(additionalRecord);

                                logger.info("为费用流程创建了对冲记录，人员: {}, 类型: {}, 积分: {}",
                                        staffList.get(0), additionalRecord.getType(), offsetPoints);
                            }
                        }
                    }
                }

                // 只有在最后一步审批通过时才发送消息
                if (instance.getStatus() == WorkflowInstance.WorkflowStatus.APPROVED) {
                    String initiator = instance.getInitiator();
                    if (initiator != null && !initiator.isEmpty()) {
                        // 创建系统消息
                        com.mylog.model.Message message = new com.mylog.model.Message();
                        message.setReceiver(initiator);
                        message.setMessageTitle("流程审批通过");

                        // 构建消息内容
                        StringBuilder contentBuilder = new StringBuilder();
                        String instanceLink = String.format("<a href=\"/workflow/instances/%d\" target=\"_blank\">%s</a>", 
                            instanceId, instance.getTitle());
                        contentBuilder.append("您发起的流程 ").append(instanceLink).append(" 已被 ")
                                .append(currentUsername).append(" 审批通过。");


                        message.setMessageContent(contentBuilder.toString());
                        message.setRelatedType("WorkflowInstance");
                        message.setRelatedId(instanceId);
                        messageService.saveMessage(message);

                        // 发送企业微信消息
                        // 查找接收者的weixinID
                        String weixinID = "";
                        Optional<User> receiverUser = userService.findUserByUsername(initiator);
                        if (receiverUser.isPresent() && receiverUser.get().getWeixinID() != null
                                && !receiverUser.get().getWeixinID().isEmpty()) {
                            weixinID = receiverUser.get().getWeixinID();
                            logger.debug("找到用户 {} 的微信ID: {}", initiator, weixinID);
                        } else {
                            logger.warn("未找到用户 {} 的微信ID，使用用户名代替", initiator);
                            weixinID = initiator; // 如果没有weixinID，则使用用户名
                        }

                        // 构建微信消息内容
                        StringBuilder weixinContentBuilder = new StringBuilder();
                        String instanceMarkdownLink = String.format("[%s](https://prj.cpolar.cn/workflow/instances/%d)", 
                            instance.getTitle(), instanceId);
                        weixinContentBuilder.append("<@").append(weixinID).append("> 您发起的流程 ")
                                .append(instanceMarkdownLink).append(" 已被 ")
                                .append(" 审批通过。");


                        List<String> mentionList = new ArrayList<>();
                        mentionList.add(initiator);
                        weixinMessageUtil.sendWeixinMessage("流程审批通过",
                        weixinContentBuilder.toString(), mentionList);
                        

                        logger.info("已发送审批通过消息给流程发起人: {}", initiator);
                    }
                } else {
                    logger.info("流程尚未完全通过（状态：{}），跳过发送审批通过消息", instance.getStatus());
                }
            } catch (Exception e) {
                logger.error("发送审批通过消息时出错: {}", e.getMessage(), e);
                // 继续执行，不影响流程审批
            }

            redirectAttributes.addFlashAttribute("message", "审批通过成功");
            // 直接跳转到流程详情页
            return "redirect:/workflow/instances/" + instanceId;
        } catch (Exception e) {
            logger.error("审批流程实例时出错: {}", e.getMessage(), e);
            redirectAttributes.addFlashAttribute("error", "审批失败: " + e.getMessage());
            return "redirect:/workflow/instances/" + instanceId;
        }
    }

    /**
     * 拒绝流程实例
     */
    @PostMapping("/reject")
    public String rejectInstance(
            @RequestParam("instanceId") Long instanceId,
            @RequestParam("comment") String comment,
            @RequestParam(value = "tempFilePath1", required = false) String tempFilePath1,
            @RequestParam(value = "tempFilePath2", required = false) String tempFilePath2,
            @RequestParam(value = "fileName1", required = false) String fileName1,
            @RequestParam(value = "fileName2", required = false) String fileName2,
            HttpServletRequest request,
            RedirectAttributes redirectAttributes) {

        try {
            // 获取当前登录用户
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String currentUsername = authentication.getName();

            // 获取流程实例以获取标题
            Optional<WorkflowInstance> instanceOpt = instanceService.findInstanceById(instanceId);
            if (!instanceOpt.isPresent()) {
                redirectAttributes.addFlashAttribute("error", "流程实例不存在");
                return "redirect:/workflow/instances/" + instanceId;
            }

            WorkflowInstance workflowInstance = instanceOpt.get();
            String instanceTitle = workflowInstance.getTitle() != null ? workflowInstance.getTitle() : "未知流程";

            // 记录拒绝请求的详细信息
            logger.info("拒绝请求详情 - 实例ID: {}, 标题: {}, 审批人: {}", instanceId, instanceTitle, currentUsername);
            logger.info("文件参数 - tempFilePath1: {}, fileName1: {}, tempFilePath2: {}, fileName2: {}",
                    tempFilePath1, fileName1, tempFilePath2, fileName2);

            // 获取会话ID
            String sessionId = request.getSession().getId();

            // 处理审批附件
            String attachments = processApprovalAttachments(tempFilePath1, tempFilePath2,
                    fileName1, fileName2,
                    instanceTitle, instanceId.toString(), sessionId);

            logger.info("文件处理完成，附件路径: {}", attachments);

            // 拒绝流程
            WorkflowInstance instance = instanceService.rejectInstance(instanceId, currentUsername, comment,
                    attachments);

            // 记录活动日志
            Optional<User> currentUser = userService.findUserByUsername(currentUsername);
            if (currentUser.isPresent()) {
                String ipAddress = getClientIpAddress();
                activityLogService.logUpdate(
                        currentUser.get().getUserId(),
                        currentUsername,
                        "拒绝流程: " + instance.getTitle(),
                        ipAddress,
                        "WorkflowInstance",
                        instanceId,
                        getAccessType());
            }

            // 如果是任务审批流程，更新任务的审批状态
            if ("任务".equals(instance.getBusinessType()) && instance.getBusinessId() != null) {
                try {
                    // 更新任务审批状态为"审批拒绝"
                    taskService.updateTaskApprovalStatus(instance.getBusinessId(), 3, instanceId, currentUsername, comment);
                    logger.info("已更新任务 {} 的审批状态为审批拒绝", instance.getBusinessId());
                } catch (Exception e) {
                    logger.error("更新任务审批状态时出错: {}", e.getMessage(), e);
                    // 继续执行，不影响流程审批
                }
            }

            // 给流程发起人发送消息
            try {
                String initiator = instance.getInitiator();
                if (initiator != null && !initiator.isEmpty()) {
                    // 创建系统消息
                    com.mylog.model.Message message = new com.mylog.model.Message();
                    message.setReceiver(initiator);
                    message.setMessageTitle("流程审批拒绝");

                    // 构建消息内容
                    StringBuilder contentBuilder = new StringBuilder();
                    String instanceLink = String.format("<a href=\"/workflow/instances/%d\" target=\"_blank\">%s</a>", 
                        instanceId, instance.getTitle());
                    contentBuilder.append("您发起的流程 ").append(instanceLink).append(" 已被 ")
                            .append(currentUsername).append(" 拒绝。");

                    // 添加拒绝原因
                    if (comment != null && !comment.isEmpty()) {
                        contentBuilder.append("<br>拒绝原因: ").append(comment);
                    }


                    message.setMessageContent(contentBuilder.toString());
                    message.setRelatedType("WorkflowInstance");
                    message.setRelatedId(instanceId);
                    messageService.saveMessage(message);

                    // 发送企业微信消息
                    // 查找接收者的weixinID
                    String weixinID = "";
                    Optional<User> receiverUser = userService.findUserByUsername(initiator);
                    if (receiverUser.isPresent() && receiverUser.get().getWeixinID() != null
                            && !receiverUser.get().getWeixinID().isEmpty()) {
                        weixinID = receiverUser.get().getWeixinID();
                        logger.debug("找到用户 {} 的微信ID: {}", initiator, weixinID);
                    } else {
                        logger.warn("未找到用户 {} 的微信ID，使用用户名代替", initiator);
                        weixinID = initiator; // 如果没有weixinID，则使用用户名
                    }

                    // 构建微信消息内容
                    StringBuilder weixinContentBuilder = new StringBuilder();
                    String instanceMarkdownLink = String.format("[%s](https://prj.cpolar.cn/workflow/instances/%d)", 
                        instance.getTitle(), instanceId);
                    weixinContentBuilder.append("<@").append(weixinID).append("> 您发起的流程 ")
                            .append(instanceMarkdownLink).append(" 已被 ")
                            .append(currentUsername).append(" 拒绝。");

                    // 添加拒绝原因
                    if (comment != null && !comment.isEmpty()) {
                        weixinContentBuilder.append("\n\n拒绝原因: ").append(comment);
                    }

                    List<String> mentionList = new ArrayList<>();
                    mentionList.add(initiator);
                    weixinMessageUtil.sendWeixinMessage("流程审批拒绝",
                    weixinContentBuilder.toString(), mentionList);

                    logger.info("已发送审批拒绝消息给流程发起人: {}", initiator);
                }
            } catch (Exception e) {
                logger.error("发送审批拒绝消息时出错: {}", e.getMessage(), e);
                // 继续执行，不影响流程审批
            }

            redirectAttributes.addFlashAttribute("message", "流程已拒绝");
            // 直接跳转到流程详情页
            return "redirect:/workflow/instances/" + instanceId;
        } catch (Exception e) {
            logger.error("拒绝流程实例时出错: {}", e.getMessage(), e);
            redirectAttributes.addFlashAttribute("error", "拒绝流程失败: " + e.getMessage());
            return "redirect:/workflow/instances/" + instanceId;
        }
    }

    /**
     * 转交流程实例
     */
    @PostMapping("/transfer")
    public String transferInstance(
            @RequestParam("instanceId") Long instanceId,
            @RequestParam("toUsername") String toUsername,
            @RequestParam("comment") String comment,
            RedirectAttributes redirectAttributes) {

        try {
            // 获取当前登录用户
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String currentUsername = authentication.getName();

            // 转交流程
            WorkflowInstance instance = instanceService.transferInstance(instanceId, currentUsername, toUsername,
                    comment);

            // 记录活动日志
            Optional<User> currentUser = userService.findUserByUsername(currentUsername);
            if (currentUser.isPresent()) {
                String ipAddress = getClientIpAddress();
                activityLogService.logUpdate(
                        currentUser.get().getUserId(),
                        currentUsername,
                        "转交流程给 " + toUsername + ": " + instance.getTitle(),
                        ipAddress,
                        "WorkflowInstance",
                        instanceId,
                        getAccessType());
            }

            redirectAttributes.addFlashAttribute("message", "流程已转交给 " + toUsername);
            // 直接跳转到流程详情页
            return "redirect:/workflow/instances/" + instanceId;
        } catch (Exception e) {
            logger.error("转交流程实例时出错: {}", e.getMessage(), e);
            redirectAttributes.addFlashAttribute("error", "转交流程失败: " + e.getMessage());
            return "redirect:/workflow/instances/" + instanceId;
        }
    }

    /**
     * 撤回流程实例
     */
    @PostMapping("/withdraw")
    public String withdrawInstance(
            @RequestParam("instanceId") Long instanceId,
            @RequestParam("comment") String comment,
            RedirectAttributes redirectAttributes) {

        try {
            // 获取当前登录用户
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String currentUsername = authentication.getName();

            // 撤回流程
            WorkflowInstance instance = instanceService.withdrawInstance(instanceId, currentUsername, comment);

            // 记录活动日志
            Optional<User> currentUser = userService.findUserByUsername(currentUsername);
            if (currentUser.isPresent()) {
                String ipAddress = getClientIpAddress();
                activityLogService.logUpdate(
                        currentUser.get().getUserId(),
                        currentUsername,
                        "撤回流程: " + instance.getTitle(),
                        ipAddress,
                        "WorkflowInstance",
                        instanceId,
                        getAccessType());
            }

            redirectAttributes.addFlashAttribute("message", "流程已撤回");
            return "redirect:/workflow/instances/" + instanceId;
        } catch (Exception e) {
            logger.error("撤回流程实例时出错: {}", e.getMessage(), e);
            redirectAttributes.addFlashAttribute("error", "撤回流程失败: " + e.getMessage());
            return "redirect:/workflow/instances/" + instanceId;
        }
    }

    /**
     * 终止流程实例
     */
    @PostMapping("/terminate")
    @PreAuthorize("hasRole('ADMIN')")
    public String terminateInstance(
            @RequestParam("instanceId") Long instanceId,
            @RequestParam("comment") String comment,
            RedirectAttributes redirectAttributes) {

        try {
            // 获取当前登录用户
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String currentUsername = authentication.getName();

            // 终止流程
            WorkflowInstance instance = instanceService.terminateInstance(instanceId, currentUsername, comment);

            // 记录活动日志
            Optional<User> currentUser = userService.findUserByUsername(currentUsername);
            if (currentUser.isPresent()) {
                String ipAddress = getClientIpAddress();
                activityLogService.logUpdate(
                        currentUser.get().getUserId(),
                        currentUsername,
                        "终止流程: " + instance.getTitle(),
                        ipAddress,
                        "WorkflowInstance",
                        instanceId,
                        getAccessType());
            }

            redirectAttributes.addFlashAttribute("message", "流程已终止");
            return "redirect:/workflow/instances/" + instanceId;
        } catch (Exception e) {
            logger.error("终止流程实例时出错: {}", e.getMessage(), e);
            redirectAttributes.addFlashAttribute("error", "终止流程失败: " + e.getMessage());
            return "redirect:/workflow/instances/" + instanceId;
        }
    }

    /**
     * 调试页面 - 测试文件上传功能
     */
    @GetMapping("/debug-upload")
    public String debugUpload(Model model) {
        logger.info("访问文件上传调试页面");
        return "debug-upload";
    }

    /**
     * Tab结构测试页面
     */
    @GetMapping("/test-tab-structure")
    public String testTabStructure(Model model) {
        logger.info("访问Tab结构测试页面");
        return "test-tab-structure";
    }

    /**
     * 处理审批附件文件
     * 将临时文件移动到永久存储位置并返回附件路径字符串
     */
    private String processApprovalAttachments(String tempFilePath1, String tempFilePath2,
            String fileName1, String fileName2,
            String instanceTitle, String processId, String sessionId) {
        logger.info("开始处理审批附件 - 流程: {}, 流程ID: {}, 会话: {}", instanceTitle, processId, sessionId);
        logger.info("文件参数 - 文件1: {} ({}), 文件2: {} ({})", tempFilePath1, fileName1, tempFilePath2, fileName2);

        List<String> attachmentPaths = new ArrayList<>();
        List<String> processedFiles = new ArrayList<>();

        try {
            // 确保流程附件目录存在
            Path attachmentDir = Paths.get(dataPath, SubmitFileUtils.WORKFLOW_ATTACHMENT_DIR);
            if (!Files.exists(attachmentDir)) {
                Files.createDirectories(attachmentDir);
                logger.info("创建流程附件目录: {}", attachmentDir);
            }

            // 处理文件1
            if (tempFilePath1 != null && !tempFilePath1.isEmpty() && fileName1 != null && !fileName1.isEmpty()) {
                logger.info("准备处理文件1: {} -> {}", tempFilePath1, fileName1);
                boolean success = processApprovalFile(tempFilePath1, fileName1, instanceTitle, processId, 1,
                        attachmentPaths);
                if (success) {
                    processedFiles.add("文件1: " + fileName1);
                    // 从会话跟踪器中移除已处理的文件
                    sessionTracker.removeTempFile(sessionId, tempFilePath1);
                    logger.info("文件1处理成功并从会话跟踪器移除");
                } else {
                    logger.error("文件1处理失败: {} -> {}", tempFilePath1, fileName1);
                }
            }

            // 处理文件2
            if (tempFilePath2 != null && !tempFilePath2.isEmpty() && fileName2 != null && !fileName2.isEmpty()) {
                logger.info("准备处理文件2: {} -> {}", tempFilePath2, fileName2);
                boolean success = processApprovalFile(tempFilePath2, fileName2, instanceTitle, processId, 2,
                        attachmentPaths);
                if (success) {
                    processedFiles.add("文件2: " + fileName2);
                    // 从会话跟踪器中移除已处理的文件
                    sessionTracker.removeTempFile(sessionId, tempFilePath2);
                    logger.info("文件2处理成功并从会话跟踪器移除");
                } else {
                    logger.error("文件2处理失败: {} -> {}", tempFilePath2, fileName2);
                }
            }

            if (!processedFiles.isEmpty()) {
                logger.info("审批附件处理完成 - 流程: {}, 流程ID: {}, 处理文件: {}",
                        instanceTitle, processId, String.join(", ", processedFiles));
            }

        } catch (IOException e) {
            logger.error("创建附件目录时出错: {}", e.getMessage(), e);
        }

        // 用分号连接所有附件路径
        String result = String.join(";", attachmentPaths);
        logger.info("审批附件处理完成 - 返回路径: {}, 处理的文件: {}", result, processedFiles);
        return result;
    }

    /**
     * 处理单个审批文件
     */
    private boolean processApprovalFile(String tempFilePath, String fileName, String instanceTitle,
            String processId, int fileNumber, List<String> attachmentPaths) {
        try {
            Path tempPath = Paths.get(tempFilePath);
            if (!Files.exists(tempPath)) {
                logger.warn("临时文件不存在，跳过处理: {}", tempFilePath);
                return false;
            }

            // 验证文件大小
            long fileSize = Files.size(tempPath);
            if (fileSize == 0) {
                logger.warn("临时文件为空，跳过处理: {}", tempFilePath);
                return false;
            }

            String fileExtension = "";
            if (fileName.contains(".")) {
                fileExtension = fileName.substring(fileName.lastIndexOf("."));
            }

            Path permanentPath = SubmitFileUtils.getWorkflowAttachmentPath(
                    dataPath, instanceTitle, processId, fileNumber, fileExtension);

            // 确保目标目录存在
            Path parentDir = permanentPath.getParent();
            if (!Files.exists(parentDir)) {
                Files.createDirectories(parentDir);
            }

            // 复制文件到永久位置
            logger.info("开始复制文件: {} -> {}", tempPath, permanentPath);
            Files.copy(tempPath, permanentPath);
            attachmentPaths.add(permanentPath.toString());
            logger.info("文件复制完成，开始验证...");

            // 验证复制结果
            if (Files.exists(permanentPath) && Files.size(permanentPath) == fileSize) {
                // 删除临时文件
                Files.deleteIfExists(tempPath);
                logger.info("审批附件{}处理成功: {} -> {} (大小: {} bytes)",
                        fileNumber, tempPath.getFileName(), permanentPath.getFileName(), fileSize);
                return true;
            } else {
                logger.error("文件复制验证失败: {} (存在: {}, 大小匹配: {})",
                        permanentPath, Files.exists(permanentPath),
                        Files.exists(permanentPath) ? Files.size(permanentPath) == fileSize : false);
                return false;
            }

        } catch (IOException e) {
            logger.error("处理审批附件{}时出错: {}, 临时文件: {}", fileNumber, e.getMessage(), tempFilePath, e);
            return false;
        }
    }
    
}
