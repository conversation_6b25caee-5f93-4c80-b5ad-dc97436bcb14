package com.mylog.service;

import com.mylog.model.SubTask;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface SubTaskService {
    /**
     * 统计所有状态为“进行中”的子任务数量
     */
    long countInProgressSubTasks();
    
    List<SubTask> findAllSubTasks();
    
    Optional<SubTask> findSubTaskById(Long id);
    
    /**
     * 根据ID查找子任务
     * @param id 子任务ID
     * @return 子任务（可选）
     */
    Optional<SubTask> findById(Long id);
    
    List<SubTask> findSubTasksByTaskId(Long taskId);
    List<SubTask> findSubTasksByProjectId(Long projectId);
    
    Page<SubTask> findSubTasksByTaskId(Long taskId, Pageable pageable);
    
    SubTask saveSubTask(SubTask subTask);
    
    void deleteSubTask(Long id);
    
    Integer getNextSequenceNumber(Long taskId);
    
    List<SubTask> searchSubTasks(String keyword);
    
    List<SubTask> findSubTasksByDateRange(LocalDateTime startDate, LocalDateTime endDate);
    
    Optional<SubTask> findLatestSubTaskByTaskId(Long taskId);
    
    /**
     * 批量获取多个任务的最新子任务
     * @param taskIds 任务ID列表
     * @return 每个任务的最新子任务
     */
    List<SubTask> findLatestSubTasksByTaskIds(List<Long> taskIds);

    /**
     * 分页查询所有评论
     * @param pageable 分页参数
     * @return 分页的评论列表
     */
    Page<SubTask> findAllSubTasksPaged(Pageable pageable);

    /**
     * 动态搜索评论
     * @param searchCriteria 搜索条件
     * @param pageable 分页参数
     * @return 分页的评论列表
     */
    Page<SubTask> dynamicSearchSubTasks(Map<String, String> searchCriteria, Pageable pageable);

    /**
     * 根据项目ID查找所有子任务（用于项目详情页评论列表）
     * @param projectId 项目ID
     * @return 该项目下所有子任务
     */
    List<SubTask> findTasksByProjectId(Long projectId);

    /**
     * 批量分页查询多个任务的所有子任务（用于项目评论列表）
     */
    Page<SubTask> findSubTasksByTaskIds(List<Long> taskIds, Pageable pageable);
}