<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">

<head th:replace="~{fragments/layout :: head('发起流程')}">
    <meta charset="UTF-8">
    <title>发起流程</title>
</head>

<body th:replace="~{fragments/layout :: body(~{::div.content-wrapper}, ~{::script})}">
    <div class="content-wrapper">
        <div
            class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2" th:text="${instance != null && instance.instanceId != null ? '编辑流程' : '发起流程'}">发起流程</h1>
            <div>
                <button type="button" class="btn btn-outline-secondary me-2" onclick="history.back()">
                    <i class="bi bi-arrow-left"></i> 返回
                </button>
                <button type="button" class="btn btn-primary"
                    onclick="document.getElementById('instanceForm').submit()">
                    <i class="bi bi-send"></i> <span th:text="${instance != null && instance.instanceId != null ? '更新' : '保存'}">保存</span>
                </button>
            </div>
        </div>

        <!-- 表单卡片 -->
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">流程信息</h5>
                    </div>
                    <div class="card-body">
                        <!-- 错误消息 -->
                        <div th:if="${error}" class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="bi bi-exclamation-triangle-fill me-2"></i>
                            <span th:text="${error}">操作失败</span>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>                        <form id="instanceForm" th:action="@{/workflow/instances/save}" method="post"
                            th:object="${instance}" class="row g-3 needs-validation" novalidate>
                            <!-- 隐藏字段：流程实例ID（用于编辑模式） -->
                            <input type="hidden" th:field="*{instanceId}" th:if="${instance != null && instance.instanceId != null}">
                            <!-- 流程模板 -->
                            <div class="col-md-6">
                                <label for="templateId" class="form-label">流程模板 <span
                                        class="text-danger">*</span></label>                                <select class="form-select" id="templateId" name="templateId" required>
                                    <option value="">请选择流程模板</option>
                                    <th:block th:each="tmpl : ${templates}">
                                        <!-- 只显示非空和启用状态的模板 -->
                                        <option th:if="${tmpl != null && (tmpl.enabled == null || tmpl.enabled == true)}"
                                            th:value="${tmpl != null ? tmpl.templateId : ''}"
                                            th:text="${tmpl != null ? tmpl.templateName : '未知模板'}"
                                            th:data-step-count="${tmpl != null && tmpl.steps != null ? tmpl.steps.size() : 0}"
                                            th:selected="${instance != null && instance.template != null && tmpl != null && instance.template.templateId == tmpl.templateId}">
                                            流程模板名称
                                        </option>
                                    </th:block>
                                </select>
                            </div>

                            <!-- 步骤数量 -->
                            <div class="col-md-6">
                                <label class="form-label">步骤数量</label>
                                <div class="form-control-plaintext">
                                    <span id="stepCount" class="badge bg-primary">0</span>
                                    <input type="hidden" name="stepCount" id="stepCountInput" th:field="*{stepCount}">
                                </div>
                            </div>                            <!-- 流程标题 -->
                            <div class="col-md-12">
                                <label for="title" class="form-label">流程标题 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="title" th:field="*{title}" required readonly>
                                <small class="form-text text-muted">流程标题由选择的模板自动填充，不可手动修改</small>
                            </div>

                            <!-- 业务类型 -->
                            <div class="col-md-6">
                                <label for="businessType" class="form-label">业务类型</label>
                                <select class="form-select" id="businessType" th:field="*{businessType}" disabled>
                                    <option value="">请选择</option>
                                    <option value="项目">项目</option>
                                    <option value="任务">任务</option>
                                    <option value="通用">通用</option>
                                </select>
                                <!-- 添加隐藏字段保存业务类型值，因为禁用字段不会提交 -->
                                <input type="hidden" name="businessType" th:value="*{businessType}">
                            </div>

                            <!-- 业务ID -->
                            <div class="col-md-6">
                                <label for="businessId" class="form-label">业务ID</label>
                                <input type="number" class="form-control" id="businessId" th:field="*{businessId}"
                                    disabled>
                            </div>

                            <!-- 开始时间 -->
                            <div class="col-md-6">
                                <label for="startTime" class="form-label">开始时间</label>
                                <input type="datetime-local" class="form-control" id="startTime" name="startTime"
                                    step="1" th:value="${instance != null && instance.startTime != null ? instance.formatDateTimeForInput(instance.startTime) : ''}">
                            </div>

                            <!-- 结束时间 -->
                            <div class="col-md-6">
                                <label for="endTime" class="form-label">结束时间</label>
                                <input type="datetime-local" class="form-control" id="endTime" name="endTime" step="1"
                                    th:value="${instance != null && instance.endTime != null ? instance.formatDateTimeForInput(instance.endTime) : ''}">
                            </div>

                            <!-- 出发地 -->
                            <div class="col-md-6">
                                <label for="startLocation" class="form-label">出发地</label>
                                <input type="text" class="form-control" id="startLocation" name="startLocation"
                                    th:value="${instance != null && instance.startLocation != null ? instance.startLocation : '公司'}">
                            </div>

                            <!-- 目的地 -->
                            <div class="col-md-6">
                                <label for="endLocation" class="form-label">目的地</label>
                                <input type="text" class="form-control" id="endLocation" name="endLocation"
                                    th:value="${instance != null && instance.endLocation != null ? instance.endLocation : ''}">
                            </div>


                            <!-- 备注1 - 从input修改为textarea，设置rows="2" -->
                            <div class="col-md-6">
                                <label for="remark1" class="form-label">备注1</label>
                                <textarea class="form-control" id="remark1" name="remark1" rows="2"
                                          th:text="${instance != null && instance.remark1 != null ? instance.remark1 : ''}"></textarea>
                            </div>

                            <!-- 备注2 - 从input修改为textarea，设置rows="2" -->
                            <div class="col-md-6">
                                <label for="remark2" class="form-label">备注2</label>
                                <textarea class="form-control" id="remark2" name="remark2" rows="2"
                                          th:text="${instance != null && instance.remark2 != null ? instance.remark2 : ''}"></textarea>
                            </div>

                            <!-- 备注3 - 从input修改为textarea，设置rows="2" -->
                            <div class="col-md-6">
                                <label for="remark3" class="form-label">备注3</label>
                                <textarea class="form-control" id="remark3" name="remark3" rows="2"
                                          th:text="${instance != null && instance.remark3 != null ? instance.remark3 : ''}"></textarea>
                            </div>

                            <!-- 备注4 - 从input修改为textarea，设置rows="2" -->
                            <div class="col-md-6">
                                <label for="remark4" class="form-label">备注4</label>
                                <textarea class="form-control" id="remark4" name="remark4" rows="2"
                                          th:text="${instance != null && instance.remark4 != null ? instance.remark4 : ''}"></textarea>
                            </div>

                            <!-- 人员 -->
                            <div class="col-md-6">
                                <label for="staff" class="form-label">人员</label>
                                <select class="form-select select2-control" id="staff" name="staff" multiple
                                        th:data-selected="${instance != null && instance.staff != null ? instance.staff : ''}">
                                    <option value="">请选择人员</option>
                                    <option th:each="person : ${personnel}"
                                            th:value="${person}"
                                            th:text="${person}"
                                            th:selected="${instance != null && instance.staff != null && instance.staff.contains(person)}"></option>
                                </select>
                            </div>

                            <!-- 流程说明 -->
                            <div class="col-md-6">
                                <label for="description" class="form-label">流程说明</label>
                                <textarea class="form-control" id="description" th:field="*{description}" rows="3"
                                    readonly style="color: red; "></textarea>
                            </div>
                        </form>
                    </div>                    <div class="card-footer text-end">
                        <button type="button" class="btn btn-secondary me-2" onclick="history.back()">取消</button>
                        <button type="button" class="btn btn-primary" id="saveInstanceBtn">保存</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加Select2的JS引用和脚本 -->
    <script th:inline="javascript">
        // 在页面加载完成后添加Select2的CSS
        document.addEventListener('DOMContentLoaded', function() {
            // 动态添加Select2 CSS
            const selectCss = document.createElement('link');
            selectCss.rel = 'stylesheet';
            selectCss.href = 'https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css';
            document.head.appendChild(selectCss);
            
            // 动态添加Select2 JS
            const selectScript = document.createElement('script');
            selectScript.src = 'https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js';
            selectScript.onload = function() {
                // 初始化Select2
                $('#staff').select2({
                    placeholder: '请选择人员',
                    allowClear: true,
                    width: '100%',
                    language: {
                        noResults: function() {
                            return "没有找到匹配的人员";
                        },
                        searching: function() {
                            return "搜索中...";
                        }
                    }
                });
            };
            document.body.appendChild(selectScript);
        });
    </script>
    <script th:src="@{/js/workflow/instance-form.js}"></script>
</body>

</html>