/**
 * 处理流程模板选择变化
 */
document.addEventListener('DOMContentLoaded', function () {
    const templateSelect = document.getElementById('templateId');
    const stepCountSpan = document.getElementById('stepCount');
    // 添加一个变量来跟踪上一次的流程标题
    let lastFlowTitle = '';

    // 检查是否为编辑模式（通过URL参数或隐藏字段判断）
    const isEditMode = checkEditMode();

    // 初始化日期时间格式化程序
    setupDateTimeFields();

    // 页面加载完成后主动刷新侧边栏徽章数据（如有侧边栏刷新管理器）
    if (window.sidebarRefreshManager && typeof window.sidebarRefreshManager.refreshSidebarData === 'function') {
        window.sidebarRefreshManager.refreshSidebarData();
    }

    // 如果是编辑模式，自动填充表单数据
    if (isEditMode) {
        console.log('检测到编辑模式，开始自动填充表单数据');
        autoFillFormData();
    } else {
        // 页面加载完成后立即显示默认字段
        updateFormFieldsVisibility('');
    }    // 检查是否是workflow-info-card页面 - 使用更简单、更兼容的选择器
    // 查找包含"基本信息"标题的卡片
    const workflowInfoCards = document.querySelectorAll('.card');
    console.log('找到的卡片数量:', workflowInfoCards.length);
    let workflowInfoCard = null;

    for (const card of workflowInfoCards) {
        // 先尝试查找 .card-header 元素
        const cardHeader = card.querySelector('.card-header');
        if (cardHeader) {
            // 然后查找其中的 h5 元素
            const headerTitle = cardHeader.querySelector('h5');
            if (headerTitle && headerTitle.textContent && headerTitle.textContent.trim().includes('基本信息')) {
                workflowInfoCard = card;
                console.log('找到基本信息卡片:', headerTitle.textContent.trim());
                break;
            }
        }
    }

    if (workflowInfoCard) {
        console.log('检测到工作流信息卡片页面');
        // 获取流程标题 - 通常在第一个表格行的第二个单元格中
        const tables = workflowInfoCard.querySelectorAll('table.table-striped');
        if (tables && tables.length > 0) {
            const titleRow = tables[0].querySelector('tr');
            if (titleRow) {
                // 确认这是流程标题行
                const labelCell = titleRow.querySelector('th');
                if (labelCell && labelCell.textContent && labelCell.textContent.trim().includes('流程标题')) {
                    const titleCell = titleRow.querySelector('td');
                    if (titleCell && titleCell.textContent) {
                        const flowTitle = titleCell.textContent.trim();
                        console.log('工作流信息卡片 - 找到流程标题:', flowTitle);
                        // 更新工作流信息卡片字段可见性
                        updateWorkflowInfoCardFieldsVisibility(flowTitle);
                    } else {
                        console.warn('未能找到流程标题单元格');
                    }
                } else {
                    console.warn('未能找到流程标题行');
                }
            } else {
                console.warn('表格中没有找到行');
            }
        } else {
            console.warn('未找到表格');
        }
    } else {
        console.log('未检测到工作流信息卡片页面');
    }

    // 添加表单验证处理
    const instanceForm = document.getElementById('instanceForm');
    const saveButton = document.getElementById('saveInstanceBtn');
    if (saveButton) {
        saveButton.addEventListener('click', function (event) {
            // 验证表单
            if (!instanceForm.checkValidity()) {
                // 阻止默认提交
                event.preventDefault();
                event.stopPropagation();

                // 显示验证消息
                instanceForm.classList.add('was-validated');

                // 滚动到第一个无效字段
                const invalidField = instanceForm.querySelector(':invalid');
                if (invalidField) {
                    invalidField.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    invalidField.focus();
                }

                // 显示错误提示
                alert('请填写所有必填字段');
                return;
            }

            // 处理日期时间字段，确保格式正确
            processDateTimeFields();

            // 验证通过，提交表单
            instanceForm.submit();
        });
    }
    // 处理表单提交前的日期时间字段格式化
    function processDateTimeFields() {
        // 获取日期时间字段
        const startTimeField = document.getElementById('startTime');
        const endTimeField = document.getElementById('endTime');

        // 处理开始时间
        if (startTimeField && startTimeField.value) {
            const isoDateTime = startTimeField.value; // HTML datetime-local返回ISO格式：yyyy-MM-ddThh:mm
            if (isoDateTime.includes('T')) {
                // 创建隐藏字段存储标准化的日期时间字符串
                createOrUpdateHiddenField('startTimeFormatted', formatDateTimeToStandard(isoDateTime));
            }
        }

        // 处理结束时间
        if (endTimeField && endTimeField.value) {
            const isoDateTime = endTimeField.value;
            if (isoDateTime.includes('T')) {
                // 创建隐藏字段存储标准化的日期时间字符串
                createOrUpdateHiddenField('endTimeFormatted', formatDateTimeToStandard(isoDateTime));
            }
        }
    }

    // 创建或更新隐藏字段
    function createOrUpdateHiddenField(name, value) {
        let hiddenField = document.querySelector(`input[name="${name}"]`);
        if (!hiddenField) {
            hiddenField = document.createElement('input');
            hiddenField.type = 'hidden';
            hiddenField.name = name;
            document.getElementById('instanceForm').appendChild(hiddenField);
        }
        hiddenField.value = value;
    }

    // 将ISO格式日期时间转换为标准格式 "yyyy-MM-dd HH:mm:ss"
    function formatDateTimeToStandard(isoDateTime) {
        try {
            // ISO格式: yyyy-MM-ddThh:mm
            const [datePart, timePart] = isoDateTime.split('T');
            // 如果时间部分不包含秒，添加:00
            const fullTimePart = timePart.includes(':') && timePart.split(':').length === 2
                ? timePart + ':00'
                : timePart;
            return `${datePart} ${fullTimePart}`;
        } catch (e) {
            console.error('格式化日期时间失败:', e);
            return isoDateTime; // 返回原始值
        }
    }

    if (templateSelect) {
        templateSelect.addEventListener('change', async function () {
            const templateId = this.value;
            if (templateId) {
                try {
                    // 发送请求获取模板步骤数量
                    const response = await fetch(`/workflow/templates/${templateId}/step-count`);
                    if (response.ok) {
                        const data = await response.json();
                        // 更新步骤数量显示
                        if (stepCountSpan) {
                            stepCountSpan.textContent = data.stepCount;
                        }
                        // 更新隐藏字段
                        const stepCountInput = document.getElementById('stepCountInput');
                        if (stepCountInput) {
                            stepCountInput.value = data.stepCount;
                        }

                        // 获取选中的模板选项
                        const selectedOption = templateSelect.options[templateSelect.selectedIndex];

                        // 获取模板相关信息，填充到对应表单字段中
                        const titleField = document.getElementById('title');
                        let newFlowTitle = titleField.value; // 保存初始标题值

                        // 发送请求获取模板详情
                        try {
                            const templateDetailResponse = await fetch(`/workflow/templates/${templateId}/detail`);
                            if (templateDetailResponse.ok) {
                                const templateData = await templateDetailResponse.json();

                                // 根据模板类型更新标题字段
                                if (templateData.templateCode) {
                                    newFlowTitle = templateData.templateCode;
                                }
                                else {
                                    newFlowTitle = templateData.templateCode;
                                }

                                // 更新标题字段值
                                titleField.value = newFlowTitle;

                                // 填充适用范围到业务类型
                                const businessTypeField = document.getElementById('businessType');
                                // 需要同时更新select元素和隐藏的input元素
                                if (businessTypeField && templateData.applicableScope) {
                                    businessTypeField.value = templateData.applicableScope;
                                    // 更新隐藏的input字段
                                    const hiddenInput = document.querySelector('input[name="businessType"]');
                                    if (hiddenInput) {
                                        hiddenInput.value = templateData.applicableScope;
                                    }
                                }

                                // 填充模板描述到流程说明
                                const descriptionField = document.getElementById('description');
                                if (descriptionField && templateData.description) {
                                    descriptionField.value = templateData.description;
                                }

                                // 检查流程标题是否发生变化
                                const flowTitleChanged = newFlowTitle !== lastFlowTitle;

                                // 保存当前流程标题用于下次比较
                                lastFlowTitle = newFlowTitle;

                                // 只有当流程标题发生变化时，才更新表单字段的可见性
                                if (flowTitleChanged) {
                                    console.log('流程标题已变化，更新表单字段可见性:', newFlowTitle);

                                    // 使用流程标题而不是模板名称来决定字段的显示与隐藏
                                    updateFormFieldsVisibility(newFlowTitle);
                                }
                            } else {
                                console.warn('获取模板详情响应不成功:', templateDetailResponse.status);
                            }
                        } catch (error) {
                            console.error('获取模板详情失败:', error);
                        }
                    } else {
                        console.warn('获取步骤数量响应不成功:', response.status);
                    }
                } catch (error) {
                    console.error('获取步骤数量失败:', error);
                }
            } else {
                // 清空步骤数量
                if (stepCountSpan) {
                    stepCountSpan.textContent = '0';
                }
                const stepCountInput = document.getElementById('stepCountInput');
                if (stepCountInput) {
                    stepCountInput.value = '0';
                }                // 模板为空时，清空其他相关字段
                const descriptionField = document.getElementById('description');
                if (descriptionField) {
                    descriptionField.value = '';
                }

                // 模板为空时，重置表单字段（清空流程标题并更新字段可见性）
                lastFlowTitle = '';
                updateFormFieldsVisibility('');
            }
        });

        // 确保页面加载完成后，如果已选择了模板，则触发一次change事件
        if (templateSelect.value) {
            // 使用setTimeout确保DOM完全加载
            setTimeout(() => {
                // 创建并触发change事件
                const event = new Event('change');
                templateSelect.dispatchEvent(event);
            }, 100);
        }        // 注释掉标题字段的手动变更监听器，因为现在不允许手动输入
        // const titleField = document.getElementById('title');
        // if (titleField) {
        //     titleField.addEventListener('change', function () {
        //         const newFlowTitle = this.value;
        //         // 只有当流程标题发生变化时，才更新表单字段的可见性
        //         if (newFlowTitle !== lastFlowTitle) {
        //             console.log('用户修改了流程标题，更新表单字段可见性:', newFlowTitle);
        //             lastFlowTitle = newFlowTitle;
        //             updateFormFieldsVisibility(newFlowTitle);
        //         }
        //     });
        // }
    }    // 根据流程标题显示/隐藏相关字段的函数
    function updateFormFieldsVisibility(flowTitle) {
        console.log('根据流程标题更新字段可见性:', flowTitle);

        // 获取所有相关字段元素
        const startTimeField = document.getElementById('startTime');
        const endTimeField = document.getElementById('endTime');
        const startLocationField = document.getElementById('startLocation');
        const endLocationField = document.getElementById('endLocation');
        const remark1Field = document.getElementById('remark1');
        const remark2Field = document.getElementById('remark2');
        const remark3Field = document.getElementById('remark3');
        const remark4Field = document.getElementById('remark4');
        const staffField = document.getElementById('staff');

        const businessTypeField = document.getElementById('businessType');
        const businessIdField = document.getElementById('businessId');

        // 首先取消所有字段的必填项设置
        console.log('首先取消所有字段的必填项设置');
        setRequired([startTimeField, endTimeField, startLocationField, endLocationField,
            remark1Field, remark2Field, remark3Field, remark4Field, staffField], false);

        // 获取字段容器
        const startTimeContainer = startTimeField?.closest('.col-md-6');
        const endTimeContainer = endTimeField?.closest('.col-md-6');
        const startLocationContainer = startLocationField?.closest('.col-md-6');
        const endLocationContainer = endLocationField?.closest('.col-md-6');
        const remark1Container = remark1Field?.closest('.col-md-6');
        const remark2Container = remark2Field?.closest('.col-md-6');
        const remark3Container = remark3Field?.closest('.col-md-6');
        const remark4Container = remark4Field?.closest('.col-md-6');
        const staffContainer = staffField?.closest('.col-md-6');
        const businessTypeContainer = businessTypeField?.closest('.col-md-6');
        const businessIdContainer = businessIdField?.closest('.col-md-6');


        // 默认显示所有字段
        [startTimeContainer, endTimeContainer, startLocationContainer, endLocationContainer,
            remark1Container, remark2Container, remark3Container, remark4Container, staffContainer]
            .forEach(container => {
                if (container) container.style.display = 'block';
            });

        // 设置默认值
        if (startLocationField) startLocationField.value = startLocationField.value || '公司';

        if (businessTypeContainer) businessTypeContainer.style.display = 'none';
        if (businessIdContainer) businessIdContainer.style.display = 'none';

        // 根据不同流程标题调整字段显示和标签
        if (flowTitle.includes('换岗')) {
            // 出差申请：显示时间、地点字段
            updateFieldLabel(startTimeField, '离岗时间 *');
            updateFieldLabel(endTimeField, '到岗时间 *');
            updateFieldLabel(startLocationField, '出发地 *（重庆京东方，公司）');
            updateFieldLabel(endLocationField, '目的地 *（重庆京东方，公司）');

            updateFieldLabel(remark1Field, '当前岗位主要工作 *');
            updateFieldLabel(remark2Field, '目标岗位主要工作 *');
            updateFieldLabel(remark4Field, '换岗事由（由###安排调岗，因为***自己主动换岗） *');

            // 设置必填项
            setRequired([startTimeField, endTimeField, startLocationField, endLocationField,
                remark1Field,
                remark2Field,
                remark4Field], true);

            // 隐藏不需要的字段
            if (remark3Container) remark3Container.style.display = 'none';
            if (staffContainer) staffContainer.style.display = 'none';
        } else if (flowTitle.includes('请假')) {
            // 请假申请：显示时间，隐藏地点字段
            updateFieldLabel(startTimeField, '开始时间 *');
            updateFieldLabel(endTimeField, '结束时间 *');
            updateFieldLabel(startLocationField, '当前所在地 *（重庆京东方，公司）');
            updateFieldLabel(endLocationField, '预计返回地 *（重庆京东方，公司）');

            updateFieldLabel(remark1Field, '当前岗位主要工作 *');
            updateFieldLabel(remark4Field, '请假原因 *');

            // 设置必填项
            setRequired([startTimeField, endTimeField, startLocationField, endLocationField,
                remark1Field,
                remark4Field], true);

            // 隐藏不需要的字段
            if (remark3Container) remark3Container.style.display = 'none';
            if (remark2Container) remark2Container.style.display = 'none';
            if (staffContainer) staffContainer.style.display = 'none';


        } else if (flowTitle.includes('签卡')) {
            // 申请
            updateFieldLabel(startTimeField, '发生时间 *');
            updateFieldLabel(startLocationField, '所在地 *（重庆京东方，公司）');
            updateFieldLabel(remark4Field, '申请原因 *');            // 设置必填项
            setRequired([startTimeField, startLocationField,
                remark4Field], true);

            // 隐藏不需要的字段
            if (endTimeContainer) endTimeContainer.style.display = 'none';
            if (endLocationContainer) endLocationContainer.style.display = 'none';
            if (remark1Container) remark1Container.style.display = 'none';
            if (remark2Container) remark2Container.style.display = 'none';
            if (remark3Container) remark3Container.style.display = 'none';
            if (staffContainer) staffContainer.style.display = 'none';




        } else if (flowTitle.includes('费用') || flowTitle.includes('奖励') || flowTitle.includes('惩罚')) {
            // 费用申请
            updateFieldLabel(startTimeField, '发生时间 *');
            updateFieldLabel(startLocationField, '当前所在地 *（重庆京东方，公司）');
            updateFieldLabel(remark3Field, '金额 *（奖励为正数，费用、惩罚为负数）');
            updateFieldLabel(remark4Field, '申请原因 *');
            updateFieldLabel(staffField, '相关人员 *');

            // 设置必填项
            setRequired([startTimeField, startLocationField, staffField,
                remark3Field,
                remark4Field], true);

            // 隐藏不需要的字段
            if (endTimeContainer) endTimeContainer.style.display = 'none';
            if (endLocationContainer) endLocationContainer.style.display = 'none';
            if (remark1Container) remark1Container.style.display = 'none';
            if (remark2Container) remark2Container.style.display = 'none';


        } else if (flowTitle.includes('源码')) {
            //源码修改申请
            updateFieldLabel(startTimeField, '开始时间 *');
            updateFieldLabel(endTimeField, '完成时间 *');
            updateFieldLabel(startLocationField, '当前所在地 *（重庆京东方，公司）');
            updateFieldLabel(remark1Field, '当前版本 *');

            updateFieldLabel(remark4Field, '修改原因 *');

            // 设置必填项
            setRequired([startTimeField, endTimeField, startLocationField,
                remark1Field,
                remark4Field], true);            // 隐藏不需要的字段         
            if (endLocationContainer) endLocationContainer.style.display = 'none';
            if (remark2Container) remark2Container.style.display = 'none';
            if (remark3Container) remark3Container.style.display = 'none';
            if (staffContainer) staffContainer.style.display = 'none';        } else {
            // 其他类型流程或模板未选择时的处理
            console.log('模板未选择或其他类型流程');
            
            // 重置字段标签为默认值（不带必填标记）
            updateFieldLabel(startTimeField, '开始时间');
            updateFieldLabel(endTimeField, '结束时间');
            updateFieldLabel(startLocationField, '出发地');
            updateFieldLabel(endLocationField, '目的地');
            updateFieldLabel(remark1Field, '备注1');
            updateFieldLabel(remark2Field, '备注2');
            updateFieldLabel(remark3Field, '备注3');
            updateFieldLabel(remark4Field, '任务提交备注');
            updateFieldLabel(staffField, '相关人员');
            
            // 隐藏不需要的字段
            if (startTimeContainer) startTimeContainer.style.display = 'none';
            if (endTimeContainer) endTimeContainer.style.display = 'none';
            if (startLocationContainer) startLocationContainer.style.display = 'none';
            if (endLocationContainer) endLocationContainer.style.display = 'none';
            if (remark1Container) remark1Container.style.display = 'none';
            if (remark2Container) remark2Container.style.display = 'none';
            if (remark3Container) remark3Container.style.display = 'none';
            //if (remark4Container) remark4Container.style.display = 'none';
            if (staffContainer) staffContainer.style.display = 'none';
        }
    }

    // 更新字段标签函数
    function updateFieldLabel(field, newLabel) {
        if (!field) return;
        const labelElement = field.previousElementSibling;
        if (labelElement && labelElement.tagName === 'LABEL') {
            // 保留原始的class
            const originalClass = labelElement.getAttribute('class');

            // 检查是否为必填项
            if (newLabel.includes('*')) {
                labelElement.innerHTML = newLabel.replace('*', '<span class="text-danger">*</span>');
            } else {
                labelElement.textContent = newLabel;
            }

            // 恢复原始的class
            if (originalClass) {
                labelElement.setAttribute('class', originalClass);
            }
        }
    }

    // 设置字段是否必填
    function setRequired(fields, required) {
        fields.forEach(field => {
            if (field) {
                if (required) {
                    field.setAttribute('required', '');

                    // 添加验证反馈提示元素，如果不存在的话
                    let parent = field.parentElement;
                    let feedbackDiv = parent.querySelector('.invalid-feedback');
                    if (!feedbackDiv) {
                        feedbackDiv = document.createElement('div');
                        feedbackDiv.className = 'invalid-feedback';
                        feedbackDiv.textContent = '此字段是必填项';
                        parent.appendChild(feedbackDiv);
                    }
                } else {
                    field.removeAttribute('required');

                    // 可选择性地移除验证反馈提示
                    let parent = field.parentElement;
                    let feedbackDiv = parent.querySelector('.invalid-feedback');
                    if (feedbackDiv) {
                        parent.removeChild(feedbackDiv);
                    }
                }
            }
        });
    }    // 为工作流信息卡片(workflow-info-card)页面更新字段可见性的函数
    function updateWorkflowInfoCardFieldsVisibility(flowTitle) {
        console.log('工作流信息卡片 - 根据流程标题更新字段可见性:', flowTitle);

        // 获取信息卡片区域 - 使用更可靠的选择器
        let infoCard = null;
        const allCards = document.querySelectorAll('.card');

        for (const card of allCards) {
            // 先尝试查找 .card-header 元素
            const cardHeader = card.querySelector('.card-header');
            if (cardHeader) {
                // 然后查找其中的 h5 元素
                const headerTitle = cardHeader.querySelector('h5');
                if (headerTitle && headerTitle.textContent && headerTitle.textContent.trim().includes('基本信息')) {
                    infoCard = card;
                    console.log('找到工作流信息卡片:', headerTitle.textContent.trim());
                    break;
                }
            }
        }

        if (!infoCard) {
            console.warn('未找到工作流信息卡片元素');
            return;
        }

        console.log('找到工作流信息卡片，开始更新字段可见性');

        // 获取所有表格行
        const allRows = infoCard.querySelectorAll('table.table-striped tr');
        console.log('找到的表格行数:', allRows.length);          // 通过字段标签文本查找相应行
        const findRowByLabel = (labelText) => {
            for (const row of allRows) {
                const th = row.querySelector('th');
                if (th && th.textContent) {
                    console.log(`检查行标签: "${th.textContent.trim()}" 是否包含 "${labelText}"`);
                    if (th.textContent.trim().includes(labelText)) {
                        console.log(`找到标签"${labelText}"的行:`, th.textContent.trim());
                        return row;
                    }
                }
            }
            console.log(`未找到标签"${labelText}"的行`);
            return null;
        };

        // 获取相关字段的行
        const startTimeContainer = findRowByLabel('开始时间') || findRowByLabel('出发时间') || findRowByLabel('发生时间');
        const endTimeContainer = findRowByLabel('结束时间') || findRowByLabel('返回时间') || findRowByLabel('完成时间');
        const startLocationContainer = findRowByLabel('出发地') || findRowByLabel('当前所在地') || findRowByLabel('所在地');
        const endLocationContainer = findRowByLabel('目的地') || findRowByLabel('预计返回地');
        const remark1Container = findRowByLabel('备注1') || findRowByLabel('当前岗位') || findRowByLabel('当前版本');
        const remark2Container = findRowByLabel('备注2') || findRowByLabel('目标岗位');
        const remark3Container = findRowByLabel('备注3') || findRowByLabel('金额');
        const remark4Container = findRowByLabel('备注4') || findRowByLabel('请假原因') || findRowByLabel('换岗事由') || findRowByLabel('申请原因') || findRowByLabel('修改原因');
        const staffContainer = findRowByLabel('人员') || findRowByLabel('相关人员');

        const businessTypeRow = findRowByLabel('业务类型');
        const businessIdRow = findRowByLabel('业务ID');

        // 默认显示所有字段
        [startTimeContainer, endTimeContainer, startLocationContainer, endLocationContainer,
            remark1Container, remark2Container, remark3Container, remark4Container, staffContainer]
            .forEach(row => {
                if (row) row.style.display = '';
            });

        // 业务类型和业务ID字段默认显示
        if (businessTypeRow) businessTypeRow.style.display = '';
        if (businessIdRow) businessIdRow.style.display = '';

        // 根据不同流程标题调整字段显示和标签
        if (flowTitle.includes('换岗')) {
            // 换岗申请：显示时间、地点字段
            updateCardFieldLabel(startTimeContainer, '离岗时间');
            updateCardFieldLabel(endTimeContainer, '到岗时间');
            updateCardFieldLabel(startLocationContainer, '出发地（重庆京东方，公司）');
            updateCardFieldLabel(endLocationContainer, '目的地（重庆京东方，公司）');
            updateCardFieldLabel(remark1Container, '当前岗位主要工作');
            updateCardFieldLabel(remark2Container, '目标岗位主要工作');
            updateCardFieldLabel(remark4Container, '换岗事由');

            // 隐藏不需要的字段
            if (remark3Container) remark3Container.style.display = 'none';
            if (staffContainer) staffContainer.style.display = 'none';

        } else if (flowTitle.includes('请假')) {
            // 请假申请：显示时间，地点字段
            updateCardFieldLabel(startTimeContainer, '开始时间');
            updateCardFieldLabel(endTimeContainer, '结束时间');
            updateCardFieldLabel(startLocationContainer, '当前所在地（重庆京东方，公司）');
            updateCardFieldLabel(endLocationContainer, '预计返回地（重庆京东方，公司）');
            updateCardFieldLabel(remark1Container, '当前岗位主要工作');
            updateCardFieldLabel(remark4Container, '请假原因');

            // 隐藏不需要的字段
            if (remark3Container) remark3Container.style.display = 'none';
            if (remark2Container) remark2Container.style.display = 'none';
            if (staffContainer) staffContainer.style.display = 'none';

        } else if (flowTitle.includes('签卡')) {
            // 签卡申请
            updateCardFieldLabel(startTimeContainer, '发生时间');
            updateCardFieldLabel(startLocationContainer, '所在地（重庆京东方，公司）');
            updateCardFieldLabel(remark4Container, '申请原因');

            // 隐藏不需要的字段
            if (endTimeContainer) endTimeContainer.style.display = 'none';
            if (endLocationContainer) endLocationContainer.style.display = 'none';
            if (remark1Container) remark1Container.style.display = 'none';
            if (remark2Container) remark2Container.style.display = 'none';
            if (remark3Container) remark3Container.style.display = 'none';
            if (staffContainer) staffContainer.style.display = 'none';

        } else if (flowTitle.includes('费用') || flowTitle.includes('奖励') || flowTitle.includes('惩罚')) {
            // 费用申请
            updateCardFieldLabel(startTimeContainer, '发生时间');
            updateCardFieldLabel(startLocationContainer, '当前所在地（重庆京东方，公司）');
            updateCardFieldLabel(remark3Container, '金额（奖励为正数，费用、惩罚为负数）');
            updateCardFieldLabel(remark4Container, '申请原因');
            updateCardFieldLabel(staffContainer, '相关人员');

            // 隐藏不需要的字段
            if (endTimeContainer) endTimeContainer.style.display = 'none';
            if (endLocationContainer) endLocationContainer.style.display = 'none';
            if (remark1Container) remark1Container.style.display = 'none';
            if (remark2Container) remark2Container.style.display = 'none';

        } else if (flowTitle.includes('源码')) {
            // 源码修改申请
            updateCardFieldLabel(startTimeContainer, '开始时间');
            updateCardFieldLabel(endTimeContainer, '完成时间');
            updateCardFieldLabel(startLocationContainer, '当前所在地（重庆京东方，公司）');
            updateCardFieldLabel(remark1Container, '当前版本');
            updateCardFieldLabel(remark4Container, '修改原因');

            // 隐藏不需要的字段
            if (endLocationContainer) endLocationContainer.style.display = 'none';
            if (remark2Container) remark2Container.style.display = 'none';
            if (remark3Container) remark3Container.style.display = 'none';
            if (staffContainer) staffContainer.style.display = 'none';

        } else {
            // 其他类型流程，保持基本显示，隐藏特定字段
            // 通常，我们应该还是显示这些字段，因为它们可能包含有用的信息
            // 其他类型流程，保持所有字段可见性和默认标签
            // 隐藏不需要的字段
            updateCardFieldLabel(remark4Container, '任务提交备注');

            if (startTimeContainer) startTimeContainer.style.display = 'none';
            if (endTimeContainer) endTimeContainer.style.display = 'none';
            if (startLocationContainer) startLocationContainer.style.display = 'none';
            if (endLocationContainer) endLocationContainer.style.display = 'none';
            if (remark1Container) remark1Container.style.display = 'none';
            if (remark2Container) remark2Container.style.display = 'none';
            if (remark3Container) remark3Container.style.display = 'none';
            //if (remark4Container) remark4Container.style.display = 'none';
            if (staffContainer) staffContainer.style.display = 'none';
        }
    }
    // 更新信息卡片字段标签函数
    function updateCardFieldLabel(row, newLabel) {
        if (!row) return;

        // 查找标签元素(th)
        const labelElement = row.querySelector('th');
        if (labelElement) {
            labelElement.textContent = newLabel;
        }
    }

    // 设置日期时间字段的处理函数
    function setupDateTimeFields() {
        // 获取日期时间字段
        const startTimeField = document.getElementById('startTime');
        const endTimeField = document.getElementById('endTime');

        // 为日期时间字段添加事件监听器
        if (startTimeField) {
            // 如果有已有值（如编辑流程实例）尝试显示为本地日期时间格式
            formatExistingDateTime(startTimeField);

            // 当字段值改变时进行处理
            startTimeField.addEventListener('change', function () {
                validateDateTimeFormat(this);
            });
        }

        if (endTimeField) {
            // 如果有已有值（如编辑流程实例）尝试显示为本地日期时间格式
            formatExistingDateTime(endTimeField);

            // 当字段值改变时进行处理
            endTimeField.addEventListener('change', function () {
                validateDateTimeFormat(this);
            });
        }
    }

    // 格式化已有的日期时间值
    function formatExistingDateTime(field) {
        const value = field.value;
        if (value && !value.includes('T')) {
            // 如果值不包含'T'（非ISO格式），尝试转换为本地日期时间格式
            try {
                // 假设值是"yyyy-MM-dd HH:mm:ss"格式
                const parts = value.split(' ');
                if (parts.length === 2) {
                    const datePart = parts[0];
                    const timePart = parts[1];
                    // 转换为HTML datetime-local接受的格式 (yyyy-MM-ddThh:mm:ss)
                    field.value = `${datePart}T${timePart}`;
                }
            } catch (e) {
                console.error('格式化日期时间字段时出错:', e);
            }
        }
    }
    // 验证日期时间格式
    function validateDateTimeFormat(field) {
        const value = field.value;
        if (value) {
            // HTML datetime-local输入会返回ISO格式: yyyy-MM-ddThh:mm
            // 转换为标准格式并预览
            if (value.includes('T')) {
                try {
                    const standardFormat = formatDateTimeToStandard(value);
                    console.log('日期时间转换为标准格式:', standardFormat);

                    // 创建预览提示
                    let formatHint = field.nextElementSibling;
                    if (!formatHint || !formatHint.classList.contains('format-hint')) {
                        formatHint = document.createElement('small');
                        formatHint.className = 'text-muted format-hint';
                        field.parentNode.insertBefore(formatHint, field.nextSibling);
                    }
                    formatHint.textContent = `将保存为: ${standardFormat}`;
                } catch (e) {
                    console.error('日期格式验证失败:', e);
                }
            }
        }
    }

    // 检查是否为编辑模式
    function checkEditMode() {
        // 方法1：检查URL参数
        const urlParams = new URLSearchParams(window.location.search);
        const idParam = urlParams.get('id');

        // 方法2：检查隐藏的instanceId字段
        const instanceIdField = document.querySelector('input[name="instanceId"]');

        return (idParam && idParam !== '') || (instanceIdField && instanceIdField.value && instanceIdField.value !== '');
    }

    // 自动填充表单数据
    function autoFillFormData() {
        console.log('开始自动填充表单数据');

        // 获取当前选中的模板，触发字段显示逻辑
        const templateSelect = document.getElementById('templateId');
        if (templateSelect && templateSelect.value) {
            console.log('当前选中的模板ID:', templateSelect.value);
            // 触发模板选择事件，这会自动设置字段可见性和标签
            const event = new Event('change');
            templateSelect.dispatchEvent(event);
        }

        // 填充日期时间字段
        fillDateTimeFields();

        // 填充人员选择字段
        fillStaffField();

        // 填充其他文本字段（这些通常已经通过Thymeleaf自动填充了）
        fillTextFields();

        console.log('表单数据自动填充完成');
    }

    // 填充日期时间字段
    function fillDateTimeFields() {
        const startTimeField = document.getElementById('startTime');
        const endTimeField = document.getElementById('endTime');

        // 这些字段的值通常已经通过Thymeleaf设置，但可能需要格式转换
        if (startTimeField && startTimeField.value) {
            formatExistingDateTime(startTimeField);
            console.log('开始时间已填充:', startTimeField.value);
        }

        if (endTimeField && endTimeField.value) {
            formatExistingDateTime(endTimeField);
            console.log('结束时间已填充:', endTimeField.value);
        }
    }

    // 填充人员选择字段
    function fillStaffField() {
        const staffField = document.getElementById('staff');
        if (staffField) {
            // 获取已选中的人员数据（通常通过Thymeleaf设置在data属性或value中）
            const selectedStaff = staffField.getAttribute('data-selected') || staffField.value;

            if (selectedStaff) {
                console.log('填充人员选择字段:', selectedStaff);

                // 如果是多选字段且数据是逗号分隔的字符串
                if (selectedStaff.includes(',')) {
                    const staffArray = selectedStaff.split(',').map(s => s.trim());
                    // 设置多选值
                    Array.from(staffField.options).forEach(option => {
                        if (staffArray.includes(option.value)) {
                            option.selected = true;
                        }
                    });
                } else {
                    staffField.value = selectedStaff;
                }

                // 如果使用了Select2，需要触发更新
                if (window.$ && $(staffField).hasClass('select2-control')) {
                    // 延迟执行，确保Select2已初始化
                    setTimeout(() => {
                        $(staffField).trigger('change');
                    }, 500);
                }
            }
        }
    }

    // 填充其他文本字段
    function fillTextFields() {
        // 这些字段通常已经通过Thymeleaf自动填充，这里主要是确认和日志记录
        const fieldsToCheck = [
            'startLocation', 'endLocation',
            'remark1', 'remark2', 'remark3', 'remark4'
        ];

        fieldsToCheck.forEach(fieldId => {
            const field = document.getElementById(fieldId);
            if (field && field.value) {
                console.log(`${fieldId} 已填充:`, field.value);
            }
        });
    }
});