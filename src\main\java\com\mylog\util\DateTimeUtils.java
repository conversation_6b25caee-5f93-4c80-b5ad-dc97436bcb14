package com.mylog.util;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

/**
 * 日期时间工具类，提供统一的日期时间处理方法
 */
public class DateTimeUtils {
    // 法定节假日集合
    private static final java.util.Set<LocalDate> LEGAL_HOLIDAYS = loadLegalHolidays();

    // 加载法定节假日配置文件
    private static java.util.Set<LocalDate> loadLegalHolidays() {
        java.util.Set<LocalDate> holidays = new java.util.HashSet<>();
    java.nio.file.Path path = java.nio.file.Paths.get("holidays/legal_holidays.txt");
        if (java.nio.file.Files.exists(path)) {
            try {
                java.util.List<String> lines = java.nio.file.Files.readAllLines(path);
                for (String line : lines) {
                    line = line.trim();
                    if (line.isEmpty() || line.startsWith("#")) continue;
                    try {
                        if (line.contains("~")) {
                            // 区间格式 2025-10-01~2025-10-07
                            String[] parts = line.split("~");
                            if (parts.length == 2) {
                                LocalDate start = LocalDate.parse(parts[0].trim());
                                LocalDate end = LocalDate.parse(parts[1].trim());
                                LocalDate current = start;
                                while (!current.isAfter(end)) {
                                    holidays.add(current);
                                    current = current.plusDays(1);
                                }
                            }
                        } else {
                            holidays.add(LocalDate.parse(line));
                        }
                    } catch (Exception ignore) {}
                }
            } catch (Exception e) {
                // 读取异常，忽略
            }
        }
        return holidays;
    }
    
    // 中国时区 UTC+8
    public static final ZoneId CHINA_ZONE = ZoneId.of("Asia/Shanghai");
    
    // 标准日期时间格式
    public static final DateTimeFormatter STANDARD_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    /**
     * 获取当前中国时区的LocalDateTime
     * @return 当前中国时区时间
     */
    public static LocalDateTime nowInChina() {
        return ZonedDateTime.now(CHINA_ZONE).toLocalDateTime();
    }
    
    /**
     * 解析日期时间字符串为LocalDateTime对象
     * 只接受标准格式 "yyyy-MM-dd HH:mm:ss"
     * @param dateStr 日期时间字符串
     * @return 解析后的LocalDateTime对象，解析失败返回null
     */
    public static LocalDateTime parseDateTime(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return null;
        }
        
        try {
            // 尝试标准格式解析
            return LocalDateTime.parse(dateStr, STANDARD_FORMATTER);
        } catch (DateTimeParseException e) {
            // 解析失败
            return null;
        }
    }
    
    /**
     * 将LocalDateTime格式化为标准格式字符串
     * @param dateTime 日期时间对象
     * @return 格式化后的字符串，如果输入为null则返回null
     */
    public static String formatDateTime(LocalDateTime dateTime) {
        return dateTime != null ? dateTime.format(STANDARD_FORMATTER) : null;
    }
    
    /**
     * 将ISO格式（如HTML datetime-local控件的值）转换为标准格式
     * @param isoDateTime ISO格式日期时间字符串，如 "yyyy-MM-ddTHH:mm"
     * @return 标准格式字符串，如果转换失败则返回原字符串
     */
    public static String convertIsoToStandard(String isoDateTime) {
        if (isoDateTime == null || isoDateTime.trim().isEmpty()) {
            return null;
        }
        
        try {
            if (isoDateTime.contains("T")) {
                // ISO格式: yyyy-MM-ddThh:mm
                LocalDateTime dateTime = LocalDateTime.parse(isoDateTime);
                return dateTime.format(STANDARD_FORMATTER);
            } else {
                // 可能已经是标准格式
                return isoDateTime;
            }
        } catch (DateTimeParseException e) {
            // 解析失败，返回原字符串
            return isoDateTime;
        }
    }
    
    /**
     * 判断指定日期是否为工作日（周一至周五，且不是法定节假日）
     * @param date 要判断的日期
     * @return 是否为工作日
     */
    public static boolean isWorkingDay(LocalDate date) {
        if (date == null) {
            return false;
        }
        
        DayOfWeek dayOfWeek = date.getDayOfWeek();
        // 周一至周五为工作日，且不是法定节假日dayOfWeek != DayOfWeek.SATURDAY && dayOfWeek != DayOfWeek.SUNDAY &&
        return  !isLegalHoliday(date);
    }
    
    /**
     * 判断指定日期是否为国家法定节假日
     * 目前该方法返回false，表示没有法定节假日数据
     * 未来可以扩展此方法，从数据库或配置文件中加载法定节假日数据
     * 
     * @param date 要判断的日期
     * @return 是否为法定节假日
     */
    public static boolean isLegalHoliday(LocalDate date) {
    if (date == null) return false;
    return LEGAL_HOLIDAYS.contains(date);
    }
    
    /**
     * 计算并设置项目的工期（工作日天数，排除周末和国家法定节假日）
     * @param project 项目对象
     */
    public static void calculateAndSetProjectDuration(com.mylog.model.Project project) {
        try {
            LocalDateTime startDateTime = project.getActualStartDateTime();
            LocalDateTime endDateTime = project.getActualEndDateTime();

            if (startDateTime != null && endDateTime != null) {
                // 计算两个日期之间的工作日天数（排除周末）
                double workingDays = calculateWorkingDays(startDateTime, endDateTime);

                // 创建BigDecimal并保留两位小数
                BigDecimal durationDays = new BigDecimal(workingDays).setScale(2, RoundingMode.HALF_UP);

                // 确保工期至少为0
                project.setDurationDays(durationDays.max(BigDecimal.ZERO));
                project.setActualDurationDays(durationDays.max(BigDecimal.ZERO));
            } else {
                // 如果开始日期或结束日期为空，则工期设为null
                project.setDurationDays(null);
                project.setActualDurationDays(null);
            }
        } catch (Exception e) {
            // 处理计算过程中可能出现的异常
            project.setDurationDays(null);
            project.setActualDurationDays(null);
            throw e;
        }
    }

    /**
     * 计算并设置任务的工期（工作日天数，排除周末和国家法定节假日）
     * @param task 任务对象
     */
    public static void calculateAndSetTaskDuration(com.mylog.model.ProjectTask task) {
        try {
            LocalDateTime startDateTime = task.getActualStartDateTime();
            LocalDateTime endDateTime = task.getActualEndDateTime();

            if (startDateTime != null && endDateTime != null) {
                // 计算两个日期之间的工作日天数（排除周末）
                double workingDays = calculateWorkingDays(startDateTime, endDateTime);

                // 创建BigDecimal并保留两位小数
                BigDecimal durationDays = new BigDecimal(workingDays).setScale(2, RoundingMode.HALF_UP);

                // 确保工期至少为0
                task.setDurationDays(durationDays.max(BigDecimal.ZERO));
            } else {
                // 如果开始日期或结束日期为空，则工期设为null
                task.setDurationDays(null);
            }
        } catch (Exception e) {
            // 处理计算过程中可能出现的异常
            task.setDurationDays(null);
            throw e;
        }
    }

    /**
     * 判断指定日期是否为工作日（周一至周五）
     * @param dateTime 要判断的日期时间
     * @return 是否为工作日
     */
    public static boolean isWorkingDay(LocalDateTime dateTime) {
        if (dateTime == null) {
            return false;
        }
        return isWorkingDay(dateTime.toLocalDate());
    }
    
    /**
     * 计算两个日期之间的工作日天数（排除周末）
     * @param start 开始日期
     * @param end 结束日期
     * @return 工作日天数
     */
    public static double calculateWorkingDays(LocalDateTime start, LocalDateTime end) {
        if (start == null || end == null || start.isAfter(end)) {
            return 0;
        }
        
        // 计算完整的工作日天数
        LocalDate startDate = start.toLocalDate();
        LocalDate endDate = end.toLocalDate();
        
        // 如果开始和结束在同一天
        if (startDate.isEqual(endDate)) {
            if (isWorkingDay(startDate)) {
                // 计算当天的工作时间比例（假设工作时间为9:00-18:00，共9小时）
                int startHour = start.getHour();
                int endHour = end.getHour();
                int startMinute = start.getMinute();
                int endMinute = end.getMinute();
                
                // 简化处理：将当天工作时间比例按24小时制计算
                double totalMinutes = (endHour * 60 + endMinute) - (startHour * 60 + startMinute);
                return totalMinutes / (24 * 60);
            }
            return 0;
        }
        
        // 计算完整的工作日天数
        long fullWorkingDays = 0;
        LocalDate current = startDate.plusDays(1);
        
        while (current.isBefore(endDate)) {
            if (isWorkingDay(current)) {
                fullWorkingDays++;
            }
            current = current.plusDays(1);
        }
        
        // 计算开始日期的剩余工作时间比例
        double startDayRatio = 0;
        if (isWorkingDay(startDate)) {
            // 从开始时间到当天结束的比例
            int startHour = start.getHour();
            int startMinute = start.getMinute();
            double totalMinutesInDay = (24 * 60) - (startHour * 60 + startMinute);
            startDayRatio = totalMinutesInDay / (24 * 60);
        }
        
        // 计算结束日期的工作时间比例
        double endDayRatio = 0;
        if (isWorkingDay(endDate)) {
            // 从当天开始到结束时间的比例
            int endHour = end.getHour();
            int endMinute = end.getMinute();
            double totalMinutesInDay = endHour * 60 + endMinute;
            endDayRatio = totalMinutesInDay / (24 * 60);
        }
        
        // 总和即为工作日天数
        return fullWorkingDays + startDayRatio + endDayRatio;
    }
}
