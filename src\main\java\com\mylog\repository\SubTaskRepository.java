package com.mylog.repository;

import com.mylog.model.SubTask;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface SubTaskRepository extends JpaRepository<SubTask, Long> {
       /**
        * 统计所有状态为“进行中”的子任务数量
        */
       @Query(value = "SELECT COUNT(*) FROM SubTasks WHERE status = '进行中'", nativeQuery = true)
       long countInProgressSubTasks();

       List<SubTask> findByTaskId(Long taskId);

       List<SubTask> findByTaskIdOrderBySequenceNumberAsc(Long taskId);

       @Query(value = "SELECT * FROM SubTasks s WHERE s.taskId = :taskId AND (s.BusinessType IS NULL OR s.BusinessType = '任务') ORDER BY s.CreatedDate DESC", nativeQuery = true)
       List<SubTask> querySubTasksByTaskId(@Param("taskId") Long taskId);

       @Query(value = "SELECT * FROM SubTasks s WHERE s.taskId = :taskId AND  s.BusinessType = '项目' ORDER BY s.CreatedDate DESC", nativeQuery = true)
       List<SubTask> querySubTasksByProjectId(@Param("taskId") Long projectId);

       @Query(value = "SELECT * FROM SubTasks s WHERE s.taskId = :taskId ORDER BY s.CreatedDate DESC", countQuery = "SELECT COUNT(*) FROM SubTasks s WHERE s.taskId = :taskId", nativeQuery = true)
       Page<SubTask> querySubTasksByTaskIdPaged(@Param("taskId") Long taskId, Pageable pageable);

       @Query("SELECT MAX(s.sequenceNumber) FROM SubTask s WHERE s.taskId = :taskId")
       Integer findMaxSequenceNumberByTaskId(@Param("taskId") Long taskId);

       @Query(value = "SELECT * FROM SubTasks s WHERE s.CreatedDate BETWEEN :startDate AND :endDate", nativeQuery = true)
       List<SubTask> findByCreatedDateBetween(
                     @Param("startDate") String startDate,
                     @Param("endDate") String endDate);

       @Query("SELECT s FROM SubTask s WHERE s.logContent LIKE %:keyword%")
       List<SubTask> findByLogContentContaining(@Param("keyword") String keyword);

       @Query(value = "SELECT * FROM SubTasks s WHERE s.taskId = :taskId ORDER BY s.CreatedDate DESC LIMIT 1", nativeQuery = true)
       Optional<SubTask> queryLatestSubTaskByTaskId(@Param("taskId") Long taskId);

       /**
        * 批量获取多个任务的最新子任务
        * 
        * @param taskIds 任务ID列表
        * @return 每个任务的最新子任务
        */
       @Query(value = "SELECT s1.* FROM SubTasks s1 " +
                     "JOIN (SELECT taskId, MAX(CreatedDate) as maxDate FROM SubTasks WHERE taskId IN :taskIds GROUP BY taskId) s2 "
                     +
                     "ON s1.taskId = s2.taskId AND s1.CreatedDate = s2.maxDate", nativeQuery = true)
       List<SubTask> findLatestSubTasksByTaskIds(@Param("taskIds") List<Long> taskIds);

       /**
        * 分页查询所有评论，按创建时间降序排列
        * 
        * @param pageable 分页参数
        * @return 分页的评论列表
        */
       @Query(value = "SELECT * FROM SubTasks ORDER BY CreatedDate DESC", countQuery = "SELECT COUNT(*) FROM SubTasks", nativeQuery = true)
       Page<SubTask> findAllSubTasksPaged(Pageable pageable);

       /**
        * 根据评论内容模糊搜索（分页）
        */
       @Query(value = "SELECT * FROM SubTasks WHERE logContent LIKE %:keyword% ORDER BY CreatedDate DESC", countQuery = "SELECT COUNT(*) FROM SubTasks WHERE logContent LIKE %:keyword%", nativeQuery = true)
       Page<SubTask> findByLogContentContainingPaged(@Param("keyword") String keyword, Pageable pageable);

       /**
        * 根据创建人模糊搜索（分页）
        */
       @Query(value = "SELECT * FROM SubTasks WHERE createdBy LIKE %:createdBy% ORDER BY CreatedDate DESC", countQuery = "SELECT COUNT(*) FROM SubTasks WHERE createdBy LIKE %:createdBy%", nativeQuery = true)
       Page<SubTask> findByCreatedByContainingPaged(@Param("createdBy") String createdBy, Pageable pageable);

       /**
        * 根据创建时间范围搜索（分页）
        */
       @Query(value = "SELECT * FROM SubTasks WHERE CreatedDate BETWEEN :startDate AND :endDate ORDER BY CreatedDate DESC", countQuery = "SELECT COUNT(*) FROM SubTasks WHERE CreatedDate BETWEEN :startDate AND :endDate", nativeQuery = true)
       Page<SubTask> findByCreatedDateBetweenPaged(@Param("startDate") String startDate,
                     @Param("endDate") String endDate, Pageable pageable);

       /**
        * 根据任务名称模糊搜索（分页）- 通过关联查询
        */
       @Query(value = "SELECT s.* FROM SubTasks s " +
                     "LEFT JOIN Tasks t ON s.taskId = t.taskId " +
                     "WHERE (t.taskName LIKE %:taskName% OR s.taskId LIKE %:taskName%) " +
                     "ORDER BY s.CreatedDate DESC", countQuery = "SELECT COUNT(*) FROM SubTasks s " +
                                   "LEFT JOIN Tasks t ON s.taskId = t.taskId " +
                                   "WHERE (t.taskName LIKE %:taskName% OR s.taskId LIKE %:taskName%)", nativeQuery = true)
       Page<SubTask> findByTaskNameContainingPaged(@Param("taskName") String taskName, Pageable pageable);

       /**
        * 多条件搜索评论（分页）
        */
       @Query(value = "SELECT s.* FROM SubTasks s " +
                     "LEFT JOIN Tasks t ON s.taskId = t.taskId " +
                     "WHERE (:taskName IS NULL OR :taskName = '' OR t.taskName LIKE %:taskName% OR s.taskId LIKE %:taskName%) "
                     +
                     "AND (:logContent IS NULL OR :logContent = '' OR s.logContent LIKE %:logContent%) " +
                     "AND (:createdBy IS NULL OR :createdBy = '' OR s.createdBy LIKE %:createdBy%) " +
                     "AND (:status IS NULL OR :status = '' OR s.status = :status) " +
                     "AND (:startDate IS NULL OR :startDate = '' OR s.CreatedDate >= :startDate) " +
                     "AND (:endDate IS NULL OR :endDate = '' OR s.CreatedDate <= :endDate) " +
                     "ORDER BY s.CreatedDate DESC", countQuery = "SELECT COUNT(*) FROM SubTasks s " +
                                   "LEFT JOIN Tasks t ON s.taskId = t.taskId " +
                                   "WHERE (:taskName IS NULL OR :taskName = '' OR t.taskName LIKE %:taskName% OR s.taskId LIKE %:taskName%) "
                                   +
                                   "AND (:logContent IS NULL OR :logContent = '' OR s.logContent LIKE %:logContent%) " +
                                   "AND (:createdBy IS NULL OR :createdBy = '' OR s.createdBy LIKE %:createdBy%) " +
                                   "AND (:status IS NULL OR :status = '' OR s.status = :status) " +
                                   "AND (:startDate IS NULL OR :startDate = '' OR s.CreatedDate >= :startDate) " +
                                   "AND (:endDate IS NULL OR :endDate = '' OR s.CreatedDate <= :endDate)", nativeQuery = true)
       Page<SubTask> findByMultipleConditionsPaged(
                     @Param("taskName") String taskName,
                     @Param("logContent") String logContent,
                     @Param("createdBy") String createdBy,
                     @Param("status") String status,
                     @Param("startDate") String startDate,
                     @Param("endDate") String endDate,
                     Pageable pageable);

       /**
        * 批量分页查询多个任务的所有子任务（用于项目评论列表）
        */
       @Query(value = "SELECT * FROM SubTasks s WHERE s.taskId IN :taskIds ORDER BY s.CreatedDate DESC", countQuery = "SELECT COUNT(*) FROM SubTasks s WHERE s.taskId IN :taskIds", nativeQuery = true)
       Page<SubTask> querySubTasksByTaskIdsPaged(@Param("taskIds") List<Long> taskIds, Pageable pageable);
}