package com.mylog.service.impl;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.WeekFields;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.mylog.model.Project;
import com.mylog.model.ProjectTask;
import com.mylog.model.ViolationRule;
import com.mylog.repository.ProjectRepository;
import com.mylog.repository.ProjectTaskRepository;
import com.mylog.service.ReportService;
import com.mylog.service.TaskService;
import com.mylog.service.ViolationRuleService;

@Service
public class ReportServiceImpl implements ReportService {
    private static final Logger logger = LoggerFactory.getLogger(ReportServiceImpl.class);

    private final ProjectRepository projectRepository;
    private final ProjectTaskRepository taskRepository;
    private final TaskService taskService;
    private final ViolationRuleService violationRuleService;
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Autowired
    public ReportServiceImpl(
            ProjectRepository projectRepository,
            ProjectTaskRepository taskRepository,
            TaskService taskService,
            ViolationRuleService violationRuleService) {
        this.projectRepository = projectRepository;
        this.taskRepository = taskRepository;
        this.taskService = taskService;
        this.violationRuleService = violationRuleService;
    }

    /**
     * 计算给定任务列表中、基于规则和额外天数增量的违规任务。
     * @param tasks 待检查的任务（已应用包含/排除过滤并计算了评论天数）
     * @param rule 违规规则
     * @param deltaDays 在判断评论天数时要额外增加的天数（例如 +2）
     * @return 违规任务列表（带有备注更新）
     */
    /**
     * 一次遍历收集两组违规任务（原始阈值和+2天阈值），提升效率。
     * @return 长度为2的List，分别为原始阈值和+2天阈值违规任务
     */
    private List<List<ProjectTask>> computeViolationsDual(List<ProjectTask> tasks, ViolationRule rule) {
        Double commentDaysThreshold = rule.getCommentDaysThreshold();
        Double specialTaskCommentDaysThreshold = rule.getSpecialTaskCommentDaysThreshold();
        String specialTaskIdentifier = rule.getSpecialTaskIdentifier();

        List<ProjectTask> violationsNormal = new ArrayList<>();
        List<ProjectTask> violationsPlus2 = new ArrayList<>();

        for (ProjectTask task : tasks) {
            String taskName = task.getTaskName();
            Double commentDays = task.getCommentDays();
            Double commentDaysValue = commentDays != null ? commentDays : 0.0;
            Double commentDaysValuePlus2 = commentDaysValue + 2.0;

            boolean isSpecialTask = specialTaskIdentifier != null &&
                    !specialTaskIdentifier.isEmpty() &&
                    taskName != null && taskName.contains(specialTaskIdentifier);

            boolean isViolationNormal = false;
            boolean isViolationPlus2 = false;

            if (isSpecialTask && specialTaskCommentDaysThreshold != null) {
                isViolationNormal = commentDaysValue > specialTaskCommentDaysThreshold;
                isViolationPlus2 = commentDaysValuePlus2 > specialTaskCommentDaysThreshold;
            } else if (commentDaysThreshold != null) {
                isViolationNormal = commentDaysValue > commentDaysThreshold;
                isViolationPlus2 = commentDaysValuePlus2 > commentDaysThreshold;
            }

            if (isViolationNormal) {
                if (logger.isDebugEnabled()) {
                    logger.debug("违规任务: {}, 评论天数: {}, 是否特殊任务: {}, 应用规则: {}",
                            taskName, commentDaysValue, isSpecialTask, rule.getRuleName());
                }
                addScoreRemark(task, rule);
                violationsNormal.add(task);
            }
            if (isViolationPlus2) {
                if (logger.isDebugEnabled()) {
                    logger.debug("违规任务+2天: {}, 评论天数: {}, 是否特殊任务: {}, 应用规则: {}",
                            taskName, commentDaysValuePlus2, isSpecialTask, rule.getRuleName());
                }
                addScoreRemark(task, rule);
                violationsPlus2.add(task);
            }
        }
        List<List<ProjectTask>> result = new ArrayList<>();
        result.add(violationsNormal);
        result.add(violationsPlus2);
        return result;
    }

    /**
     * 备注写入辅助方法
     */
    private void addScoreRemark(ProjectTask task, ViolationRule rule) {
        Integer ruleScore = rule.getScore() != null ? rule.getScore() : 0;
        String existingRemarks = task.getRemarks() != null ? task.getRemarks() : "";
        String scoreInfo = String.format("[违规扣分:%d分-规则:%s]", ruleScore, rule.getRuleName());
        if (!existingRemarks.contains(scoreInfo)) {
            String newRemarks = existingRemarks.isEmpty() ? scoreInfo : existingRemarks + " " + scoreInfo;
            task.setRemarks(newRemarks);
        }
    }

    @Override
    public Map<String, Long> getTaskStatusDistribution(Long projectId, LocalDate startDate, LocalDate endDate) {
        List<ProjectTask> tasks = getTasksByFilter(projectId, startDate, endDate);

        Map<String, Long> statusDistribution = new LinkedHashMap<>();
        statusDistribution.put("未开始", 0L);
        statusDistribution.put("进行中", 0L);
        statusDistribution.put("已完成", 0L);
        statusDistribution.put("已暂停", 0L);

        // 统计各状态的任务数量
        Map<String, Long> counts = tasks.stream()
                .collect(Collectors.groupingBy(ProjectTask::getStatus, Collectors.counting()));

        // 合并到预设的状态分布中
        statusDistribution.putAll(counts);

        return statusDistribution;
    }

    @Override
    public Map<String, Long> getTaskRiskDistribution(Long projectId, LocalDate startDate, LocalDate endDate) {
        List<ProjectTask> tasks = getTasksByFilter(projectId, startDate, endDate);

        Map<String, Long> riskDistribution = new LinkedHashMap<>();
        riskDistribution.put("正常", 0L);
        riskDistribution.put("低", 0L);
        riskDistribution.put("中", 0L);
        riskDistribution.put("高", 0L);

        // 统计各风险等级的任务数量
        Map<String, Long> counts = tasks.stream()
                .collect(Collectors.groupingBy(task -> task.getRisk() != null ? task.getRisk() : "正常",
                        Collectors.counting()));

        // 合并到预设的风险分布中
        riskDistribution.putAll(counts);

        return riskDistribution;
    }

    @Override
    public Map<String, Long> getTaskResponsibleDistribution(Long projectId, LocalDate startDate, LocalDate endDate) {
        List<ProjectTask> tasks = getTasksByFilter(projectId, startDate, endDate);

        // 过滤掉状态为“未开始”的任务
        List<ProjectTask> filteredTasks = tasks.stream()
                .filter(task -> task.getStatus() == null || !"未开始".equals(task.getStatus()))
                .collect(Collectors.toList());

        // 统计各负责人的任务数量
        return filteredTasks.stream()
                .collect(Collectors.groupingBy(ProjectTask::getResponsible, Collectors.counting()));
    }

    @Override
    public List<ProjectTask> getTasksByFilter(Long projectId, LocalDate startDate, LocalDate endDate) {
        LocalDateTime startDateTime = startDate.atStartOfDay();
        LocalDateTime endDateTime = endDate.plusDays(1).atStartOfDay();

        List<ProjectTask> allTasks = taskRepository.findAll();

        // 根据条件筛选任务
        return allTasks.stream()
                .filter(task -> {
                    // 项目ID筛选
                    if (projectId != null && !projectId.equals(task.getProjectId())) {
                        return false;
                    }

                    // 日期范围筛选
                    LocalDateTime taskCreatedDate = task.getCreatedDateTime();
                    if (taskCreatedDate == null) {
                        return false;
                    }

                    return !taskCreatedDate.isBefore(startDateTime) && taskCreatedDate.isBefore(endDateTime);
                })
                .collect(Collectors.toList());
    }

    @Override
    public Map<String, Double> getProjectProgressData(Long projectId, LocalDate startDate, LocalDate endDate) {
        List<Project> projects;

        if (projectId != null) {
            // 如果指定了项目ID，只获取该项目
            Optional<Project> projectOpt = projectRepository.findById(projectId);
            projects = projectOpt.map(Collections::singletonList).orElse(Collections.emptyList());
        } else {
            // 否则获取所有项目
            projects = projectRepository.findAll();
        }

        Map<String, Double> progressData = new LinkedHashMap<>();

        for (Project project : projects) {
            // 获取项目下的所有任务
            List<ProjectTask> projectTasks = taskRepository.findByProjectId(project.getProjectId());

            // 计算项目进度（基于任务完成情况）
            double completedTaskCount = projectTasks.stream()
                    .filter(task -> "已完成".equals(task.getStatus()))
                    .count();

            double progress = projectTasks.isEmpty() ? 0 : (completedTaskCount / projectTasks.size());
            progressData.put(project.getProjectName(), progress);
        }

        return progressData;
    }

    @Override
    public Map<String, Double> getTaskCompletionData(Long projectId, LocalDate startDate, LocalDate endDate) {
        List<ProjectTask> tasks = getTasksByFilter(projectId, startDate, endDate);

        Map<String, Double> completionData = new LinkedHashMap<>();

        for (ProjectTask task : tasks) {
            // 使用任务的完成率（如果有）或根据状态估算
            Double ratio = task.getRatio();
            if (ratio == null) {
                // 根据状态估算完成率
                switch (task.getStatus()) {
                    case "未开始":
                        ratio = 0.0;
                        break;
                    case "进行中":
                        ratio = 0.5;
                        break;
                    case "已完成":
                        ratio = 1.0;
                        break;
                    case "已暂停":
                        ratio = 0.0;
                        break;
                    default:
                        ratio = 0.0;
                }
            }

            completionData.put(task.getTaskName(), ratio);
        }

        return completionData;
    }

    @Override
    public List<ProjectTask> getDelayedTasks(Long projectId, LocalDate startDate, LocalDate endDate) {
        List<ProjectTask> tasks = getTasksByFilter(projectId, startDate, endDate);

        LocalDateTime now = LocalDateTime.now();

        // 筛选进行中但已超过预期完成时间的任务
        return tasks.stream()
                .filter(task -> {
                    if (!"进行中".equals(task.getStatus())) {
                        return false;
                    }

                    // 如果任务有实际开始时间但没有结束时间，且已经开始超过30天，视为延迟
                    LocalDateTime startDateTime = task.getActualStartDateTime();
                    if (startDateTime != null && task.getActualEndDateTime() == null) {
                        long daysBetween = ChronoUnit.DAYS.between(startDateTime, now);
                        return daysBetween > 30; // 假设任务应该在30天内完成
                    }

                    return false;
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<ProjectTask> getHighRiskTasks(Long projectId, LocalDate startDate, LocalDate endDate) {
        List<ProjectTask> tasks = getTasksByFilter(projectId, startDate, endDate);

        // 筛选高任务
        return tasks.stream()
                .filter(task -> "高".equals(task.getRisk()))
                .collect(Collectors.toList());
    }

    @Override
    public Map<String, Map<String, Long>> getRiskTrendData(Long projectId, LocalDate startDate, LocalDate endDate) {
        List<ProjectTask> tasks = getTasksByFilter(projectId, startDate, endDate);

        // 按周分组
        Map<String, List<ProjectTask>> tasksByWeek = new LinkedHashMap<>();

        // 获取周数范围
        int startWeek = startDate.get(WeekFields.ISO.weekOfWeekBasedYear());
        int endWeek = endDate.get(WeekFields.ISO.weekOfWeekBasedYear());
        int startYear = startDate.getYear();
        int endYear = endDate.getYear();

        // 初始化每周的数据
        for (int year = startYear; year <= endYear; year++) {
            int weekStart = (year == startYear) ? startWeek : 1;
            int weekEnd = (year == endYear) ? endWeek : 52;

            for (int week = weekStart; week <= weekEnd; week++) {
                String weekKey = year + "-W" + week;
                tasksByWeek.put(weekKey, new ArrayList<>());
            }
        }

        // 将任务按创建时间分配到对应的周
        for (ProjectTask task : tasks) {
            LocalDateTime createdDateTime = task.getCreatedDateTime();
            if (createdDateTime != null) {
                int year = createdDateTime.getYear();
                int week = createdDateTime.get(WeekFields.ISO.weekOfWeekBasedYear());
                String weekKey = year + "-W" + week;

                if (tasksByWeek.containsKey(weekKey)) {
                    tasksByWeek.get(weekKey).add(task);
                }
            }
        }

        // 计算每周的风险分布
        Map<String, Map<String, Long>> riskTrendData = new LinkedHashMap<>();

        for (Map.Entry<String, List<ProjectTask>> entry : tasksByWeek.entrySet()) {
            String weekKey = entry.getKey();
            List<ProjectTask> weekTasks = entry.getValue();

            // 统计该周各风险等级的任务数量
            Map<String, Long> weekRiskDistribution = new LinkedHashMap<>();
            weekRiskDistribution.put("正常", 0L);
            weekRiskDistribution.put("低", 0L);
            weekRiskDistribution.put("中", 0L);
            weekRiskDistribution.put("高", 0L);

            Map<String, Long> counts = weekTasks.stream()
                    .collect(Collectors.groupingBy(task -> task.getRisk() != null ? task.getRisk() : "正常",
                            Collectors.counting()));

            weekRiskDistribution.putAll(counts);
            riskTrendData.put(weekKey, weekRiskDistribution);
        }

        return riskTrendData;
    }

    @Override
    public Map<String, Map<String, Long>> getResponsibleWorkloadData(Long projectId, LocalDate startDate,
            LocalDate endDate) {
        List<ProjectTask> tasks = getTasksByFilter(projectId, startDate, endDate);

        // 过滤掉状态为“未开始”的任务
        List<ProjectTask> filteredTasks = tasks.stream()
                .filter(task -> task.getStatus() == null || !"未开始".equals(task.getStatus()))
                .collect(Collectors.toList());

        // 按负责人分组
        Map<String, List<ProjectTask>> tasksByResponsible = filteredTasks.stream()
                .collect(Collectors.groupingBy(ProjectTask::getResponsible));

        Map<String, Map<String, Long>> workloadData = new LinkedHashMap<>();

        for (Map.Entry<String, List<ProjectTask>> entry : tasksByResponsible.entrySet()) {
            String responsible = entry.getKey();
            List<ProjectTask> responsibleTasks = entry.getValue();

            // 统计该负责人各状态的任务数量
            Map<String, Long> statusCounts = responsibleTasks.stream()
                    .collect(Collectors.groupingBy(ProjectTask::getStatus, Collectors.counting()));

            workloadData.put(responsible, statusCounts);
        }

        return workloadData;
    }

    @Override
    public Map<String, Double> getResponsibleEfficiencyData(Long projectId, LocalDate startDate, LocalDate endDate) {
        List<ProjectTask> tasks = getTasksByFilter(projectId, startDate, endDate);

        // 过滤掉状态为“未开始”的任务
        List<ProjectTask> filteredTasks = tasks.stream()
                .filter(task -> task.getStatus() == null || !"未开始".equals(task.getStatus()))
                .collect(Collectors.toList());

        // 按负责人分组
        Map<String, List<ProjectTask>> tasksByResponsible = filteredTasks.stream()
                .collect(Collectors.groupingBy(ProjectTask::getResponsible));

        Map<String, Double> efficiencyData = new LinkedHashMap<>();

        for (Map.Entry<String, List<ProjectTask>> entry : tasksByResponsible.entrySet()) {
            String responsible = entry.getKey();
            List<ProjectTask> responsibleTasks = entry.getValue();

            // 计算完成率（已完成任务数 / 总任务数）
            long completedCount = responsibleTasks.stream()
                    .filter(task -> "已完成".equals(task.getStatus()))
                    .count();

            double efficiency = responsibleTasks.isEmpty() ? 0 : (double) completedCount / responsibleTasks.size();
            efficiencyData.put(responsible, efficiency);
        }

        return efficiencyData;
    }

    @Override
    public Map<String, Object> getCustomReportData(String reportType, String groupBy, LocalDate startDate,
            LocalDate endDate) {
        Map<String, Object> reportData = new HashMap<>();

        switch (reportType) {
            case "task":
                if ("status".equals(groupBy)) {
                    reportData.put("chartData", getTaskStatusDistribution(null, startDate, endDate));
                } else if ("responsible".equals(groupBy)) {
                    reportData.put("chartData", getTaskResponsibleDistribution(null, startDate, endDate));
                } else if ("risk".equals(groupBy)) {
                    reportData.put("chartData", getTaskRiskDistribution(null, startDate, endDate));
                }
                break;
            case "progress":
                if ("project".equals(groupBy)) {
                    reportData.put("chartData", getProjectProgressData(null, startDate, endDate));
                } else {
                    reportData.put("chartData", getTaskCompletionData(null, startDate, endDate));
                }
                break;
            case "risk":
                reportData.put("chartData", getTaskRiskDistribution(null, startDate, endDate));
                reportData.put("highRiskTasks", getHighRiskTasks(null, startDate, endDate));
                break;
            case "workload":
                if ("responsible".equals(groupBy)) {
                    reportData.put("chartData", getResponsibleWorkloadData(null, startDate, endDate));
                    reportData.put("efficiencyData", getResponsibleEfficiencyData(null, startDate, endDate));
                }
                break;
        }

        return reportData;
    }

    @Override
    public List<List<ProjectTask>> getViolationTasks() {
        // 获取所有启用的违规规则
        List<ViolationRule> rules = violationRuleService.getAllEnabledRules();

        if (rules.isEmpty()) {
            logger.warn("未找到启用的违规规则，将使用默认规则");
            // 初始化默认规则
            violationRuleService.initDefaultRules();
            rules = violationRuleService.getAllEnabledRules();

            if (rules.isEmpty()) {
                logger.error("无法创建默认规则，违规任务报表将返回空列表");
                return Collections.emptyList();
            }
        }

        logger.debug("找到{}个启用的违规规则", rules.size());

        // 应用规则过滤任务
        List<ProjectTask> allViolations = new ArrayList<>();
        List<ProjectTask> allViolationsPlus2 = new ArrayList<>();

        for (ViolationRule rule : rules) {
            logger.debug("应用规则: {}", rule.getRuleName()); // 根据任务状态过滤
            String taskStatus = rule.getTaskStatus();
            List<ProjectTask> tasksByStatus;

            if (taskStatus == null || taskStatus.trim().isEmpty()) {
                // 如果任务状态为空或null，搜索所有任务
                tasksByStatus = taskRepository.findAll();
                logger.debug("任务状态为空，搜索所有任务: {}", tasksByStatus.size());
            } else {
                tasksByStatus = taskRepository.findByStatus(taskStatus);
                logger.debug("状态为'{}'的任务: {}", taskStatus, tasksByStatus.size());
            }

            // 根据任务名称排除条件过滤
            String excludes = rule.getTaskNameExcludes();
            List<String> excludeList = excludes != null ? Arrays.asList(excludes.split(",")) : Collections.emptyList();

            List<ProjectTask> tasksAfterExcludes = tasksByStatus.stream()
                    .filter(task -> {
                        String taskName = task.getTaskName();
                        return excludeList.stream().noneMatch(
                                exclude -> exclude != null && !exclude.isEmpty() && taskName.contains(exclude.trim()));
                    })
                    .collect(Collectors.toList());

            logger.debug("排除特定名称后的任务: {}", tasksAfterExcludes.size());

            // 根据任务名称包含条件过滤
            String includes = rule.getTaskNameIncludes();
            List<String> includeList = includes != null ? Arrays.asList(includes.split(",")) : Collections.emptyList();

            // 如果包含列表为空，则不执行过滤，保留所有任务
            // 否则只保留名称包含指定字符串的任务
            List<ProjectTask> tasksAfterIncludes;
            if (includeList.isEmpty() || (includeList.size() == 1 && includeList.get(0).isEmpty())) {
                // 包含列表为空，不过滤
                logger.debug("包含条件为空，不执行包含过滤");
                tasksAfterIncludes = tasksAfterExcludes;
            } else {
                // 包含列表不为空，执行过滤
                logger.debug("应用包含条件: {}", includeList);
                tasksAfterIncludes = tasksAfterExcludes.stream()
                        .filter(task -> {
                            String taskName = task.getTaskName();
                            return includeList.stream().anyMatch(include -> include != null && !include.isEmpty()
                                    && taskName.contains(include.trim()));
                        })
                        .collect(Collectors.toList());
            }

            logger.debug("应用包含条件后的任务: {}", tasksAfterIncludes.size());

            // 计算评论天数
            taskService.calculateCommentDays(tasksAfterIncludes);
            logger.debug("已计算{}个任务的评论天数", tasksAfterIncludes.size());

            // 一次遍历收集两组违规任务
            List<List<ProjectTask>> dualViolations = computeViolationsDual(tasksAfterIncludes, rule);
            List<ProjectTask> violationTasksForRule = dualViolations.get(0);
            List<ProjectTask> violationTasksForRule2 = dualViolations.get(1);

            logger.debug("规则'{}'找到{}个违规任务", rule.getRuleName(), violationTasksForRule.size());
            logger.debug("规则'{}'找到{}个违规任务+2天", rule.getRuleName(), violationTasksForRule2.size());

            // 添加到结果列表
            allViolations.addAll(violationTasksForRule);
            allViolationsPlus2.addAll(violationTasksForRule2);
        }

        // 使用任务ID进行去重，避免懒加载异常
        List<ProjectTask> uniqueViolationTasks = allViolations.stream()
                .collect(Collectors.toMap(
                        ProjectTask::getTaskId,
                        task -> task,
                        (existing, replacement) -> existing))
                .values()
                .stream()
                .collect(Collectors.toList());

        logger.info("违规任务报表：筛选出{}个违规任务", uniqueViolationTasks.size());

        List<ProjectTask> uniqueViolationTasks2 = allViolationsPlus2.stream()
                .collect(Collectors.toMap(
                        ProjectTask::getTaskId,
                        task -> task,
                        (existing, replacement) -> existing))
                .values()
                .stream()
                .collect(Collectors.toList());

        logger.info("违规任务+2天报表：筛选出{}个违规任务", uniqueViolationTasks2.size());

        List<List<ProjectTask>> result = new ArrayList<>();
        result.add(uniqueViolationTasks);
        result.add(uniqueViolationTasks2);
        return result;
    }
}