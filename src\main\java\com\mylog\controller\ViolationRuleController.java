package com.mylog.controller;

import com.mylog.model.ReportExportConfig;
import com.mylog.model.ViolationRule;
import com.mylog.service.ReportExportConfigService;
import com.mylog.service.ViolationRuleService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 违规规则控制器
 */
@Controller
@RequestMapping("/violation-rules")
public class ViolationRuleController {

    private static final Logger logger = LoggerFactory.getLogger(ViolationRuleController.class);

    private final ViolationRuleService violationRuleService;
    private final ReportExportConfigService reportExportConfigService;

    public ViolationRuleController(ViolationRuleService violationRuleService,
                                  ReportExportConfigService reportExportConfigService) {
        this.violationRuleService = violationRuleService;
        this.reportExportConfigService = reportExportConfigService;
    }

    /**
     * 显示所有规则
     */
    @GetMapping
    public String listRules(Model model, @RequestParam(required = false) String currentUsername) {
        // 获取所有规则
        List<ViolationRule> rules = violationRuleService.getAllRules();
        model.addAttribute("rules", rules);
        model.addAttribute("currentUsername", currentUsername);

        // 获取报表导出配置
        Optional<ReportExportConfig> configOpt = reportExportConfigService.getConfig();
        ReportExportConfig config = configOpt.orElse(new ReportExportConfig());
        model.addAttribute("config", config);

    return "violation-rules/index";
    }

    /**
     * 显示创建规则表单
     */
    @GetMapping("/create")
    public String showCreateForm(Model model, @RequestParam(required = false) String currentUsername) {
        model.addAttribute("rule", new ViolationRule());
        model.addAttribute("currentUsername", currentUsername);
    return "violation-rules/form";
    }

    /**
     * 保存新规则
     */
    @PostMapping
    public String saveRule(@ModelAttribute ViolationRule rule,
                          @RequestParam(required = false) String currentUsername,
                          RedirectAttributes redirectAttributes) {
        try {
            // 设置创建信息
            rule.setCreatedDateTime(LocalDateTime.now());
            rule.setLastModifiedDateTime(LocalDateTime.now());
            rule.setCreatedBy(currentUsername);
            rule.setLastModifiedBy(currentUsername);

            violationRuleService.saveRule(rule);
            redirectAttributes.addFlashAttribute("successMessage", "规则创建成功");
            logger.info("用户 {} 创建了新的违规规则: {}", currentUsername, rule.getRuleName());
            return "redirect:/violation-rules";
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("errorMessage", "创建规则失败: " + e.getMessage());
            logger.error("创建规则失败", e);
            return "redirect:/violation-rules/create";
        }
    }

    /**
     * 显示编辑规则表单
     */
    @GetMapping("/edit/{id}")
    public String showEditForm(@PathVariable Long id, Model model,
                              @RequestParam(required = false) String currentUsername,
                              RedirectAttributes redirectAttributes) {
        Optional<ViolationRule> ruleOpt = violationRuleService.getRuleById(id);

        if (ruleOpt.isPresent()) {
            model.addAttribute("rule", ruleOpt.get());
            model.addAttribute("currentUsername", currentUsername);
            return "violation-rules/form";
        } else {
            redirectAttributes.addFlashAttribute("errorMessage", "找不到ID为 " + id + " 的规则");
            return "redirect:/violation-rules";
        }
    }

    /**
     * 更新规则
     */
    @PostMapping("/update/{id}")
    public String updateRule(@PathVariable Long id,
                            @ModelAttribute ViolationRule rule,
                            @RequestParam(required = false) String currentUsername,
                            RedirectAttributes redirectAttributes) {
        try {
            Optional<ViolationRule> existingRuleOpt = violationRuleService.getRuleById(id);

            if (existingRuleOpt.isPresent()) {
                ViolationRule existingRule = existingRuleOpt.get();

                // 保留创建信息
                rule.setRuleId(id);
                rule.setCreatedDateTime(existingRule.getCreatedDateTime());
                rule.setCreatedBy(existingRule.getCreatedBy());

                // 更新修改信息
                rule.setLastModifiedDateTime(LocalDateTime.now());
                rule.setLastModifiedBy(currentUsername);

                violationRuleService.saveRule(rule);
                redirectAttributes.addFlashAttribute("successMessage", "规则更新成功");
                logger.info("用户 {} 更新了违规规则: {}", currentUsername, rule.getRuleName());
            } else {
                redirectAttributes.addFlashAttribute("errorMessage", "找不到ID为 " + id + " 的规则");
            }

            return "redirect:/violation-rules";
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("errorMessage", "更新规则失败: " + e.getMessage());
            logger.error("更新规则失败", e);
            return "redirect:/violation-rules/edit/" + id;
        }
    }

    /**
     * 删除规则
     */
    @PostMapping("/delete/{id}")
    public String deleteRule(@PathVariable Long id,
                            @RequestParam(required = false) String currentUsername,
                            RedirectAttributes redirectAttributes) {
        try {
            Optional<ViolationRule> ruleOpt = violationRuleService.getRuleById(id);

            if (ruleOpt.isPresent()) {
                String ruleName = ruleOpt.get().getRuleName();
                violationRuleService.deleteRule(id);
                redirectAttributes.addFlashAttribute("successMessage", "规则删除成功");
                logger.info("用户 {} 删除了违规规则: {}", currentUsername, ruleName);
            } else {
                redirectAttributes.addFlashAttribute("errorMessage", "找不到ID为 " + id + " 的规则");
            }

            return "redirect:/violation-rules";
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("errorMessage", "删除规则失败: " + e.getMessage());
            logger.error("删除规则失败", e);
            return "redirect:/violation-rules";
        }
    }

    /**
     * 启用/禁用规则
     */
    @PostMapping("/toggle/{id}")
    public String toggleRule(@PathVariable Long id,
                            @RequestParam(required = false) String currentUsername,
                            RedirectAttributes redirectAttributes) {
        try {
            Optional<ViolationRule> ruleOpt = violationRuleService.getRuleById(id);

            if (ruleOpt.isPresent()) {
                ViolationRule rule = ruleOpt.get();

                if (rule.getEnabled()) {
                    violationRuleService.disableRule(id);
                    redirectAttributes.addFlashAttribute("successMessage", "规则已禁用");
                    logger.info("用户 {} 禁用了违规规则: {}", currentUsername, rule.getRuleName());
                } else {
                    violationRuleService.enableRule(id);
                    redirectAttributes.addFlashAttribute("successMessage", "规则已启用");
                    logger.info("用户 {} 启用了违规规则: {}", currentUsername, rule.getRuleName());
                }
            } else {
                redirectAttributes.addFlashAttribute("errorMessage", "找不到ID为 " + id + " 的规则");
            }

            return "redirect:/violation-rules";
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("errorMessage", "操作失败: " + e.getMessage());
            logger.error("切换规则状态失败", e);
            return "redirect:/violation-rules";
        }
    }
}
