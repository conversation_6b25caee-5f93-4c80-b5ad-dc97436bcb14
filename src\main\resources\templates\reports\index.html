<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head th:replace="~{fragments/layout :: head('报表查询')}">
    <meta charset="UTF-8">
    <title>报表查询</title>
</head>
<body th:replace="~{fragments/layout :: body(~{::div.content-wrapper}, ~{::script})}">
    <div class="content-wrapper">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">报表查询</h1>
                </div>

                <!-- 报表类型选择卡片 -->
                <div class="row">
                    <div class="col-md-3 mb-4">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-pie-chart fs-1 mb-3 text-primary"></i>
                                <h5 class="card-title">任务统计报表</h5>
                                <p class="card-text">按状态、风险等级和负责人统计任务分布情况</p>
                                <a th:href="@{/reports/task-statistics}" class="btn btn-outline-primary">查看报表</a>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 mb-4">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-graph-up fs-1 mb-3 text-success"></i>
                                <h5 class="card-title">进度跟踪报表</h5>
                                <p class="card-text">项目整体进度统计和各任务完成率对比</p>
                                <a th:href="@{/reports/progress-tracking}" class="btn btn-outline-success">查看报表</a>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 mb-4">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-exclamation-triangle fs-1 mb-3 text-warning"></i>
                                <h5 class="card-title">风险分析报表</h5>
                                <p class="card-text">风险等级分布和高风险任务清单</p>
                                <a th:href="@{/reports/risk-analysis}" class="btn btn-outline-warning">查看报表</a>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 mb-4">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-people fs-1 mb-3 text-info"></i>
                                <h5 class="card-title">人员工作量报表</h5>
                                <p class="card-text">各负责人任务数量和工作量分析</p>
                                <a th:href="@{/reports/workload-analysis}" class="btn btn-outline-info">查看报表</a>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 mb-4">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-sliders fs-1 mb-3 text-purple" style="color: #6f42c1;"></i>
                                <h5 class="card-title">自定义报表</h5>
                                <p class="card-text">根据自定义条件和维度生成个性化报表</p>
                                <a th:href="@{/reports/custom-report}" class="btn btn-outline-secondary" style="border-color: #6f42c1; color: #6f42c1;">查看报表</a>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 mb-4">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-exclamation-triangle fs-1 mb-3 text-warning"></i>
                                <h5 class="card-title">违规登记任务报表</h5>
                                <p class="card-text">显示符合特定条件的违规任务列表</p>
                                <a th:href="@{/reports/violation-tasks}" class="btn btn-outline-warning">查看报表</a>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-3 mb-4">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-gear fs-1 mb-3 text-info"></i>
                                <h5 class="card-title">违规登记任务设置</h5>
                                <p class="card-text">配置违规任务的筛选规则和条件</p>
                                <a th:if="${#authorization.expression('hasRole(''ADMIN'')') or #authentication.name == '邓利鹏'}" th:href="@{/violation-rules(currentUsername=${#authentication.name})}" class="btn btn-outline-info">管理设置</a>
                                
                            </div>
                        </div>
                    </div>
                </div>


    </div>


</body>
</html>