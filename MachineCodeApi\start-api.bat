@echo off
chcp 65001 >nul
echo 启动机器码API服务...
cd /d "%~dp0"
echo 当前目录: %CD%
echo.
echo 检查关键文件...
REM 检查5000端口是否被占用
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :5000') do (
    echo 端口5000已被占用，PID: %%a
    echo 请先释放该端口后再启动服务。
    pause
    exit /b 1
)
if not exist "MachineCodeApi.dll" (
    echo 错误：找不到 MachineCodeApi.dll
    pause
    exit /b 1
)
if not exist "ClassPs.dll" (
    echo 错误：找不到 ClassPs.dll
    pause
    exit /b 1
)
echo.
echo 正在启动API服务...
echo 服务地址: http://localhost:5000
echo Swagger文档: http://localhost:5000/swagger
echo 健康检查: http://localhost:5000/api/machinecode/health
echo.
echo 按 Ctrl+C 停止服务
echo ========================================
dotnet MachineCodeApi.dll --urls "http://localhost:5000"
