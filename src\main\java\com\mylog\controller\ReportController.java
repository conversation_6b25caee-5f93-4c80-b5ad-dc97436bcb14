package com.mylog.controller;

import com.mylog.model.ProjectTask;
import com.mylog.service.ReportService;
import com.mylog.service.OptionsService;
import com.mylog.task.ReportExportTask;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.beans.PropertyEditorSupport;

import java.io.File;
import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import jakarta.servlet.http.HttpServletResponse;
import com.mylog.model.ReportExportConfig;
import com.mylog.service.ReportExportConfigService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Controller
@RequestMapping("/reports")
public class ReportController {    private static final Logger logger = LoggerFactory.getLogger(ReportController.class);

    private final ReportService reportService;
    private final OptionsService optionsService;
    private final ReportExportConfigService reportExportConfigService;
    private final ReportExportTask reportExportTask;

    public ReportController(ReportService reportService, OptionsService optionsService, 
                           ReportExportConfigService reportExportConfigService,
                           ReportExportTask reportExportTask) {
        this.reportService = reportService;
        this.optionsService = optionsService;
        this.reportExportConfigService = reportExportConfigService;
        this.reportExportTask = reportExportTask;
    }

    /**
     * 报表查询主页
     */
    @GetMapping
    public String reportIndex(Model model) {
        model.addAttribute("activeMenu", "reports");

        // 获取报表导出配置，如果不存在则创建新的
        Optional<ReportExportConfig> configOpt = reportExportConfigService.getConfig();
        ReportExportConfig config = configOpt.orElse(new ReportExportConfig());
        model.addAttribute("config", config);

        return "reports/index";
    }

    /**
     * 任务统计报表
     */
    @GetMapping("/task-statistics")
    public String taskStatistics(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            Model model) {

        // 设置默认日期范围（如果未提供）
        if (startDate == null) {
            startDate = LocalDate.now().minusMonths(1);
        }
        if (endDate == null) {
            endDate = LocalDate.now();
        }

        // 限制每页最大显示数量为20
        size = Math.min(size, 20);

        // 获取任务状态分布数据
        Map<String, Long> statusData = reportService.getTaskStatusDistribution(null, startDate, endDate);

        // 获取风险等级分布数据
        Map<String, Long> riskData = reportService.getTaskRiskDistribution(null, startDate, endDate);

        // 获取负责人任务分配数据
        Map<String, Long> responsibleData = reportService.getTaskResponsibleDistribution(null, startDate, endDate);

        // 获取所有符合条件的任务
        List<ProjectTask> allTasks = reportService.getTasksByFilter(null, startDate, endDate);

        // 计算总任务数
        int totalTasks = allTasks.size();

        // 计算总页数
        int totalPages = (int) Math.ceil((double) totalTasks / size);

        // 确保页码在有效范围内
        page = Math.max(0, Math.min(page, totalPages - 1 < 0 ? 0 : totalPages - 1));

        // 分页获取任务
        int fromIndex = page * size;
        int toIndex = Math.min(fromIndex + size, totalTasks);
        List<ProjectTask> pagedTasks = fromIndex < totalTasks ? allTasks.subList(fromIndex, toIndex) : new ArrayList<>();

        model.addAttribute("activeMenu", "reports");
        model.addAttribute("startDate", startDate);
        model.addAttribute("endDate", endDate);
        model.addAttribute("statusData", statusData);
        model.addAttribute("riskData", riskData);
        model.addAttribute("responsibleData", responsibleData);
        model.addAttribute("tasks", pagedTasks);
        model.addAttribute("totalTasks", totalTasks);
        model.addAttribute("currentPage", page);
        model.addAttribute("totalPages", totalPages);
        model.addAttribute("pageSize", size);

        return "reports/task-statistics";
    }

    /**
     * 进度跟踪报表
     */
    @GetMapping("/progress-tracking")
    public String progressTracking(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            Model model) {

        // 设置默认日期范围（如果未提供）
        if (startDate == null) {
            startDate = LocalDate.now().minusMonths(1);
        }
        if (endDate == null) {
            endDate = LocalDate.now();
        }

        // 获取项目进度数据
        Map<String, Double> projectProgressData = reportService.getProjectProgressData(null, startDate, endDate);

        // 获取任务完成率数据
        Map<String, Double> taskCompletionData = reportService.getTaskCompletionData(null, startDate, endDate);

        // 获取进度延迟任务
        List<ProjectTask> delayedTasks = reportService.getDelayedTasks(null, startDate, endDate);

        model.addAttribute("activeMenu", "reports");
        model.addAttribute("startDate", startDate);
        model.addAttribute("endDate", endDate);
        model.addAttribute("projectProgressData", projectProgressData);
        model.addAttribute("taskCompletionData", taskCompletionData);
        model.addAttribute("delayedTasks", delayedTasks);

        return "reports/progress-tracking";
    }

    /**
     * 风险分析报表
     */
    @GetMapping("/risk-analysis")
    public String riskAnalysis(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            Model model) {

        // 设置默认日期范围（如果未提供）
        if (startDate == null) {
            startDate = LocalDate.now().minusMonths(1);
        }
        if (endDate == null) {
            endDate = LocalDate.now();
        }

        // 获取风险等级分布数据
        Map<String, Long> riskDistributionData = reportService.getTaskRiskDistribution(null, startDate, endDate);

        // 获取高风险任务列表
        List<ProjectTask> highRiskTasks = reportService.getHighRiskTasks(null, startDate, endDate);

        // 获取风险趋势数据（按周统计）
        Map<String, Map<String, Long>> riskTrendData = reportService.getRiskTrendData(null, startDate, endDate);

        model.addAttribute("activeMenu", "reports");
        model.addAttribute("startDate", startDate);
        model.addAttribute("endDate", endDate);
        model.addAttribute("riskDistributionData", riskDistributionData);
        model.addAttribute("highRiskTasks", highRiskTasks);
        model.addAttribute("riskTrendData", riskTrendData);

        return "reports/risk-analysis";
    }

    /**
     * 人员工作量报表
     */
    @GetMapping("/workload-analysis")
    public String workloadAnalysis(
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            Model model) {

        // 设置默认日期范围（如果未提供）
        if (startDate == null) {
            startDate = LocalDate.now().minusMonths(1);
        }
        if (endDate == null) {
            endDate = LocalDate.now();
        }

        // 获取各负责人任务数量统计
        Map<String, Long> responsibleTaskCountData = reportService.getTaskResponsibleDistribution(null, startDate, endDate);

        // 获取各负责人工作量分析（基于任务数量和完成情况）
        Map<String, Map<String, Long>> responsibleWorkloadData = reportService.getResponsibleWorkloadData(null, startDate, endDate);

        // 获取人员效率对比（基于任务完成率）
        Map<String, Double> responsibleEfficiencyData = reportService.getResponsibleEfficiencyData(null, startDate, endDate);

        // 从OptionsService获取人员列表
        List<String> personnelList = optionsService.getPersonnel();

        // 计算每个人员的总任务数并按总任务数从高到低排序
        Map<String, Integer> personTotalTasks = new HashMap<>();
        for (String person : personnelList) {
            Map<String, Long> workloadData = responsibleWorkloadData.get(person);
            int totalTasks = 0;
            if (workloadData != null) {
                // 累加每种状态的任务数
                for (Long count : workloadData.values()) {
                    totalTasks += count != null ? count.intValue() : 0;
                }
            }
            personTotalTasks.put(person, totalTasks);
        }

        // 对人员列表按总任务数从高到低排序
        personnelList.sort((person1, person2) ->
            personTotalTasks.getOrDefault(person2, 0).compareTo(personTotalTasks.getOrDefault(person1, 0))
        );

        model.addAttribute("activeMenu", "reports");
        model.addAttribute("startDate", startDate);
        model.addAttribute("endDate", endDate);
        model.addAttribute("responsibleTaskCountData", responsibleTaskCountData);
        model.addAttribute("responsibleWorkloadData", responsibleWorkloadData);
        model.addAttribute("responsibleEfficiencyData", responsibleEfficiencyData);
        model.addAttribute("personnelList", personnelList);

        return "reports/workload-analysis";
    }

    /**
     * 违规登记任务报表
     */
    @GetMapping("/violation-tasks")
    public String violationTasks(Model model) {
        // 获取违规任务列表
        logger.info("开始获取违规任务列表...");
        long startTime = System.currentTimeMillis();

        List<List<ProjectTask>> result = reportService.getViolationTasks();
        List<ProjectTask> violationTasks = result.get(0);

        long endTime = System.currentTimeMillis();
        logger.info("获取违规任务列表完成，耗时: {} 毫秒，找到 {} 个违规任务", (endTime - startTime), violationTasks.size());

        // 设置必要的变量
        model.addAttribute("activeMenu", "reports");
        model.addAttribute("tasks", violationTasks);

        return "reports/violation-tasks";
    }    /**
     * 导出违规任务报表为Excel
     */    @GetMapping("/violation-tasks/export")
    @ResponseBody
    public String exportViolationTasks(
            HttpServletResponse response, 
            @RequestParam(name = "sendWeixinMessage", defaultValue = "false") boolean sendWeixinMessage            ) throws IOException {
        response.setContentType("application/json;charset=UTF-8");
        
        // 记录参数值，便于调试
        logger.info("收到导出请求，发送微信消息参数值: {}", sendWeixinMessage);
        
        try {
            // 直接调用ReportExportTask中的导出方法，获取导出文件路径，并传递参数
            String exportedFilePath = reportExportTask.exportViolationTasksReport(sendWeixinMessage, true);
            
            // 如果返回null，表示没有导出文件
            if (exportedFilePath == null) {
                return "{\"success\": false, \"message\": \"未能导出报表，请检查配置或日志\"}";
            }
            
            // 获取导出目录
            File exportedFile = new File(exportedFilePath);
            String exportPath = exportedFile.getParent();
            
            // 使用JSON字符串格式化，正确处理路径中的特殊字符
            String escapedPath = exportPath.replace("\\", "\\\\");
            return "{\"success\": true, \"message\": \"违规任务报表导出成功，请在导出目录查看: " + escapedPath + "\"}";
        } catch (Exception e) {
            logger.error("导出违规任务报表时发生错误: {}", e.getMessage(), e);
            // 确保错误消息中的特殊字符被正确转义
            String escapedErrorMsg = e.getMessage().replace("\"", "\\\"").replace("\\", "\\\\");
            return "{\"success\": false, \"message\": \"导出报表时发生错误：" + escapedErrorMsg + "\"}";
        }
    }

    /**
     * 自定义报表
     */
    @GetMapping("/custom-report")
    public String customReport(
            @RequestParam(required = false) String reportType,
            @RequestParam(required = false) String groupBy,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            Model model) {

        // 如果没有提供报表类型或分组维度，则显示配置页面
        if (reportType == null || groupBy == null) {
            model.addAttribute("noData", true);
            model.addAttribute("reportType", "task"); // 默认选择任务报表
            model.addAttribute("groupBy", "status");  // 默认按状态分组
            model.addAttribute("activeMenu", "reports");

            // 设置默认日期范围
            if (startDate == null) {
                startDate = LocalDate.now().minusMonths(1);
            }
            if (endDate == null) {
                endDate = LocalDate.now();
            }

            model.addAttribute("startDate", startDate);
            model.addAttribute("endDate", endDate);

            return "reports/custom-report";
        }

        // 设置默认日期范围（如果未提供）
        if (startDate == null) {
            startDate = LocalDate.now().minusMonths(1);
        }
        if (endDate == null) {
            endDate = LocalDate.now();
        }

        // 根据报表类型和分组维度生成报表标题
        String reportTitle = generateReportTitle(reportType, groupBy);

        // 获取自定义报表数据
        Map<String, Object> customData = reportService.getCustomReportData(reportType, groupBy, startDate, endDate);

        // 根据报表类型获取相关任务数据
        List<ProjectTask> tasks = new ArrayList<>();
        if (reportType.equals("task") || reportType.equals("risk")) {
            tasks = reportService.getTasksByFilter(null, startDate, endDate);
        }

        // 将数据添加到模型中
        model.addAttribute("activeMenu", "reports");
        model.addAttribute("reportTitle", reportTitle);
        model.addAttribute("reportType", reportType);
        model.addAttribute("groupBy", groupBy);
        model.addAttribute("startDate", startDate);
        model.addAttribute("endDate", endDate);
        model.addAttribute("reportData", customData.get("reportData"));
        model.addAttribute("tasks", tasks);

        return "reports/custom-report";
    }

    /**
     * 根据报表类型和分组维度生成报表标题
     */
    /**
     * 初始化绑定器，用于处理LocalTime类型的属性
     */
    @InitBinder
    public void initBinder(WebDataBinder binder) {
        // 注册 LocalTime 类型的属性编辑器，只处理小时部分
        binder.registerCustomEditor(LocalTime.class, new PropertyEditorSupport() {
            @Override
            public void setAsText(String text) {
                if (text == null || text.trim().isEmpty()) {
                    setValue(null);
                } else {
                    // 处理格式如 "14:00"
                    String[] parts = text.split(":");
                    if (parts.length > 0) {
                        try {
                            int hour = Integer.parseInt(parts[0]);
                            // 创建只有小时的 LocalTime，分钟和秒都设为 0
                            setValue(LocalTime.of(hour, 0, 0));
                        } catch (NumberFormatException e) {
                            setValue(null);
                        }
                    } else {
                        setValue(null);
                    }
                }
            }

            @Override
            public String getAsText() {
                LocalTime value = (LocalTime) getValue();
                if (value == null) {
                    return "";
                }
                return value.format(DateTimeFormatter.ofPattern("H:00"));
            }
        });
    }

    /**
     * 保存报表导出配置
     */
    @PostMapping("/report-export-config/save")
    public String saveConfig(ReportExportConfig config, RedirectAttributes redirectAttributes) {
        // 获取现有配置，保留lastExportTime字段的值
        Optional<ReportExportConfig> existingConfigOpt = reportExportConfigService.getConfig();
        if (existingConfigOpt.isPresent()) {
            ReportExportConfig existingConfig = existingConfigOpt.get();
            // 保留最后导出时间
            config.setLastExportTime(existingConfig.getLastExportTime());
        }

        reportExportConfigService.saveConfig(config);
        redirectAttributes.addFlashAttribute("successMessage", "配置已保存");
        return "redirect:/reports";
    }

    private String generateReportTitle(String reportType, String groupBy) {
        StringBuilder title = new StringBuilder();

        // 报表类型部分
        switch (reportType) {
            case "task":
                title.append("任务");
                break;
            case "project":
                title.append("项目");
                break;
            case "risk":
                title.append("风险");
                break;
            case "personnel":
                title.append("人员");
                break;
            default:
                title.append("自定义");
        }

        // 分组维度部分
        switch (groupBy) {
            case "status":
                title.append("状态分布");
                break;
            case "responsible":
                title.append("责任人分布");
                break;
            case "risk":
                title.append("风险等级分布");
                break;
            case "date":
                title.append("时间趋势");
                break;
            case "efficiency":
                title.append("效率比较");
                break;
            default:
                title.append("统计");
        }

        return title.toString();
    }
}