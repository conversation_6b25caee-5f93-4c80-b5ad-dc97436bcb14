<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">

<head th:replace="~{fragments/layout :: head(${project.projectName} + ' - 项目详情')}">
    <meta charset="UTF-8">
    <title>项目详情</title>
</head>

<body th:replace="~{fragments/layout :: body(~{::div.content-wrapper}, ~{::script#customScript})}">
    <div class="content-wrapper">
        <div
            class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
            <h1 class="h2" th:text="${project.projectName}">项目详情</h1>
            <div class="btn-toolbar mb-2 mb-md-0">
                <div class="btn-group me-2">
                    <button onclick="return handleBack(event)" class="btn btn-sm btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i> 返回上一页
                    </button>
                    <!-- 计算所有任务的额定工期按钮 - 权限检查 -->
                    <button th:if="${#authorization.expression('hasRole(''ADMIN'')') ||
                                    #authentication.name == project.responsible}" type="button"
                        class="btn btn-sm btn-outline-info" onclick="calculateFilterTasksRatedDuration(this)"
                        th:attr="data-project-id=${project.projectId}">
                        <i class="bi bi-calculator"></i> 计算各任务的额定工期
                    </button>
                    <!-- 提交按钮 - 权限检查 -->
                    <button th:if="${(#authorization.expression('hasRole(''ADMIN'')') ||
                                    #authentication.name == project.responsible)
                                   && project.status != '已完成'
                                   && (project.archive == null || project.archive != 1)}" type="button"
                        class="btn btn-sm btn-outline-success" data-bs-toggle="modal" data-bs-target="#completeModal"
                        th:attr="data-project-id=${project.projectId},
                                 data-project-name=${project.projectName}">
                        <i class="bi bi-check-circle"></i> 提交
                    </button> <!-- 编辑按钮 - 权限检查 -->
                    <a th:if="${(#authorization.expression('hasRole(''ADMIN'')') ||
                                (#authorization.expression('hasRole(''MANAGER'')') && #authentication.name == project.responsible) ||
                                (#authorization.expression('hasRole(''MANAGER'')') && #authentication.name == project.createdBy) ||
                                #authentication.name == '邓利鹏'
                                 )
                               && (project.archive == null || project.archive != 1)}"
                        th:href="@{/projects/{id}/edit(id=${project.projectId})}"
                        class="btn btn-sm btn-outline-primary">
                        <i class="bi bi-pencil"></i> 编辑
                    </a>
                    <!-- 归档按钮 - 权限检查 -->
                    <button th:if="${(#authorization.expression('hasRole(''ADMIN'')') ||
                                    (#authorization.expression('hasRole(''MANAGER'')') &&
                                     #authentication.name == project.responsible))
                                   && (project.archive == null || project.archive != 1)}" type="button"
                        class="btn btn-sm btn-outline-warning" th:attr="data-project-id=${project.projectId},
                                    data-project-name=${project.projectName}" onclick="checkTasksBeforeArchive(this)">
                        <i class="bi bi-archive"></i> 归档
                    </button>
                    <!-- 取消归档按钮 - 权限检查 -->
                    <button th:if="${(#authorization.expression('hasRole(''ADMIN'')') ||
                                    (#authorization.expression('hasRole(''MANAGER'')') &&
                                     #authentication.name == project.responsible))
                                   && project.archive == 1}" type="button" class="btn btn-sm btn-outline-info"
                        data-bs-toggle="modal" data-bs-target="#unarchiveModal" th:attr="data-project-id=${project.projectId},
                                    data-project-name=${project.projectName}">
                        <i class="bi bi-archive"></i> 取消归档
                    </button> <!-- 删除按钮 - 权限检查 -->
                    <button th:if="${#authorization.expression('hasRole(''ADMIN'')') ||
                                    (#authorization.expression('hasRole(''MANAGER'')') &&
                                     #authentication.name == project.responsible) ||
                                    #authentication.name == project.createdBy}" type="button"
                        class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteModal"
                        th:attr="data-project-id=${project.projectId},
                                    data-project-name=${project.projectName}">
                        <i class="bi bi-trash"></i> 删除
                    </button>
                </div>
            </div>
        </div>

        <!-- 基本信息 -->
        <div class="card mb-3">
            <div class="card-header py-2">
                <h5 class="mb-0">基本信息</h5>
            </div>
            <div class="card-body py-2">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th style="width: 35%">项目名称：</th>
                                <td th:text="${project.projectName}">项目名称</td>
                            </tr>
                            <tr>
                                <th>项目ID：</th>
                                <td th:text="${project.projectId}">0</td>
                            </tr>
                            <tr>
                                <th>项目编号：</th>
                                <td th:text="${project.projectCode}">项目编号</td>
                            </tr>
                            <tr>
                                <th>客户名称：</th>
                                <td th:text="${project.customerName}">客户名称</td>
                            </tr>
                            <tr>
                                <th>设备数量：</th>
                                <td
                                    th:text="${project.quantity != null ? #numbers.formatDecimal(project.quantity, 1, 2) : '-'}">
                                    -</td>
                            </tr>
                            <tr>
                                <th>相机数量：</th>
                                <td
                                    th:text="${project.cameraQuantity != null ? #numbers.formatDecimal(project.cameraQuantity, 1, 0) + ' 个' : '-'}">
                                    -</td>
                            </tr>

                            <tr>
                                <th>难度系数：</th>
                                <td
                                    th:text="${project.difficultyCoefficient != null ? #numbers.formatDecimal(project.difficultyCoefficient, 1, 2) : '-'}">
                                    -</td>
                            </tr>

                            <tr>
                                <th>额定工期(天)：</th>
                                <td
                                    th:text="${project.ratedDurationDays != null ? #numbers.formatDecimal(project.ratedDurationDays, 1, 2) + ' 天' : '-'}">
                                    -</td>
                            </tr>
                            <tr>
                                <th>绩效分：</th>
                                <td
                                    th:text="${project.bonus != null ? #numbers.formatDecimal(project.bonus, 1, 2) + ' ' : '-'}">
                                    -</td>
                            </tr>
                            <tr>
                                <th>状态：</th>
                                <td>
                                    <span th:class="${'badge ' +
                                        (project.status == '进行中' ? 'bg-primary' :
                                        (project.status == '已完成' ? 'bg-success' :
                                        (project.status == '已暂停' ? 'bg-danger' :
                                        (project.status == '已取消' ? 'bg-dark' : 'bg-secondary'))))}"
                                        th:text="${project.status}">状态</span>
                                </td>
                            </tr>
                            <tr>
                                <th>备注：</th>
                                <td th:text="${project.remarks ?: '-'}">-</td>
                            </tr>


                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th style="width: 35%">视觉类型：</th>
                                <td
                                    th:text="${project.visionType != null ? project.visionType.replace(',', '、') : '-'}">
                                    -</td>
                            </tr>
                            <tr>
                                <th>销售订单号：</th>
                                <td th:text="${project.salesOrderNumber}">-</td>
                            </tr>
                            <tr>
                                <th>料号：</th>
                                <td th:text="${project.productPartNumber}">-</td>
                            </tr>
                            <tr>
                                <th>单机成本1：</th>
                                <td
                                    th:text="${project.visionCost != null ? #numbers.formatDecimal(project.visionCost, 1, 2) + ' 元' : '-'}">
                                    -</td>
                            </tr>
                            <tr>
                                <th>总成本1：</th>
                                <td
                                    th:text="${project.totalCost1 != null ? #numbers.formatDecimal(project.totalCost1, 1, 2) + ' 元' : '-'}">
                                    -</td>
                            </tr>
                            <tr>
                                <th>单机成本2：</th>
                                <td
                                    th:text="${project.visionCost2 != null ? #numbers.formatDecimal(project.visionCost2, 1, 2) + ' 元' : '-'}">
                                    -</td>
                            </tr>
                            <tr>
                                <th>总成本2：</th>
                                <td
                                    th:text="${project.totalCost2 != null ? #numbers.formatDecimal(project.totalCost2, 1, 2) + ' 元' : '-'}">
                                    -</td>
                            </tr>
                            <tr>
                                <th>项目类型：</th>
                                <td th:text="${project.projectType ?: '-'}">-</td>
                            </tr>
                            <tr>
                                <th>创建者：</th>
                                <td th:text="${project.createdBy ?: '-'}">-</td>
                            </tr>

                            <tr>
                                <th>创建时间：</th>
                                <td th:text="${project.createdDate}">2023-01-01 12:00</td>
                            </tr>


                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 负责人信息 -->
        <div class="card mb-3">
            <div class="card-header py-2">
                <h5 class="mb-0">负责人信息</h5>
            </div>
            <div class="card-body py-2">
                <div class="row">
                    <div class="col-md-12">
                        <table class="table table-borderless">
                            <tr>
                                <th style="width: 15%">项目负责人：</th>
                                <td style="width: 35%">
                                    <span class="badge bg-primary fs-6"
                                        th:text="${project.responsible ?: '未指定'}">张三</span>
                                </td>
                                <th style="width: 15%">销售负责人：</th>
                                <td style="width: 35%" th:text="${project.salesResponsible ?: '未指定'}">李四</td>
                            </tr>
                            <tr>
                                <th>机械负责人：</th>
                                <td th:text="${project.mechanicalResponsible ?: '未指定'}">王五</td>
                                <th>电气负责人：</th>
                                <td th:text="${project.electricalResponsible ?: '未指定'}">赵六</td>
                            </tr>
                            <tr>
                                <th>监理人1：</th>
                                <td>
                                    <span class="badge bg-info fs-6"
                                        th:text="${project.supervisor1 ?: '未指定'}">监理人1</span>
                                </td>
                                <th>监理人2：</th>
                                <td>
                                    <span class="badge bg-info fs-6"
                                        th:text="${project.supervisor2 ?: '未指定'}">监理人2</span>
                                </td>
                            </tr>
                            <tr sec:authorize="hasRole('ADMIN')">
                                <th>注意事项：</th>
                                <td colspan="3" th:text="${project.note ?: '未设置'}">-</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 计划信息 -->
        <div class="card mb-3">
            <div class="card-header py-2">
                <h5 class="mb-0">计划信息</h5>
            </div>
            <div class="card-body py-2">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th style="width: 35%">计划开始日期：</th>
                                <td th:text="${project.plannedStartDate != null ? project.plannedStartDate : '未设置'}">
                                    -</td>
                            </tr>
                            <tr>
                                <th>计划结束日期：</th>
                                <td th:text="${project.plannedEndDate != null ? project.plannedEndDate : '未设置'}">
                                    -</td>
                            </tr>
                            <tr>
                                <th>计划天数：</th>
                                <td
                                    th:text="${project.durationDays != null ? #numbers.formatDecimal(project.durationDays, 1, 2) + ' 天' : '未设置'}">
                                    -</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th style="width: 35%">实际开始日期：</th>
                                <td th:text="${project.actualStartDate != null ? project.actualStartDate : '尚未开始'}">
                                    -</td>
                            </tr>
                            <tr>
                                <th>实际结束日期：</th>
                                <td th:text="${project.actualEndDate != null ? project.actualEndDate : '尚未完成'}">
                                    -</td>
                            </tr>
                            <tr>
                                <th>实际工期：</th>
                                <td
                                    th:text="${project.actualDurationDays != null ? #numbers.formatDecimal(project.actualDurationDays, 1, 2) + ' 天' : '未设置'}">
                                    -</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 项目任务列表 -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">项目任务 (<span th:text="${taskPage.totalElements}">0</span>) |
                    额定工期总数: <span
                        th:text="${totalRatedDuration != null ? #numbers.formatDecimal(totalRatedDuration, 1, 2) + ' 天' : '0.00 天'}">0.00
                        天</span> |
                    实际工期总数: <span
                        th:text="${totalDuration != null ? #numbers.formatDecimal(totalDuration, 1, 2) + ' 天' : '0.00 天'}">0.00
                        天</span> |
                    总绩效分: <span
                        th:text="${totalBonus != null ? #numbers.formatDecimal(totalBonus, 1, 2) : '0.00'}">0.00</span>
                    |
                    比例总数: <span th:class="${totalRatio != null && totalRatio != 1.0 ? 'text-danger fw-bold' : ''}"
                        th:text="${totalRatio != null ? #numbers.formatDecimal(totalRatio, 1, 3) : '0.000'}">0.000</span>
                </h5>
                <a th:if="${(#authorization.expression('hasRole(''ADMIN'')') || #authorization.expression('hasRole(''MANAGER'')'))
                         && (project.archive == null || project.archive != 1)}"
                    th:href="@{/tasks/new(projectId=${project.projectId})}" class="btn btn-sm btn-primary">
                    <i class="bi bi-plus"></i> 添加任务
                </a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th style="white-space: nowrap;">任务名称</th>
                                <th style="white-space: nowrap;">负责人</th>
                                <th style="white-space: nowrap;">创建者</th>
                                <th style="white-space: nowrap;">风险</th>
                                <th style="white-space: nowrap;">进度</th>
                                <th style="white-space: nowrap;">状态</th>
                                <th style="white-space: nowrap;">评论</th>
                                <th style="white-space: nowrap;">比例</th>
                                <th style="white-space: nowrap;">额定工期</th>
                                <th style="white-space: nowrap;">累计工期</th>
                                <th style="white-space: nowrap;">剩余工期</th>
                                <th style="white-space: nowrap;">工期(天)</th>
                                <th style="white-space: nowrap;">绩效分</th>
                                <th style="white-space: nowrap;">实际结束时间</th>
                                <th style="white-space: nowrap;">创建时间</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr th:each="task : ${taskPage.content}">
                                <td>
                                    <a th:href="@{/tasks/{id}(id=${task.taskId})}" th:style="${task.taskName != null && #strings.length(task.taskName) >= 2 && 
                                                 #lists.contains(highlightPrefixes, #strings.substring(task.taskName, 0, 2)) ? 
                                                 'color: green; font-weight: bold;' : ''}"
                                        th:text="${task.taskName}">任务名称</a>
                                </td>
                                <td th:text="${task.responsible}">
                                    负责人
                                </td>
                                <td th:text="${task.createdBy ?: '-'}">创建者</td>
                                <td>
                                    <span
                                        th:class="${task.risk == '高' ? 'badge bg-danger' : (task.risk == '中' ? 'badge bg-warning' : 'badge bg-success')}"
                                        th:text="${task.risk}">风险</span>
                                </td>
                                <td>
                                    <div class="progress">
                                        <div class="progress-bar" role="progressbar"
                                            th:style="'width: ' + (${task.progressPercentage} ?: 0) + '%'" th:class="${'progress-bar ' +
                                              ((task.progressPercentage ?: 0) <= 30 ? 'bg-danger' :
                                              ((task.progressPercentage ?: 0) <= 70 ? 'bg-warning' : 'bg-success'))}"
                                            th:text="(${task.progressPercentage} ?: 0) + '%'">0%</div>
                                    </div>
                                </td>
                                </td>
                                <td> <span th:class="${'badge ' +
                                        (task.status == '进行中' ? 'bg-primary' :
                                        (task.status == '已完成' ? 'bg-success' :
                                        (task.status == '未开始' ? 'bg-secondary' :
                                        (task.status == '已暂停' ? 'bg-dark' : 'bg-secondary'))))}"
                                        th:text="${task.status}">状态</span>
                                </td>
                                <td>
                                    <span th:if="${task.commentDays != null}" th:class="'badge ' +
                                            ((${task.commentDays} >= 7) ? 'bg-danger' :
                                            ((${task.commentDays} >= 4) ? 'bg-warning' :
                                            ((${task.commentDays} >= 0) ? 'bg-success' : 'bg-dark')))"
                                        th:text="${task.commentDays}">0.0</span> <span
                                        th:unless="${task.commentDays != null}">-</span>
                                </td>
                                <td th:text="${task.ratio != null ? #numbers.formatDecimal(task.ratio, 1, 3) : '-'}"
                                    style="white-space: nowrap;">-</td>
                                <td th:text="${task.ratedDurationDays != null ? #numbers.formatDecimal(task.ratedDurationDays, 1, 2) + ' 天' : '-'}"
                                    style="white-space: nowrap;">-</td>
                                <td th:text="${task.cumulativeDurationDays != null ? #numbers.formatDecimal(task.cumulativeDurationDays, 1, 2) + ' 天' : '-'}"
                                    style="white-space: nowrap;">-</td>
                                <td th:text="${task.remainingDurationDays != null ? #numbers.formatDecimal(task.remainingDurationDays, 1, 2) + ' 天' : '-'}"
                                    th:style="${task.remainingDurationDays != null && task.remainingDurationDays < 0 ? 'white-space: nowrap; color: red; font-weight: bold;' : 'white-space: nowrap;'}"
                                    style="white-space: nowrap;">-</td>

                                <td th:text="${task.durationDays != null ? #numbers.formatDecimal(task.durationDays, 1, 2) + ' 天' : '-'}"
                                    style="white-space: nowrap;">-</td>
                                <td th:text="${task.bonus != null ? #numbers.formatDecimal(task.bonus, 1, 2) : '-'}"
                                    style="white-space: nowrap;">-</td>
                                <td th:text="${task.actualEndDate ?: '尚未完成'}" style="white-space: nowrap;">-</td>
                                <td th:text="${task.createdDate}">创建时间</td>
                            </tr>
                            <tr th:if="${taskPage.empty}">
                                <td colspan="14" class="text-center">暂无任务</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 使用通用分页组件 -->
                <div class="mt-4" th:if="${taskPage.totalPages > 0}">
                    <div
                        th:replace="~{fragments/pagination :: pagination(${taskPage}, '/projects/' + ${project.projectId})}">
                    </div>
                </div>
            </div>
        </div>

        <!-- 项目工期记录 -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">项目工期记录 (<span th:text="${workHoursPage.totalElements}">0</span>)</h5>
                <div class="d-flex align-items-center">
                    <span class="badge bg-info me-2"
                        th:text="'累计工期: ' + ${#numbers.formatDecimal(totalProjectHours, 1, 1)} + ' 天'">累计工期: 0.0
                        天</span>
                    <a th:if="${#authorization.expression('hasRole(''ADMIN'')') || #authorization.expression('hasRole(''MANAGER'')')}"
                        th:href="@{/work-hours/new(businessType='项目',businessId=${project.projectId})}"
                        class="btn btn-sm btn-outline-primary">
                        <i class="bi bi-plus"></i> 登记工期
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div th:if="${workHoursPage.empty}" class="alert alert-info">
                    <i class="bi bi-info-circle me-2"></i>暂无工期记录。
                </div>

                <div th:unless="${workHoursPage.empty}" class="table-responsive">
                    <table class="table table-striped table-hover table-sm">
                        <thead>
                            <tr>
                                <th style="width: 11%">登记时间</th>
                                <th style="width: 11%">开始时间</th>
                                <th style="width: 11%">结束时间</th>
                                <th style="width: 6%">工期变化</th>
                                <th style="width: 6%">累计工期</th>

                                <th style="width: 6%">额定工期</th>
                                <th style="width: 18%">变化原因</th>
                                <th style="width: 6%">责任人</th>
                                <th style="width: 6%">创建人</th>
                                <th style="width: 20%">备注</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr th:each="workHours : ${workHoursPage.content}">
                                <td th:text="${#strings.substring(workHours.createdTime, 0, 16)}"
                                    style="white-space: nowrap;">2025-01-01 12:00</td>
                                <td th:text="${workHours.startTime != null ? workHours.startTime : '-'}"
                                    style="white-space: nowrap;">-</td>
                                <td th:text="${workHours.endTime != null ? workHours.endTime : '-'}"
                                    style="white-space: nowrap;">-</td>
                                <td>
                                    <span
                                        th:class="${'badge ' + (workHours.daysChange >= 0 ? 'bg-success' : 'bg-danger')}"
                                        th:text="${(workHours.daysChange >= 0 ? '+' : '') + #numbers.formatDecimal(workHours.daysChange, 1, 1) + '天'}">+2.0天</span>
                                </td>

                                <td>
                                    <span class="fw-bold text-primary"
                                        th:text="${#numbers.formatDecimal(workHours.daysActual, 1, 1) + '天'}">8.0天</span>
                                </td>
                                <td
                                    th:text="${workHours.daysRated != null ? (#numbers.formatDecimal(workHours.daysRated, 1, 2) + ' 天') : '-'}">
                                    -</td>
                                <td th:text="${workHours.reason}" class="text-break">工期原因</td>
                                <td th:text="${workHours.responsiblePerson ?: '-'}">责任人</td>
                                <td th:text="${workHours.creator ?: '-'}">创建者</td>
                                <td th:text="${workHours.remark ?: '-'}" class="text-break">备注</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 工期记录分页 -->
                <div class="mt-3" th:if="${workHoursPage.totalPages > 1}">
                    <nav aria-label="工期记录分页">
                        <ul class="pagination pagination-sm justify-content-center">
                            <li class="page-item" th:classappend="${workHoursPage.first ? 'disabled' : ''}">
                                <a class="page-link"
                                    th:href="${workHoursPage.first ? '#' : '/projects/' + project.projectId + '?workHoursPage=' + (workHoursPage.number - 1)}"
                                    aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            <li class="page-item" th:each="i : ${#numbers.sequence(0, workHoursPage.totalPages - 1)}"
                                th:classappend="${i == workHoursPage.number ? 'active' : ''}">
                                <a class="page-link"
                                    th:href="'/projects/' + ${project.projectId} + '?workHoursPage=' + ${i}"
                                    th:text="${i + 1}">1</a>
                            </li>
                            <li class="page-item" th:classappend="${workHoursPage.last ? 'disabled' : ''}">
                                <a class="page-link"
                                    th:href="${workHoursPage.last ? '#' : '/projects/' + project.projectId + '?workHoursPage=' + (workHoursPage.number + 1)}"
                                    aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>

        <!-- 项目评论列表（用 SubTask 替代 ProjectComment） -->
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">项目评论列表 (<span th:text="${projectCommentPage.totalElements}">0</span>)</h5>
                <a th:if="${project.archive == null || project.archive != 1}"
                    th:href="@{/subtasks/new(taskId=${project.projectId}, businessType='项目')}"
                    class="btn btn-sm btn-primary">
                    <i class="bi bi-plus-circle"></i> 添加评论
                </a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th style="width: 6%">ID</th>
                                <th style="width: 40%">评论内容</th>
                                <th style="width: 12%">创建者</th>
                                <th style="width: 12%">状态</th>
                                <th style="width: 12%">创建时间</th>
                                <th style="width: 5%" th:if="${#authorization.expression('hasRole(''ADMIN'')')}">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr th:each="comment : ${projectCommentPage.content}">
                                <td th:text="${comment.subTaskId}">1</td>
                                <td>
                                    <div style="white-space: pre-wrap;" th:text="${comment.logContent}">评论内容</div>
                                </td>
                                <td th:text="${comment.createdBy ?: '-'}">创建者</td>
                                <td>
                                    <span th:switch="${comment.status}">
                                        <span th:case="'未开始'" class="badge bg-secondary">未开始</span>
                                        <span th:case="'进行中'" class="badge bg-primary">进行中</span>
                                        <span th:case="'已完成'" class="badge bg-success">已完成</span>
                                        <span th:case="*" class="badge bg-info" th:text="${comment.status}">其他状态</span>
                                    </span>
                                </td>
                                <td th:text="${comment.CreatedDate}">2023-01-01 12:00:00</td>
                                <!-- <td th:if="${#authorization.expression('hasRole(''ADMIN'')')}">
                                    <button 
                                    th:if="${project.archive == null || project.archive != 1}" 
                                    type="button" class="btn btn-sm btn-outline-danger" 
                                    th:attr="data-comment-id=${comment.subTaskId}, data-project-id=${project.projectId}" 
                                    data-bs-toggle="modal"  data-bs-target="#deleteProjectCommentModal">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </td> -->

                                <td th:if="${#authorization.expression('hasRole(''ADMIN'')')}">
                                    <button
                                        th:if="${project.archive == null || project.archive != 1}" 
                                        type="button" class="btn btn-sm btn-outline-danger"
                                        th:attr="data-comment-id=${comment.subTaskId}, data-project-id=${project.projectId}" 
                                        data-bs-toggle="modal" data-bs-target="#deleteSubTaskModal">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </td>

                            </tr>
                            <tr th:if="${projectCommentPage.totalElements == 0}">
                                <td th:colspan="${5 + (#authorization.expression('hasRole(''ADMIN'')') ? 1 : 0)}"
                                    class="text-center">暂无评论</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <!-- 评论分页 -->
                <div class="mt-3" th:if="${projectCommentPage.totalPages > 1}">
                    <nav aria-label="评论分页">
                        <ul class="pagination pagination-sm justify-content-center">
                            <li class="page-item" th:classappend="${projectCommentPage.first ? 'disabled' : ''}">
                                <a class="page-link"
                                    th:href="${projectCommentPage.first ? '#' : '/projects/' + project.projectId + '?projectCommentPage=' + (projectCommentPage.number - 1)}"
                                    aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            <li class="page-item"
                                th:each="i : ${#numbers.sequence(0, projectCommentPage.totalPages - 1)}"
                                th:classappend="${i == projectCommentPage.number ? 'active' : ''}">
                                <a class="page-link"
                                    th:href="'/projects/' + ${project.projectId} + '?projectCommentPage=' + ${i}"
                                    th:text="${i + 1}">1</a>
                            </li>
                            <li class="page-item" th:classappend="${projectCommentPage.last ? 'disabled' : ''}">
                                <a class="page-link"
                                    th:href="${projectCommentPage.last ? '#' : '/projects/' + project.projectId + '?projectCommentPage=' + (projectCommentPage.number + 1)}"
                                    aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>

        <!-- 删除子任务评论确认对话框 -->
        <div class="modal fade" id="deleteSubTaskModal" tabindex="-1" aria-labelledby="deleteSubTaskModalLabel"
            aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="deleteSubTaskModalLabel">确认删除</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        确定要删除这条评论吗？此操作不可恢复。
                    </div>
                    <div class="modal-footer">
                        <form th:action="@{/subtasks/delete}" method="post">
                            <input type="hidden" name="subTaskId" id="deleteSubTaskId">
                            <input type="hidden" name="taskId" id="taskIdForSubTask">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="submit" class="btn btn-danger">删除</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- 删除确认对话框 -->
        <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        确定要删除项目 <span id="projectName"></span> 吗？此操作不可撤销。
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <form id="deleteForm" th:action="@{/projects/delete}" method="post">
                            <input type="hidden" id="projectId" name="projectId">
                            <button type="submit" class="btn btn-danger">删除</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- 归档确认对话框 -->
        <div class="modal fade" id="archiveModal" tabindex="-1" aria-labelledby="archiveModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="archiveModalLabel">确认归档</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        确定要归档项目 <span class="project-name"></span> 吗？归档后的项目将不会在主列表中显示。
                        <p class="text-warning mt-2"><i class="bi bi-exclamation-triangle"></i>
                            注意：只有当项目下所有任务都标记为"已完成"状态时，才能归档项目。</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <form id="archiveForm" method="post">
                            <input type="hidden" class="project-id" name="projectId">
                            <button type="submit" class="btn btn-primary">归档</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- 取消归档确认对话框 -->
        <div class="modal fade" id="unarchiveModal" tabindex="-1" aria-labelledby="unarchiveModalLabel"
            aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="unarchiveModalLabel">确认取消归档</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        确定要取消归档项目 <strong id="unarchiveProjectName"></strong> 吗？
                    </div>
                    <div class="modal-footer">
                        <form th:action="@{/projects/{id}/unarchive(id=${project.projectId})}" method="post">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="submit" class="btn btn-info">确认取消归档</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- 提交确认对话框 -->
        <div class="modal fade" id="completeModal" tabindex="-1" aria-labelledby="completeModalLabel"
            aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="completeModalLabel">提交确认</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        确定要提交项目 <span class="fw-bold project-name"></span> 吗？提交后项目将被标记为已完成。
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <form th:action="@{/projects/{id}/complete(id=${project.projectId})}" method="post"
                            style="display: inline;">
                            <button type="submit" class="btn btn-success">确认提交</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- 二维码显示对话框 -->
        <div class="modal fade" id="qrCodeModal" tabindex="-1" aria-labelledby="qrCodeModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="qrCodeModalLabel">注册码二维码</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body text-center">
                        <div class="mb-3">
                            <p class="text-muted">注册码内容：</p>
                            <div class="alert alert-light" id="licenseCodeText" style="word-break: break-all;"></div>
                        </div>
                        <div id="qrCodeContainer">
                            <!-- 二维码图片将在这里显示 -->
                        </div>
                        <div class="mt-3">
                            <small class="text-muted">可扫描此二维码获取注册码</small>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 自定义脚本 -->
    <script id="customScript" th:inline="javascript">
        // 获取总比例数据
        var totalRatio = /*[[${totalRatio}]]*/ 0.0;

        // 页面加载完成后检查总比例
        document.addEventListener('DOMContentLoaded', function () {
            checkTotalRatio();
        });

        // 检查总比例是否大于1，如果是则显示弹窗提醒
        function checkTotalRatio() {
            if (totalRatio != null && totalRatio > 1.0) {
                // 创建并显示提醒弹窗
                var message = '⚠️ 项目任务比例异常\n\n' +
                    '当前项目的任务比例总数为: ' + totalRatio.toFixed(3) + '\n' +
                    '正常情况下，比例总数应该等于或小于 1.000\n\n' +
                    '建议检查并调整各任务的比例设置，确保资源分配合理。';

                // 使用浏览器原生弹窗
                alert(message);

                // 如果需要使用Bootstrap模态框，可以取消下面的注释
                /*
                // 创建动态模态框
                var modalHtml = `
                    <div class="modal fade" id="ratioWarningModal" tabindex="-1" aria-labelledby="ratioWarningModalLabel" aria-hidden="true">
                        <div class="modal-dialog modal-dialog-centered">
                            <div class="modal-content border-warning">
                                <div class="modal-header bg-warning text-dark">
                                    <h5 class="modal-title" id="ratioWarningModalLabel">
                                        <i class="bi bi-exclamation-triangle me-2"></i>项目任务比例异常
                                    </h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="alert alert-warning mb-3">
                                        <strong>当前项目的任务比例总数:</strong> ${totalRatio.toFixed(3)}
                                    </div>
                                    <p class="mb-2">正常情况下，比例总数应该等于或小于 <strong>1.000</strong></p>
                                    <p class="mb-0">建议检查并调整各任务的比例设置，确保资源分配合理。</p>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">我知道了</button>
                                    <button type="button" class="btn btn-warning" onclick="scrollToTaskList()">查看任务列表</button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                
                // 添加模态框到页面
                document.body.insertAdjacentHTML('beforeend', modalHtml);
                
                // 显示模态框
                var ratioWarningModal = new bootstrap.Modal(document.getElementById('ratioWarningModal'));
                ratioWarningModal.show();
                
                // 模态框关闭后移除DOM元素
                document.getElementById('ratioWarningModal').addEventListener('hidden.bs.modal', function () {
                    this.remove();
                });
                */
            }
        }

        // 滚动到任务列表的函数（如果使用Bootstrap模态框时会用到）
        function scrollToTaskList() {
            var taskListElement = document.querySelector('.card-header h5');
            if (taskListElement && taskListElement.textContent.includes('项目任务')) {
                taskListElement.scrollIntoView({ behavior: 'smooth' });
            }
            // 关闭模态框
            var modal = bootstrap.Modal.getInstance(document.getElementById('ratioWarningModal'));
            if (modal) {
                modal.hide();
            }
        }

        // 设置模态框数据的通用函数
        function setupModal(modal, projectId, projectName, actionUrl) {
            var $modal = $(modal);
            $modal.find('.project-id').val(projectId);
            $modal.find('.project-name').text(projectName);
            $modal.find('form').attr('action', actionUrl);
        }

        // 设置删除子任务评论对话框数据
        document.getElementById('deleteSubTaskModal').addEventListener('show.bs.modal', function (event) {
            const button = event.relatedTarget;
            const subTaskId = button.getAttribute('data-comment-id');
            const taskId = button.getAttribute('data-project-id');
            document.getElementById('deleteSubTaskId').value = subTaskId;
            document.getElementById('taskIdForSubTask').value = taskId;
        });


        // 删除项目的事件监听器
        document.getElementById('deleteModal').addEventListener('show.bs.modal', function (event) {
            var button = event.relatedTarget;
            var projectId = button.getAttribute('data-project-id');
            var projectName = button.getAttribute('data-project-name');

            document.getElementById('projectName').textContent = projectName;
            document.getElementById('projectId').value = projectId;
        });

        // 归档项目
        $('#archiveModal').on('show.bs.modal', function (event) {
            var button = $(event.relatedTarget);
            var projectId = button.data('project-id');
            var projectName = button.data('project-name');
            var actionUrl = '/projects/' + projectId + '/archive';

            setupModal(this, projectId, projectName, actionUrl);
        });

        // 取消归档项目
        $('#unarchiveModal').on('show.bs.modal', function (event) {
            var button = $(event.relatedTarget);
            var projectId = button.data('project-id');
            var projectName = button.data('project-name');
            var actionUrl = '/projects/' + projectId + '/unarchive';

            setupModal(this, projectId, projectName, actionUrl);
        });

        function handleBack(event) {
            const fromPage = new URLSearchParams(window.location.search).get('from');
            if (fromPage === 'dashboard') {
                window.location.href = '/dashboard';
            } else {
                window.history.back();
            }
        }

        function checkTasksBeforeArchive(button) {
            var projectId = button.getAttribute('data-project-id');
            var projectName = button.getAttribute('data-project-name');

            // 发起AJAX请求检查项目是否有未完成任务
            $.ajax({
                url: '/projects/' + projectId + '/check-uncompleted-tasks',
                type: 'GET',
                success: function (response) {
                    if (response.hasUncompletedTasks) {
                        // 如果有未完成任务，显示错误信息
                        alert('归档失败：项目下存在未完成的任务，不能归档。请先完成所有任务。');
                    } else {
                        // 如果所有任务都已完成，显示归档确认对话框
                        $('#archiveModal').modal('show');
                        // 设置归档模态框的数据
                        setupModal('#archiveModal', projectId, projectName, '/projects/' + projectId + '/archive');
                    }
                },
                error: function () {
                    alert('检查任务状态时发生错误，请稍后再试。');
                }
            });
        }

        // 取消归档模态框事件处理
        document.getElementById('unarchiveModal').addEventListener('show.bs.modal', function (event) {
            var button = event.relatedTarget;
            var projectName = button.getAttribute('data-project-name');
            document.getElementById('unarchiveProjectName').textContent = projectName;
        });        // 提交确认对话框处理
        document.getElementById('completeModal').addEventListener('show.bs.modal', function (event) {
            var button = event.relatedTarget;
            var projectName = button.getAttribute('data-project-name');
            this.querySelector('.project-name').textContent = projectName;
        });        // 计算各任务的额定工期
        function calculateFilterTasksRatedDuration(button) {
            const projectId = button.getAttribute('data-project-id');

            console.log('开始计算项目ID:', projectId); // 添加调试日志

            // 显示加载状态
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="bi bi-hourglass"></i> 计算中...';
            button.disabled = true;

            // 发起AJAX请求
            $.ajax({
                url: '/projects/' + projectId + '/calculate-rated-duration',
                type: 'POST',
                dataType: 'json',
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                beforeSend: function () {
                    console.log('发送请求到:', '/projects/' + projectId + '/calculate-rated-duration');
                },
                success: function (response) {
                    console.log('收到响应:', response);
                    if (response.success) {
                        // 显示成功消息并刷新页面
                        alert(response.message || '计算完成！');
                        location.reload();
                    } else {
                        alert('计算失败: ' + (response.message || '未知错误'));
                    }
                },
                error: function (xhr, status, error) {
                    console.error('计算额定工期失败:', error);
                    console.error('HTTP状态码:', xhr.status);
                    console.error('响应文本:', xhr.responseText);
                    alert('计算失败，请稍后再试。错误: ' + xhr.status + ' - ' + error);
                },
                complete: function () {
                    // 恢复按钮状态
                    button.innerHTML = originalText;
                    button.disabled = false;
                }
            });
        }

        // 删除电脑信息函数
        function deleteComputerInfo(button) {
            const computerInfoId = button.getAttribute('data-computer-info-id');
            const computerInfoName = button.getAttribute('data-computer-info-name');

            if (confirm('确定要删除 ' + computerInfoName + ' 吗？此操作不可撤销。')) {
                // 显示加载状态
                const originalText = button.innerHTML;
                button.innerHTML = '<i class="bi bi-hourglass-split"></i>';
                button.disabled = true;

                // 发起删除请求
                fetch('/computer-info/' + computerInfoId + '/delete', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    }
                })
                    .then(response => {
                        if (response.ok) {
                            // 删除成功，刷新页面
                            location.reload();
                        } else {
                            return response.text().then(text => {
                                throw new Error(text || '删除失败');
                            });
                        }
                    })
                    .catch(error => {
                        console.error('删除电脑信息错误:', error);
                        alert('删除失败：' + error.message);
                        // 恢复按钮状态
                        button.innerHTML = originalText;
                        button.disabled = false;
                    });
            }
        }

        // 生成二维码函数
        function generateQRCode(button) {
            const licenseCode = button.getAttribute('data-license-code');

            if (!licenseCode || licenseCode.trim() === '') {
                alert('注册码为空，无法生成二维码');
                return;
            }

            // 显示注册码内容
            document.getElementById('licenseCodeText').textContent = licenseCode;

            // 清空二维码容器
            const qrCodeContainer = document.getElementById('qrCodeContainer');
            qrCodeContainer.innerHTML = '<div class="text-center"><i class="bi bi-hourglass-split"></i> 生成中...</div>';

            // 显示模态框
            const qrCodeModal = new bootstrap.Modal(document.getElementById('qrCodeModal'));
            qrCodeModal.show();

            // 创建图片元素
            const img = document.createElement('img');
            img.style.maxWidth = '100%';
            img.style.height = 'auto';
            img.className = 'border rounded';

            // 设置图片地址
            const encodedLicenseCode = encodeURIComponent(licenseCode);
            img.src = '/projects/generate-qrcode?licenseCode=' + encodedLicenseCode;

            // 处理图片加载
            img.onload = function () {
                qrCodeContainer.innerHTML = '';
                qrCodeContainer.appendChild(img);
            };

            img.onerror = function () {
                qrCodeContainer.innerHTML = '<div class="alert alert-danger">二维码生成失败，请稍后重试</div>';
            };
        }
    </script>
</body>

</html>