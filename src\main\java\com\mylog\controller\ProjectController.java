package com.mylog.controller;

import java.io.File;
import java.io.FileOutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import com.mylog.model.Message;
import com.mylog.model.Project;
import com.mylog.model.ProjectTask;
import com.mylog.model.SubTask;
import com.mylog.model.UserActivityLog;
import com.mylog.model.user.User;
import com.mylog.service.MachineCodeService;
import com.mylog.service.MessageService;
import com.mylog.service.OptionsService;
import com.mylog.service.ProjectService;
import com.mylog.service.SubTaskService;
import com.mylog.service.TaskService;
import com.mylog.service.UserActivityLogService;
import com.mylog.util.DateTimeUtils;
import com.mylog.service.UserService;
import com.mylog.service.WorkHoursLogService;
import com.mylog.service.ComputerInfoService;
import com.mylog.service.QRCodeService;
import com.mylog.util.ProjectCodeGenerator;
import com.mylog.util.WeixinMessageUtil;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@Controller
@RequestMapping("/projects")
public class ProjectController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(ProjectController.class);

    @Autowired
    private ProjectService projectService;

    @Autowired
    private OptionsService optionsService;

    @Autowired
    private TaskService taskService;

    @Autowired
    private MessageService messageService;

    @Autowired
    private UserActivityLogService activityLogService;

    @Autowired
    private UserService userService;

    @Autowired
    private ProjectCodeGenerator projectCodeGenerator;

    @Autowired
    private SubTaskService subTaskService;

    @Autowired
    private WeixinMessageUtil weixinMessageUtil;

    @Autowired
    private WorkHoursLogService workHoursLogService;

    @Autowired
    private MachineCodeService machineCodeService;

    @Autowired
    private ComputerInfoService computerInfoService;
    
    @Autowired
    private QRCodeService qrCodeService;


    @GetMapping
    public String listProjects(
            @RequestParam(value = "page", defaultValue = "0") int page,
            @RequestParam(value = "size", defaultValue = "20") int size,
            @RequestParam(value = "archived", required = false) Boolean archived,
            Model model) {

        try {
            // 获取当前用户信息
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String currentUsername = authentication.getName();
            boolean isAdmin = authentication.getAuthorities().stream()
                    .anyMatch(a -> a.getAuthority().equals("ROLE_ADMIN"));
            boolean isManager = authentication.getAuthorities().stream()
                    .anyMatch(a -> a.getAuthority().equals("ROLE_MANAGER"));

            // 设置分页，按创建日期降序排序
            Pageable pageable = PageRequest.of(page, size, Sort.by("createdDate").descending());
            Page<Project> projectPage;

            // 根据用户角色获取项目列表
            if (isAdmin || isManager) {
                projectPage = projectService.findAllProjects(pageable);
            } else {
                projectPage = projectService.findProjectsByResponsible(currentUsername, pageable);
            }

            try {
                // 计算当前页总成本1
                BigDecimal totalCost1 = projectPage.getContent().stream()
                        .map(project -> {
                            try {
                                return project.getTotalCost1() != null ? project.getTotalCost1() : BigDecimal.ZERO;
                            } catch (Exception e) {
                                logger.error("计算项目总成本1时出错 [projectId={}]: {}", project.getProjectId(), e.getMessage());
                                return BigDecimal.ZERO;
                            }
                        })
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                model.addAttribute("totalCost1", totalCost1.setScale(2, RoundingMode.HALF_UP));

                // 计算所有项目的总成本1总和（全局总额）
                BigDecimal globalTotalCost1 = BigDecimal.ZERO;
                try {
                    globalTotalCost1 = projectService.calculateTotalCost1();
                    globalTotalCost1 = globalTotalCost1 != null ? globalTotalCost1 : BigDecimal.ZERO;
                } catch (Exception e) {
                    logger.error("计算全局总成本1总额时出错: {}", e.getMessage(), e);
                    globalTotalCost1 = BigDecimal.ZERO;
                }
                model.addAttribute("globalTotalCost1", globalTotalCost1);
                logger.info("全局总成本1总额: {}", globalTotalCost1);

                // 计算当前页总成本1占总成本的百分比
                BigDecimal costPercentage = BigDecimal.ZERO;
                if (globalTotalCost1.compareTo(BigDecimal.ZERO) > 0) {
                    try {
                        costPercentage = totalCost1.multiply(new BigDecimal("100"))
                                .divide(globalTotalCost1, 2, RoundingMode.HALF_UP);
                    } catch (Exception e) {
                        logger.error("计算成本百分比时出错: {}", e.getMessage());
                        costPercentage = BigDecimal.ZERO;
                    }
                }
                model.addAttribute("costPercentage", costPercentage);

            } catch (Exception e) {
                logger.error("计算总成本1时出错: {}", e.getMessage(), e);
                model.addAttribute("totalCost1", BigDecimal.ZERO.setScale(2));
                model.addAttribute("globalTotalCost1", BigDecimal.ZERO);
                model.addAttribute("costPercentage", BigDecimal.ZERO);
                model.addAttribute("totalCostError", "计算总成本1时发生错误");
            }

            // 获取进行中的项目数量
            Long inProgressCount = projectService.countProjectsByStatus("进行中");
            model.addAttribute("inProgressProjectCount", inProgressCount);

            if (projectPage != null) {
                // 为每个项目添加"进行中任务"信息和"任务额定工期"信息
                for (Project project : projectPage.getContent()) {
                    // 调用getter方法以初始化Transient字段
                    project.getPlannedStartDateTime();
                    project.getPlannedEndDateTime();

                    // 获取项目的"进行中"任务
                    List<ProjectTask> inProgressTasks = taskService.findTasksByProjectIdAndStatus(project.getProjectId(),
                            "进行中");
                    // 将任务名称连接成字符串，用逗号分隔
                    String inProgressTaskNames = inProgressTasks.stream()
                            .map(ProjectTask::getTaskName)
                            .collect(java.util.stream.Collectors.joining(", "));
                    // 将进行中任务名称字符串添加到项目对象的备注字段中作为临时存储
                    project.setNote(inProgressTaskNames);
                    
                    // 获取项目的"暂停中"任务
                    List<ProjectTask> pausedTasks = taskService.findTasksByProjectIdAndStatus(project.getProjectId(),
                            "已暂停");
                    // 将暂停中任务名称连接成字符串，用逗号分隔
                    String pausedTaskNames = pausedTasks.stream()
                            .map(ProjectTask::getTaskName)
                            .collect(java.util.stream.Collectors.joining(", "));
                    // 将暂停中任务名称字符串添加到项目对象的暂停中任务字段中作为临时存储
                    project.setPausedTaskNames(pausedTaskNames);
                    
                    // 计算项目的任务额定工期总数
                    Map<String, Double> taskDurationStats = calculateTaskDurationStatistics(project.getProjectId(), project);
                    Double totalRatedDuration = taskDurationStats.get("totalRatedDuration");
                    // 将任务额定工期保存到项目的 taskRatedDurationTotal 字段中作为临时存储
                    project.setTaskRatedDurationTotal(totalRatedDuration != null ? java.math.BigDecimal.valueOf(totalRatedDuration) : java.math.BigDecimal.ZERO);
                    
                    // 检查项目是否有电脑信息记录，设置编码信息
                    boolean hasComputerInfo = computerInfoService.hasComputerInfoByProjectId(project.getProjectId());
                    project.setHasComputerInfo(hasComputerInfo ? "有" : "无");
                }

                // 添加分页相关属性
                model.addAttribute("projectPage", projectPage);
                model.addAttribute("currentPage", projectPage.getNumber());
                model.addAttribute("page", projectPage); // 用于分页组件
                model.addAttribute("totalPages", projectPage.getTotalPages());
                model.addAttribute("totalElements", projectPage.getTotalElements());
                model.addAttribute("projects", projectPage.getContent());
            } else {
                // 处理 projectPage 为 null 的情况
                model.addAttribute("projectPage", null);
                model.addAttribute("currentPage", 0);
                model.addAttribute("totalPages", 0);
                model.addAttribute("totalElements", 0L);
                model.addAttribute("projects", Collections.emptyList());
            }

            // 添加其他属性
            model.addAttribute("size", size);
            model.addAttribute("archived", archived);
            model.addAttribute("activeMenu", "projects");

            // 添加人员列表到模型中，用于负责人下拉选择
            List<String> personnelList = optionsService.getPersonnel();
            model.addAttribute("personnel", personnelList);

            // 添加视觉类型列表到模型中，用于视觉类型下拉选择
            model.addAttribute("visionTypes", optionsService.getVisionTypes());

            return "projects/list";
        } catch (Exception e) {
            logger.error("获取项目列表时发生错误: {}", e.getMessage(), e);
            model.addAttribute("error", "获取项目列表时发生错误：" + e.getMessage());
            model.addAttribute("activeMenu", "projects"); // 确保导航菜单正确高亮
            return "error/500"; // 现在模板文件已经存在
        }
    }

    @GetMapping("/{id}")
    public String viewProject(
            @PathVariable("id") Long id,
            @RequestParam(value = "page", defaultValue = "0") int page,
            @RequestParam(value = "size", defaultValue = "20") int size,
            @RequestParam(value = "from", required = false) String from,
            Model model,
            RedirectAttributes redirectAttributes) {
        // 获取当前用户信息
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String currentUsername = authentication.getName();
        boolean isAdmin = authentication.getAuthorities().stream()
                .anyMatch(a -> a.getAuthority().equals("ROLE_ADMIN"));
        boolean isManager = authentication.getAuthorities().stream()
                .anyMatch(a -> a.getAuthority().equals("ROLE_MANAGER"));

        // 设置activeMenu
        model.addAttribute("activeMenu", "projects");

        Optional<Project> projectOpt = projectService.findProjectById(id);
        if (!projectOpt.isPresent()) {
            redirectAttributes.addFlashAttribute("error", "项目不存在");
            return from != null ? "redirect:/dashboard" : "redirect:/projects";
        }

        
        Project project = projectOpt.get();
        //boolean isProjectResponsible = currentUsername.equals(project.getResponsible());

        // 所有用户都可以查看项目详情，无需权限检查

        // 确保日期字段被初始化
        project.getPlannedStartDateTime();
        project.getPlannedEndDateTime();
        project.getActualStartDateTime();
        project.getActualEndDateTime();
        project.getCreatedDateTime();

        model.addAttribute("project", project);

        // 先获取项目所有任务并计算评论天数
        List<ProjectTask> allTasks = taskService.findTasksByProjectId(id);
        taskService.calculateCommentDays(allTasks);
        logger.info("已计算项目 {} 中所有 {} 个任务的评论天数", id, allTasks.size());

        // 添加项目关联的任务列表（分页版本），按任务名称排序
        Pageable pageable = PageRequest.of(page, Math.min(size, 20), Sort.by(Sort.Direction.ASC, "taskName"));
        Page<ProjectTask> taskPage = taskService.findTasksByProjectId(id, pageable);
        
        // 累计工期和剩余工期现在直接从数据库字段获取，无需手动计算
        logger.info("项目详情页面加载完成，累计工期和剩余工期将从数据库字段直接显示");
        
        model.addAttribute("taskPage", taskPage);
        
        // 计算项目中符合前缀条件的任务的工期统计信息
        Map<String, Double> taskDurationStats = calculateTaskDurationStatistics(id, project);
        model.addAttribute("totalRatedDuration", taskDurationStats.get("totalRatedDuration"));
        model.addAttribute("totalDuration", taskDurationStats.get("totalDuration"));
        model.addAttribute("totalBonus", taskDurationStats.get("totalBonus"));
        model.addAttribute("totalRatio", taskDurationStats.get("totalRatio"));
        

        // 将前缀列表添加到模型中，供模板使用
       List<String> highlightPrefixes=optionsService.getPrefixesByCategoryAndRatio("任务名称", project.getVisionType() != null && project.getVisionType().contains("自制") ? "ratio" : "ratio2");

        // 过滤掉比例为0或负数的前缀
        highlightPrefixes = highlightPrefixes.stream()
                .filter(prefix -> {
                    // 获取该前缀对应的总比例
                    // 从所有任务名称中找到第一个匹配该前缀的任务名称
                    List<String> taskNames = optionsService.getTaskNames();
                    String matchingTaskName = taskNames.stream()
                            .filter(name -> name != null && name.length() >= 2 && name.substring(0, 2).equals(prefix))
                            .findFirst()
                            .orElse(null);
                    
                    if (matchingTaskName != null) {
                        Double totalRatioForPrefix = optionsService.getTaskDurationRatio("任务名称", matchingTaskName, project.getVisionType());
                        return totalRatioForPrefix != null && totalRatioForPrefix > 0;
                    }
                    return false;
                })
                .collect(java.util.stream.Collectors.toList());


        model.addAttribute("highlightPrefixes", highlightPrefixes);

        // 添加工期记录查询（项目的工期记录）
        try {
            // 查询项目的工期记录，分页查询，按创建时间降序排序
            Pageable workHoursPageable = PageRequest.of(0, 10, Sort.by(Sort.Direction.DESC, "createdTime"));
            Page<com.mylog.model.WorkHoursLog> workHoursPage = workHoursLogService
                    .findWorkHoursLogsByBusinessTypeAndBusinessId(
                            "项目", id.intValue(), workHoursPageable);
            model.addAttribute("workHoursPage", workHoursPage);

            // 计算项目累计工期 (获取最新的累计工期)
            Double totalProjectHours = workHoursLogService.getLatestHoursInventory("项目", id.intValue());
            model.addAttribute("totalProjectHours", totalProjectHours != null ? totalProjectHours : 0.0);

            logger.info("项目 ID={} 的工期记录查询成功：共 {} 条记录，累计工期 {} 天",
                    id, workHoursPage.getTotalElements(), totalProjectHours);
        } catch (Exception e) {
            logger.error("查询项目工期记录时出错: {}", e.getMessage(), e);
            // 如果查询失败，设置默认值
            model.addAttribute("workHoursPage", Page.empty());
            model.addAttribute("totalProjectHours", 0.0);
        }

        // 添加电脑信息查询
        try {
            List<com.mylog.model.ComputerInfo> computerInfoList = computerInfoService.getComputerInfoByProjectId(id.intValue());
            model.addAttribute("computerInfoList", computerInfoList);
            logger.info("项目 ID={} 的电脑信息查询成功：共 {} 条记录", id, computerInfoList.size());
        } catch (Exception e) {
            logger.error("查询项目电脑信息时出错: {}", e.getMessage(), e);
            // 如果查询失败，设置空列表
            model.addAttribute("computerInfoList", Collections.emptyList());
        }

            // 查询项目评论分页数据（用 SubTask 代替 ProjectComment）
            try {
                Pageable commentPageable = PageRequest.of(0, 10, Sort.by(Sort.Direction.DESC, "CreatedDate"));
                List<SubTask>  subTasks = subTaskService.findSubTasksByProjectId(id);
                List<Long> taskIds = new ArrayList<>();
                for (SubTask t : subTasks) {
                    taskIds.add(t.getTaskId());
                }
                Page<com.mylog.model.SubTask> projectCommentPage = org.springframework.data.domain.Page.empty();
                if (!taskIds.isEmpty()) {
                    projectCommentPage = subTaskService.findSubTasksByTaskIds(taskIds, commentPageable);
                }
                model.addAttribute("projectCommentPage", projectCommentPage);
            } catch (Exception e) {
                logger.error("查询项目评论(SubTask)时出错: {}", e.getMessage(), e);
                model.addAttribute("projectCommentPage", org.springframework.data.domain.Page.empty());
            }

        return "projects/view";
    }

    @GetMapping("/new")
    @PreAuthorize("hasRole('ADMIN') or hasRole('MANAGER')")
    public String newProjectForm(Model model, HttpServletRequest request) {
        Project project = new Project();

        // 设置activeMenu
        model.addAttribute("activeMenu", "projects");

        // 获取当前日期的最大序号，如果为null则使用1作为起始序号
        String currentDate = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        Integer maxSequence = projectService.findMaxSequenceByDate(currentDate);
        int nextSequence = (maxSequence == null) ? 1 : maxSequence + 1;

        project.setProjectCode(projectCodeGenerator.generateProjectCode(nextSequence));
        project.setStatus("未开始");

        model.addAttribute("project", project);
        model.addAttribute("personnel", optionsService.getPersonnel());
        model.addAttribute("salesPersonnel", optionsService.getSalesPersonnel());
        model.addAttribute("mechanicalPersonnel", optionsService.getMechanicalPersonnel());
        model.addAttribute("electricalPersonnel", optionsService.getElectricalPersonnel());
        model.addAttribute("visionTypes", optionsService.getVisionTypes());
        model.addAttribute("projectTypes", optionsService.getProjectTypes());

        // 获取来源页面URL
        String referer = request.getHeader("Referer");
        model.addAttribute("referer", referer);

        return "projects/form";
    }

    @GetMapping("/{id}/edit")
    public String editProjectForm(@PathVariable Long id, Model model, RedirectAttributes redirectAttributes,
            HttpServletRequest request) {
        Optional<Project> projectOpt = projectService.findProjectById(id);
        if (!projectOpt.isPresent()) {
            redirectAttributes.addFlashAttribute("error", "项目不存在");
            return "redirect:/projects";
        }

        // 设置activeMenu
        model.addAttribute("activeMenu", "projects");

        Project project = projectOpt.get();

        // 检查权限
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        String currentUsername = auth.getName();
        boolean isAdmin = auth.getAuthorities().stream().anyMatch(a -> a.getAuthority().equals("ROLE_ADMIN"));
        boolean isManager = auth.getAuthorities().stream().anyMatch(a -> a.getAuthority().equals("ROLE_MANAGER"));
        boolean isProjectResponsible = project.getResponsible().equals(currentUsername);
        boolean isProjectCreator = currentUsername.equals(project.getCreatedBy());

        // 检查权限：管理员、负责的经理、项目创建者或邓利鹏用户可以编辑
        if (!isAdmin && !(isManager && isProjectResponsible) && !isProjectCreator && !currentUsername.equals("邓利鹏")) {
            redirectAttributes.addFlashAttribute("error", "您没有权限编辑此项目");
            return "redirect:/projects/" + id;
        }

        model.addAttribute("project", project);
        model.addAttribute("personnel", optionsService.getPersonnel());
        model.addAttribute("salesPersonnel", optionsService.getSalesPersonnel());
        model.addAttribute("mechanicalPersonnel", optionsService.getMechanicalPersonnel());
        model.addAttribute("electricalPersonnel", optionsService.getElectricalPersonnel());
        model.addAttribute("visionTypes", optionsService.getVisionTypes());
        model.addAttribute("projectTypes", optionsService.getProjectTypes());

        // 获取来源页面URL
        String referer = request.getHeader("Referer");
        model.addAttribute("referer", referer);

        return "projects/form";
    }

    @PostMapping("/save")
    public String saveProject(@ModelAttribute Project project,
            @RequestParam(value = "autoCreateTasks", required = false) Boolean autoCreateTasks,
            RedirectAttributes redirectAttributes) {
        try {
            logger.info("开始保存项目: ID={}, 名称={}", project.getProjectId(), project.getProjectName());

            // 获取当前用户信息
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            String currentUsername = auth.getName();
            boolean isAdmin = auth.getAuthorities().stream().anyMatch(a -> a.getAuthority().equals("ROLE_ADMIN"));
            boolean isManager = auth.getAuthorities().stream().anyMatch(a -> a.getAuthority().equals("ROLE_MANAGER"));
            logger.info("当前操作用户: {}", currentUsername);

            // 判断是否为新项目
            boolean isNewProject = project.getProjectId() == null;
            
            // 如果是更新项目，获取原项目信息以比较变化
            String oldStatus = null;
            String oldResponsible = null;
            BigDecimal oldRatedDurationDays = null;
            Project existingProject = null;
            
            if (!isNewProject) {
                Optional<Project> existingProjectOpt = projectService.findProjectById(project.getProjectId());
                if (existingProjectOpt.isPresent()) {
                    existingProject = existingProjectOpt.get();
                    oldStatus = existingProject.getStatus();
                    oldResponsible = existingProject.getResponsible();
                    oldRatedDurationDays = existingProject.getRatedDurationDays();
                    logger.info("获取到原项目信息: ID={}, 原状态={}, 原负责人={}, 原额定工期={}天", 
                        existingProject.getProjectId(), oldStatus, oldResponsible, oldRatedDurationDays);
                    
                    // 权限检查
                    boolean isProjectResponsible = existingProject.getResponsible().equals(currentUsername);
                    boolean isProjectCreator = currentUsername.equals(existingProject.getCreatedBy());

                    // 检查权限：管理员、负责的经理、项目创建者或邓利鹏用户可以编辑
                    if (!isAdmin && !(isManager && isProjectResponsible) && !isProjectCreator  && !currentUsername.equals("邓利鹏")) {
                        redirectAttributes.addFlashAttribute("error", "您没有权限编辑此项目");
                        return "redirect:/projects/" + project.getProjectId();
                    }
                }
            } else {
                // 新项目检查：只有管理员和经理可以创建项目
                if (!isAdmin && !isManager) {
                    redirectAttributes.addFlashAttribute("error", "您没有权限创建项目");
                    return "redirect:/projects";
                }
            }

            // 格式化日期
            if (project.getPlannedStartDate() != null && !project.getPlannedStartDate().trim().isEmpty()) {
                LocalDateTime startDate = LocalDateTime.parse(project.getPlannedStartDate().replace(" ", "T"));
                project.setPlannedStartDate(startDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            }
            if (project.getPlannedEndDate() != null && !project.getPlannedEndDate().trim().isEmpty()) {
                LocalDateTime endDate = LocalDateTime.parse(project.getPlannedEndDate().replace(" ", "T"));
                project.setPlannedEndDate(endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            }

            // 设置创建者
            if (isNewProject) {
                project.setCreatedBy(currentUsername);
                project.setCreatedDate(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                logger.info("新建项目，设置创建者: {}", currentUsername);
            }

            // 数据验证：责任人为"未分配"和状态为"进行中"不允许同时存在
            if ("未分配".equals(project.getResponsible()) && "进行中".equals(project.getStatus())) {
                redirectAttributes.addFlashAttribute("error", "责任人为\"未分配\"时，项目状态不能设置为\"进行中\"");
                if (isNewProject) {
                    return "redirect:/projects/new";
                } else {
                    return "redirect:/projects/" + project.getProjectId() + "/edit";
                }
            }

            // 自动计算工期（在保存前计算）
            calculateDuration(project);
            
            // 自动计算总成本1和总成本2
            calculateTotalCosts(project);

            // 保存项目
            Project savedProject = projectService.saveProject(project);
            logger.info("项目保存成功: ID={}", savedProject.getProjectId());



            // 检查责任人是否变化
            if (!isNewProject && oldResponsible != null) {
                logger.info("比较项目责任人: 原责任人='{}', 新责任人='{}'", oldResponsible, project.getResponsible());
                if (!oldResponsible.equals(project.getResponsible())) {
                    logger.info("检测到责任人变更，准备发送消息");
                    // 向新责任人发送消息
                    if (project.getResponsible() != null && !project.getResponsible().isEmpty()) {
                        logger.info("向新责任人 {} 发送分配消息", project.getResponsible());
                        sendResponsibleChangeMessage(project.getResponsible(), savedProject, true);
                    } else {
                        logger.warn("新责任人为空，不发送分配消息");
                    }

                    // 向旧责任人发送取消的消息
                    logger.info("向原责任人 {} 发送取消消息", oldResponsible);
                    sendResponsibleChangeMessage(oldResponsible, savedProject, false);
                } else {
                    logger.info("责任人未变更，不发送消息");
                }
            } else if (isNewProject && project.getResponsible() != null && !project.getResponsible().isEmpty()) {
                // 新项目且指定了责任人
                logger.info("新建项目，向责任人 {} 发送分配消息", project.getResponsible());
                sendResponsibleChangeMessage(project.getResponsible(), savedProject, true);
            } else {
                logger.info("不满足发送责任人变更消息的条件: isNewProject={}, oldResponsible={}, newResponsible={}",
                        isNewProject, oldResponsible, project.getResponsible());
            }

            // 如果是新建项目且选择了自动创建任务
            if (isNewProject && Boolean.TRUE.equals(autoCreateTasks)) {
                logger.info("开始自动创建项目任务");

                // 获取所有任务名称
                List<String> taskNames = optionsService.getTaskNames();
                // 使用OptionsService获取前缀列表
                List<String> prefixes = optionsService.getPrefixesByCategoryAndRatio("任务名称", savedProject.getVisionType() != null && savedProject.getVisionType().contains("自制") ? "ratio" : "ratio2");

                // 筛选出前2个字符包含指定前缀的任务名称
                List<String> selectedTaskNames = taskNames.stream()
                        .filter(name -> {
                            if (name != null && name.length() >= 2) {
                                String prefix = name.substring(0, 2);
                                return prefixes.contains(prefix);
                            }
                            return false;
                        })
                        .collect(java.util.stream.Collectors.toList());

                // 如果需要倒序排列
                java.util.Collections.reverse(selectedTaskNames);

                // 按前缀分组任务名称
                Map<String, List<String>> taskNamesByPrefix = selectedTaskNames.stream()
                        .collect(java.util.stream.Collectors.groupingBy(name -> name.substring(0, 2)));

                // 从当前项目获取额定工期天数
                Double projectRatedDurationDays = savedProject.getRatedDurationDays() != null ? 
                        savedProject.getRatedDurationDays().doubleValue() : 0.0;

                // 为每个前缀组的任务创建并保存
                for (Map.Entry<String, List<String>> entry : taskNamesByPrefix.entrySet()) {
                    String prefix = entry.getKey();
                    List<String> taskNamesWithSamePrefix = entry.getValue();
                    
                    logger.info("自动创建前缀 {} 的任务，共 {} 个任务", prefix, taskNamesWithSamePrefix.size());

                    // 获取该前缀对应的总比例（从第一个任务名称获取）
                    String firstTaskName = taskNamesWithSamePrefix.get(0);
                    Double totalRatioForPrefix = optionsService.getTaskDurationRatio("任务名称", firstTaskName, savedProject.getVisionType());

                    if (totalRatioForPrefix != null && totalRatioForPrefix >= 0) {
                        // 计算每个任务平均分配的比例
                        Double averageRatio = totalRatioForPrefix / taskNamesWithSamePrefix.size();

                        logger.info("前缀 {} 的总比例: {}, 平均每个任务比例: {}", 
                                   prefix, totalRatioForPrefix, averageRatio);

                        // 为该前缀下的每个任务分配平均比例
                        for (String taskName : taskNamesWithSamePrefix) {
                            ProjectTask task = new ProjectTask();
                            task.setProjectId(savedProject.getProjectId());
                            task.setTaskName(taskName);

                            // 使用平均分配的比例
                            Double durationDaysRated = projectRatedDurationDays * averageRatio;

                            task.setRatio(averageRatio);
                            task.setRatedDurationDays(BigDecimal.valueOf(durationDaysRated));

                            task.setResponsible("未分配");
                            task.setType("订单");
                            if (task.getTaskName().contains("责任人")) {
                                task.setType("分管");
                            }   
                            
                            task.setProgress(0);
                            task.setRisk("正常");
                            task.setStatus("未开始");
                            task.setCreatedBy(currentUsername);
                            task.setCreatedDate(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                            task.setRemarks(""); // 设置备注为空
                            task.setCommentDays(-1.0); // 设置评论天数为-1

                            // 保存任务并获取保存后的任务对象
                            ProjectTask savedTask = taskService.saveTask(task);
                            logger.info("自动创建任务成功: 项目ID={}, 任务名称={}, 平均比例={}, 额定工期={}天", 
                                       savedProject.getProjectId(), taskName, averageRatio, durationDaysRated);

                            // 为新任务添加一条系统评论
                            try {
                                SubTask commentTask = new SubTask();
                                commentTask.setTaskId(savedTask.getTaskId());
                                commentTask.setLogContent("本评论由系统在任务创建时自动添加");
                                commentTask.setCreatedDateTime(LocalDateTime.now());
                                commentTask.setCreatedBy(currentUsername != null ? currentUsername : "system");

                                // 保存评论
                                subTaskService.saveSubTask(commentTask);
                                logger.info("已为新任务添加首条评论");
                            } catch (Exception e) {
                                // 记录错误但不影响任务保存流程
                                logger.error("为新任务添加首条评论时出错: {}", e.getMessage(), e);
                            }
                        }
                    } else {
                        logger.warn("前缀 {} 的任务未找到对应的工期分配比例", prefix);
                        for (String taskName : taskNamesWithSamePrefix) {
                            logger.warn("任务 {} 未找到对应的工期分配比例", taskName);
                        }
                    }
                }
            }
            
            // 记录用户活动
            Optional<User> currentUser = userService.findUserByUsername(currentUsername);
            if (currentUser.isPresent()) {
                User user = currentUser.get();
                
                // 如果是更新操作且状态发生变化，记录状态变更日志
                if (oldStatus != null && !oldStatus.equals(project.getStatus())) {
                    logger.info("检测到项目状态变更：从 '{}' 变更为 '{}'", oldStatus, project.getStatus());
                    UserActivityLog statusLog = new UserActivityLog();
                    statusLog.setUserId(user.getUserId());
                    statusLog.setUsername(currentUsername);
                    statusLog.setActivityType(UserActivityLog.ActivityType.SETTINGS_CHANGE);
                    statusLog.setDescription(String.format("[状态变更] 将项目 [%s] (编号: %s) 的状态从 '%s' 修改为 '%s'",
                            project.getProjectName(),
                            project.getProjectCode(),
                            oldStatus,
                            project.getStatus()));
                    statusLog.setEntityType("Project");
                    statusLog.setEntityId(savedProject.getProjectId());
                    statusLog.setTimestamp(LocalDateTime.now());
                    
                    try {
                        logger.info("准备保存项目状态变更日志：{}", statusLog.getDescription());
                        UserActivityLog savedStatusLog = activityLogService.saveLog(statusLog);
                        logger.info("项目状态变更日志保存成功: ID={}", savedStatusLog.getId());
                    } catch (Exception e) {
                        logger.error("保存项目状态变更日志时出错: {}", e.getMessage(), e);
                    }

                    // 记录项目状态变更工期
                    try {
                        String responsiblePerson = project.getResponsible() != null ? project.getResponsible() : currentUsername;
                        String startTime = null;
                        String endTime = null;
                        if (project.getActualStartDate() != null && !project.getActualStartDate().isEmpty()) {
                            startTime = project.getActualStartDate();
                        }
                        if (project.getActualEndDate() != null && !project.getActualEndDate().isEmpty()) {
                            endTime = project.getActualEndDate();
                        }
                        workHoursLogService.logProjectStatusChange(
                            savedProject.getProjectId(),
                            oldStatus,
                            project.getStatus(),
                            savedProject.getDurationDays(),
                            savedProject.getRatedDurationDays(),
                            currentUsername,
                            responsiblePerson,
                            startTime,
                            endTime
                        );
                        logger.info("已记录项目 {} 的状态变更工期日志", savedProject.getProjectId());
                    } catch (Exception e) {
                        logger.error("记录项目状态变更工期日志时出错: {}", e.getMessage(), e);
                    }
                }
                
                // 如果是更新操作且额定工期发生变化，记录额定工期变更日志
                if (!isNewProject && hasRatedDurationChanged(oldRatedDurationDays, savedProject.getRatedDurationDays())) {
                    String oldRatedDaysStr = oldRatedDurationDays != null ? 
                        String.format("%.2f天", oldRatedDurationDays.doubleValue()) : "未设置";
                    String newRatedDaysStr = savedProject.getRatedDurationDays() != null ? 
                        String.format("%.2f天", savedProject.getRatedDurationDays().doubleValue()) : "未设置";
                    
                    UserActivityLog ratedDurationLog = new UserActivityLog();
                    ratedDurationLog.setUserId(user.getUserId());
                    ratedDurationLog.setUsername(currentUsername);
                    ratedDurationLog.setActivityType(UserActivityLog.ActivityType.SETTINGS_CHANGE);
                    ratedDurationLog.setDescription(String.format("将项目 [%s] (编号: %s) 的额定工期从 '%s' 修改为 '%s'",
                            project.getProjectName(),
                            project.getProjectCode(),
                            oldRatedDaysStr,
                            newRatedDaysStr));
                    ratedDurationLog.setEntityType("Project");
                    ratedDurationLog.setEntityId(savedProject.getProjectId());
                    ratedDurationLog.setTimestamp(LocalDateTime.now());
                    
                    try {
                        logger.info("准备保存项目额定工期变更日志：{}", ratedDurationLog.getDescription());
                        UserActivityLog savedRatedDurationLog = activityLogService.saveLog(ratedDurationLog);
                        logger.info("项目额定工期变更日志保存成功: ID={}", savedRatedDurationLog.getId());
                    } catch (Exception e) {
                        logger.error("保存项目额定工期变更日志时出错: {}", e.getMessage(), e);
                    }
                }
                
                // 记录常规的创建/更新日志
                UserActivityLog log = new UserActivityLog();
                log.setUserId(user.getUserId());
                log.setUsername(currentUsername);
                UserActivityLog.ActivityType activityType = isNewProject ? UserActivityLog.ActivityType.CREATE
                        : UserActivityLog.ActivityType.UPDATE;
                String description = isNewProject ? "创建了新项目: " + project.getProjectName()
                        : "更新了项目: " + project.getProjectName();

                logger.info("记录项目{}日志", isNewProject ? "创建" : "更新");
                log.setActivityType(activityType);
                log.setDescription(description);

                log.setEntityType("Project");
                log.setEntityId(savedProject.getProjectId());
                log.setTimestamp(LocalDateTime.now());

                // 保存日志
                try {
                    logger.info("准备保存用户活动日志：类型={}, 描述={}", log.getActivityType(), log.getDescription());
                    UserActivityLog savedLog = activityLogService.saveLog(log);
                    logger.info("用户活动日志保存成功: ID={}, 类型={}, 描述={}",
                            savedLog.getId(), savedLog.getActivityType(), savedLog.getDescription());
                } catch (Exception e) {
                    logger.error("保存用户活动日志时出错: {}", e.getMessage(), e);
                }
            } else {
                logger.warn("未找到当前用户信息: {}", currentUsername);
            }

            // 记录额定工期变化
            if (!isNewProject && oldRatedDurationDays != null && !oldRatedDurationDays.equals(project.getRatedDurationDays())) {
                try {
                    // 记录到工期日志表
                    String responsiblePerson = project.getResponsible() != null ? project.getResponsible() : currentUsername;
                    // 读取项目实际开始/结束时间
                    String startTime = null;
                    String endTime = null;
                    if (project.getActualStartDate() != null && !project.getActualStartDate().isEmpty()) {
                        startTime = project.getActualStartDate();
                    }
                    if (project.getActualEndDate() != null && !project.getActualEndDate().isEmpty()) {
                        endTime = project.getActualEndDate();
                    }
                    workHoursLogService.logProjectRatedDurationChange(
                        savedProject.getProjectId(),
                        oldRatedDurationDays,
                        project.getRatedDurationDays(),
                        currentUsername,
                        responsiblePerson,
                        startTime,
                        endTime
                    );
                    logger.info("已记录项目 {} 的额定工期变化日志", project.getProjectId());
                } catch (Exception e) {
                    logger.error("记录项目额定工期变化日志时出错: {}", e.getMessage(), e);
                }
            }

            redirectAttributes.addFlashAttribute("success", "项目保存成功");
            return "redirect:/projects/" + savedProject.getProjectId() + "?success=true";
        } catch (Exception e) {
            logger.error("保存项目时发生错误: {}", e.getMessage(), e);
            redirectAttributes.addFlashAttribute("error", "保存项目失败：" + e.getMessage());
            return "redirect:/projects";
        }
    }

    /**
     * 自动计算工期（工作日天数，排除周末和国家法定节假日）
     *
     * @param project 项目对象
     */
    private void calculateDuration(Project project) {
        try {
            // 调用DateTimeUtils中的通用方法计算并设置项目工期
            DateTimeUtils.calculateAndSetProjectDuration(project);
            
            // 记录日志
            if (project.getDurationDays() != null) {
                logger.info("自动计算项目工期: {} 天（工作日），实际工期: {} 天（工作日）", project.getDurationDays(), project.getActualDurationDays());
            } else {
                logger.debug("无法计算工期，开始日期或结束日期为空");
            }
        } catch (Exception e) {
            // 处理计算过程中可能出现的异常
            logger.error("计算工期时出错: {}", e.getMessage(), e);
            // 异常处理已在DateTimeUtils中完成
        }
    }

    /**
     * 自动计算总成本1和总成本2
     *
     * @param project 项目对象
     */
    private void calculateTotalCosts(Project project) {
        try {
            // 计算总成本1 = 单机成本1 * 设备数量
            if (project.getVisionCost() != null && project.getQuantity() != null) {
                BigDecimal quantity = new BigDecimal(project.getQuantity().toString());
                BigDecimal totalCost1 = project.getVisionCost().multiply(quantity);
                project.setTotalCost1(totalCost1);
                logger.info("计算总成本1: {} = {} * {}", totalCost1, project.getVisionCost(), project.getQuantity());
            } else {
                project.setTotalCost1(null);
                logger.debug("无法计算总成本1，单机成本1或设备数量为空");
            }

            // 计算总成本2 = 单机成本2 * 设备数量
            if (project.getVisionCost2() != null && project.getQuantity() != null) {
                BigDecimal quantity = new BigDecimal(project.getQuantity().toString());
                BigDecimal totalCost2 = project.getVisionCost2().multiply(quantity);
                project.setTotalCost2(totalCost2);
                logger.info("计算总成本2: {} = {} * {}", totalCost2, project.getVisionCost2(), project.getQuantity());
            } else {
                project.setTotalCost2(null);
                logger.debug("无法计算总成本2，单机成本2或设备数量为空");
            }
        } catch (Exception e) {
            logger.error("计算总成本时出错: {}", e.getMessage(), e);
            project.setTotalCost1(null);
            project.setTotalCost2(null);
        }
    }

    /**
     * 批量修复所有项目的总成本1和总成本2
     * 仅管理员可访问
     */
    @PostMapping("/fix-total-costs")
    @PreAuthorize("hasRole('ADMIN')")
    @ResponseBody
    public Map<String, Object> fixAllTotalCosts() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            logger.info("开始批量修复所有项目的总成本");
            
            // 获取所有项目
            List<Project> allProjects = projectService.findAllProjects();
            int totalProjects = allProjects.size();
            int fixedProjects = 0;
            
            for (Project project : allProjects) {
                // 记录修复前的值
                BigDecimal oldTotalCost1 = project.getTotalCost1();
                BigDecimal oldTotalCost2 = project.getTotalCost2();
                
                // 重新计算总成本
                calculateTotalCosts(project);
                
                // 检查是否有变化
                boolean changed = false;
                if (!java.util.Objects.equals(oldTotalCost1, project.getTotalCost1()) ||
                    !java.util.Objects.equals(oldTotalCost2, project.getTotalCost2())) {
                    changed = true;
                    fixedProjects++;
                }
                
                // 保存项目（即使没有变化也保存，确保数据一致性）
                projectService.saveProject(project);
                
                if (changed) {
                    logger.debug("项目 {} 总成本已修复: 总成本1 {} -> {}, 总成本2 {} -> {}", 
                        project.getProjectName(), oldTotalCost1, project.getTotalCost1(),
                        oldTotalCost2, project.getTotalCost2());
                }
            }
            
            // 记录活动日志
            Authentication auth = SecurityContextHolder.getContext().getAuthentication();
            if (auth != null) {
                String username = auth.getName();
                Optional<User> userOpt = userService.findUserByUsername(username);
                if (userOpt.isPresent()) {
                    User user = userOpt.get();
                    activityLogService.logSettingsChange(
                        user.getUserId(), 
                        username,
                        String.format("批量修复总成本: 修复了 %d/%d 个项目的总成本", fixedProjects, totalProjects),
                        null, // IP地址
                        "Projects",
                        null, // 实体ID
                        "桌面端" // 访问类型
                    );
                }
            }
            
            result.put("success", true);
            result.put("message", String.format("成功修复了 %d 个项目的总成本（共处理 %d 个项目）", fixedProjects, totalProjects));
            result.put("totalProjects", totalProjects);
            result.put("fixedProjects", fixedProjects);
            
            logger.info("批量修复总成本完成: 共处理 {} 个项目，修复 {} 个项目", totalProjects, fixedProjects);
            
        } catch (Exception e) {
            logger.error("批量修复总成本时出错: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "修复总成本时出错: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 获取机器码
     */
    @GetMapping("/machine-code")
    @ResponseBody
    public Map<String, Object> getMachineCode() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            logger.info("收到获取机器码请求");
            
            if (!machineCodeService.isDllAvailable()) {
                result.put("success", false);
                result.put("message", "机器码API服务不可用。请确保以下条件满足：\n" +
                    "1. C# API服务已启动（端口5000）\n" +
                    "2. ClassPs.dll文件存在于API项目目录\n" +
                    "3. 防火墙未阻止localhost:5000端口\n\n" +
                    "启动命令：cd c:\\mylog-web\\MachineCodeApi && dotnet run --urls \"http://localhost:5000\"");
                logger.warn("机器码API服务不可用");
                return result;
            }
            
            String machineCode = machineCodeService.getMachineCode();
            
            if (machineCode.startsWith("错误：")) {
                result.put("success", false);
                result.put("message", machineCode);
                logger.error("获取机器码失败: {}", machineCode);
            } else {
                result.put("success", true);
                result.put("machineCode", machineCode);
                result.put("message", "成功获取机器码");
                logger.info("成功获取机器码");
                
                // 记录活动日志
                Authentication auth = SecurityContextHolder.getContext().getAuthentication();
                if (auth != null) {
                    String username = auth.getName();
                    Optional<User> userOpt = userService.findUserByUsername(username);
                    if (userOpt.isPresent()) {
                        User user = userOpt.get();
                        activityLogService.logSettingsChange(
                            user.getUserId(), 
                            username,
                            "获取了机器码",
                            getClientIpAddress(),
                            "System",
                            null,
                            getAccessType()
                        );
                    }
                }
            }
            
        } catch (Exception e) {
            logger.error("获取机器码时发生异常: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "获取机器码时发生异常: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 计算注册码
     */
    @PostMapping("/calculate-license")
    @ResponseBody
    public Map<String, Object> calculateLicense(@RequestBody Map<String, String> request) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            String machineCode = request.get("machineCode");
            logger.info("收到计算注册码请求，机器码: {}", machineCode);
            
            if (machineCode == null || machineCode.trim().isEmpty()) {
                result.put("success", false);
                result.put("message", "机器码不能为空");
                return result;
            }
            
            if (!machineCodeService.isDllAvailable()) {
                result.put("success", false);
                result.put("message", "机器码API服务不可用。\n" +
                    "请管理员启动命令");
                logger.warn("机器码API服务不可用");
                return result;
            }
            
            String licenseCode = machineCodeService.calculateLicense(machineCode.trim());
            
            if (licenseCode.startsWith("错误：")) {
                result.put("success", false);
                result.put("licenseCode", "");
                result.put("message", licenseCode);
                logger.error("计算注册码失败: {}", licenseCode);
            } else {
                result.put("success", true);
                result.put("licenseCode", licenseCode);
                result.put("message", "成功计算注册码");
                logger.info("成功计算注册码");
                
                // 记录活动日志
                Authentication auth = SecurityContextHolder.getContext().getAuthentication();
                if (auth != null) {
                    String username = auth.getName();
                    Optional<User> userOpt = userService.findUserByUsername(username);
                    if (userOpt.isPresent()) {
                        User user = userOpt.get();
                        activityLogService.logSettingsChange(
                            user.getUserId(), 
                            username,
                            "计算了注册码，机器码: " + machineCode.substring(0, Math.min(machineCode.length(), 20)) + "...",
                            getClientIpAddress(),
                            "System",
                            null,
                            getAccessType()
                        );
                    }
                }
            }
            
        } catch (Exception e) {
            logger.error("计算注册码时发生异常: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("licenseCode", "");
            result.put("message", "计算注册码时发生异常: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 生成注册码的二维码图片
     */
    @GetMapping("/generate-qrcode")
    @ResponseBody
    public void generateQRCode(@RequestParam("licenseCode") String licenseCode, HttpServletResponse response) {
        try {
            if (licenseCode == null || licenseCode.trim().isEmpty()) {
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                response.getWriter().write("注册码不能为空");
                return;
            }
            
            // 生成二维码图片
            byte[] qrCodeImage = qrCodeService.generateQRCodeImage(licenseCode.trim(), 300, 300);
            
            // 设置响应头
            response.setContentType("image/png");
            response.setContentLength(qrCodeImage.length);
            response.setHeader("Cache-Control", "no-cache");
            
            // 输出图片
            response.getOutputStream().write(qrCodeImage);
            response.getOutputStream().flush();
            
            logger.info("成功生成注册码二维码，注册码: {}", licenseCode.substring(0, Math.min(licenseCode.length(), 20)) + "...");
            
        } catch (Exception e) {
            logger.error("生成二维码时发生异常: {}", e.getMessage(), e);
            try {
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.getWriter().write("生成二维码失败: " + e.getMessage());
            } catch (Exception ex) {
                logger.error("写入错误响应时发生异常: {}", ex.getMessage(), ex);
            }
        }
    }

    /**
     * 发送责任人变更消息
     *
     * @param receiver   消息接收者
     * @param project    项目对象
     * @param isAssigned 是否分配责任人
     */
    private void sendResponsibleChangeMessage(String receiver, Project project, boolean isAssigned) {
        logger.info("准备发送责任人变更消息: 接收者={}, 项目ID={}, 项目名称={}, 是否分配={}",
                receiver, project.getProjectId(), project.getProjectName(), isAssigned);

        if (receiver == null || receiver.isEmpty()) {
            logger.warn("接收者为空，不发送消息");
            return;
        }

        Message message = new Message();
        message.setReceiver(receiver);

        // 获取当前登录用户
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String currentUsername = authentication.getName();
        logger.info("当前操作用户: {}", currentUsername);

        // 设置消息标题和内容，无论接收者是谁
        if (isAssigned) {
            message.setMessageTitle("项目责任人分配");
            String projectLink = String.format("<a href=\"/projects/%d\" target=\"_blank\">%s</a>", 
                project.getProjectId(), project.getProjectName());
            message.setMessageContent("您被" + currentUsername + "分配为项目 " + projectLink + " (编号: "
                    + project.getProjectCode() + ") 的负责人。");
            logger.info("生成分配消息: {}", message.getMessageContent());

            // 如果接收者不是"未分配"且不是当前用户，发送企业微信消息
            if (!receiver.contains("未分配") && !receiver.contains(currentUsername)) {
                // 使用StringBuilder构建内容，更高效
                StringBuilder contentBuilder = new StringBuilder();

                // 查找接收者的weixinID
                String weixinID = "";
                Optional<User> receiverUser = userService.findUserByUsername(receiver);
                if (receiverUser.isPresent() && receiverUser.get().getWeixinID() != null
                        && !receiverUser.get().getWeixinID().isEmpty()) {
                    weixinID = receiverUser.get().getWeixinID();
                    logger.debug("找到用户 {} 的微信ID: {}", receiver, weixinID);
                } else {
                    logger.warn("未找到用户 {} 的微信ID，使用用户名代替", receiver);
                    weixinID = receiver; // 如果没有weixinID，则使用用户名
                }

                // 添加到Markdown表格中
                contentBuilder.append(" <@").append(weixinID)
                        .append(">，你被" + currentUsername + "分配为项目 [" + project.getProjectName()
                                + "](https://prj.cpolar.cn/projects/" + project.getProjectId() + ") (编号: "
                                + project.getProjectCode() + ") 的负责人。");

                String content = contentBuilder.toString();

                List<String> mentionlist = new ArrayList<>();
                mentionlist.add(receiver);
                weixinMessageUtil.sendWeixinMessage("项目分配", content, mentionlist);
            }
        } else {
            message.setMessageTitle("项目责任人取消");
            String projectLink = String.format("<a href=\"/projects/%d\" target=\"_blank\">%s</a>", 
                project.getProjectId(), project.getProjectName());
            message.setMessageContent("您被" + currentUsername + "取消为项目 " + projectLink + " (编号: "
                    + project.getProjectCode() + ") 的负责人。");
            logger.info("生成取消消息: {}", message.getMessageContent());
        }

        message.setRelatedType("Project");
        message.setRelatedId(project.getProjectId());
        message.setRead(false);

        // 设置创建日期
        LocalDateTime now = LocalDateTime.now();
        message.setCreatedDate(now);

        try {
            messageService.saveMessage(message);
            logger.info("责任人变更消息发送成功: 接收者={}, 项目={}", receiver, project.getProjectName());
        } catch (Exception e) {
            logger.error("发送责任人变更消息失败: {}", e.getMessage(), e);
        }
    }

    @PostMapping("/delete")
    public String deleteProject(@RequestParam("projectId") Long id, RedirectAttributes redirectAttributes) {
        // 获取当前用户信息
        Authentication auth = SecurityContextHolder.getContext().getAuthentication();
        String currentUsername = auth.getName();
        boolean isAdmin = auth.getAuthorities().stream()
                .anyMatch(a -> a.getAuthority().equals("ROLE_ADMIN"));
        boolean isManager = auth.getAuthorities().stream()
                .anyMatch(a -> a.getAuthority().equals("ROLE_MANAGER"));

        // 获取项目信息
        Optional<Project> projectOpt = projectService.findProjectById(id);
        if (!projectOpt.isPresent()) {
            redirectAttributes.addFlashAttribute("error", "项目不存在");
            return "redirect:/projects";
        }

        Project project = projectOpt.get();
        boolean isProjectResponsible = currentUsername.equals(project.getResponsible());
        boolean isProjectCreator = currentUsername.equals(project.getCreatedBy());

        // 检查权限：
        // 1. 管理员可以删除任何项目
        // 2. 经理可以删除自己负责的项目
        // 3. 项目创建者可以删除自己创建的项目

        if (!isAdmin && !(isManager && isProjectResponsible) && !isProjectCreator ) {
            redirectAttributes.addFlashAttribute("error", "您没有权限删除此项目");
            return "redirect:/projects/" + id;
        }

        try {
            // 删除项目
            projectService.deleteProject(id);

            // 记录用户活动
            Optional<User> currentUser = userService.findUserByUsername(currentUsername);
            if (currentUser.isPresent()) {
                User user = currentUser.get();
                String description = "删除项目：" + project.getProjectName();
                activityLogService.logDelete(
                        user.getUserId(),
                        currentUsername,
                        description,
                        getClientIpAddress(),
                        "Project",
                        id,
                        getAccessType());
            }

            redirectAttributes.addFlashAttribute("success", "项目已成功删除");
        } catch (Exception e) {
            logger.error("删除项目时发生错误", e);
            redirectAttributes.addFlashAttribute("error", "删除项目时发生错误：" + e.getMessage());
        }

        return "redirect:/projects";
    }

    /**
     * 将普通搜索重定向到高级搜索
     */
    @GetMapping("/search")
    public String redirectToAdvancedSearch(
            @RequestParam(required = false) String keyword,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {

        // 检查当前用户是否具有管理员或经理权限
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        boolean isAdmin = authentication.getAuthorities().stream()
                .anyMatch(a -> a.getAuthority().equals("ROLE_ADMIN"));
        boolean isManager = authentication.getAuthorities().stream()
                .anyMatch(a -> a.getAuthority().equals("ROLE_MANAGER"));

        // 如果既不是管理员也不是经理，则重定向到首页
        if (!isAdmin && !isManager) {
            return "redirect:/dashboard";
        }

        // 如果有关键词，则重定向到高级搜索并将关键词作为项目名称的搜索条件
        if (keyword != null && !keyword.trim().isEmpty()) {
            return "redirect:/projects/advanced-search?fieldNames=projectName&field_projectName=" + keyword + "&page="
                    + page + "&size=" + size;
        }

        // 如果没有关键词，则重定向到项目列表
        return "redirect:/projects?page=" + page + "&size=" + size;
    }

    /**
     * 高级搜索项目
     *
     * @param fieldNames 字段名称列表
     * @param request    HTTP请求对象，用于获取动态参数
     * @param page       页码
     * @param size       每页大小
     * @param model      模型
     * @return 项目列表页面
     */
    @GetMapping("/advanced-search")
    public String advancedSearchProjects(
            @RequestParam(required = false) List<String> fieldNames,
            HttpServletRequest request,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            Model model) {

        // 检查当前用户是否具有管理员或经理权限
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        boolean isAdmin = authentication.getAuthorities().stream()
                .anyMatch(a -> a.getAuthority().equals("ROLE_ADMIN"));
        boolean isManager = authentication.getAuthorities().stream()
                .anyMatch(a -> a.getAuthority().equals("ROLE_MANAGER"));

        // 如果既不是管理员也不是经理，则重定向到首页
        if (!isAdmin && !isManager) {
            return "redirect:/dashboard";
        }

        // 添加详细日志
        logger.info("接收到高级搜索请求 - 请求URL: {}", request.getRequestURL());
        logger.info("接收到高级搜索请求 - 来源页面: {}", request.getHeader("Referer"));
        logger.info("接收到高级搜索请求 - 字段列表: {}", fieldNames);

        // 如果没有指定字段，则返回所有项目
        if (fieldNames == null || fieldNames.isEmpty()) {
            return listProjects(page, size, null, model);
        }

        // 创建查询条件映射
        Map<String, String> searchCriteria = new HashMap<>();
        for (String fieldName : fieldNames) {
            // 处理特殊的时间范围条件
            if (fieldName.equals("createdDate")) {
                // 获取日期值
                String startDate = request.getParameter("field_createdDate_start");
                String endDate = request.getParameter("field_createdDate_end");

                logger.info("处理创建时间条件 - 开始日期: {}, 结束日期: {}", startDate, endDate);

                // 处理开始日期
                if (startDate != null && !startDate.trim().isEmpty()) {
                    // 确保日期格式正确
                    String formattedDate = startDate.trim() + " 00:00:00";
                    searchCriteria.put("createdDate_start", formattedDate);
                    logger.info("添加开始日期条件: {}", formattedDate);
                }

                // 处理结束日期
                if (endDate != null && !endDate.trim().isEmpty()) {
                    // 确保日期格式正确
                    String formattedDate = endDate.trim() + " 23:59:59";
                    searchCriteria.put("createdDate_end", formattedDate);
                    logger.info("添加结束日期条件: {}", formattedDate);
                }
            } else if (fieldName.equals("actualEndDate")) {
                // 处理实际结束日期范围条件
                String startDate = request.getParameter("field_actualEndDate_start");
                String endDate = request.getParameter("field_actualEndDate_end");

                logger.info("处理实际结束日期条件 - 开始日期: {}, 结束日期: {}", startDate, endDate);

                // 处理开始日期
                if (startDate != null && !startDate.trim().isEmpty()) {
                    // 确保日期格式正确
                    String formattedDate = startDate.trim() + " 00:00:00";
                    searchCriteria.put("actualEndDate_start", formattedDate);
                    logger.info("添加实际结束开始日期条件: {}", formattedDate);
                }

                // 处理结束日期
                if (endDate != null && !endDate.trim().isEmpty()) {
                    // 确保日期格式正确
                    String formattedDate = endDate.trim() + " 23:59:59";
                    searchCriteria.put("actualEndDate_end", formattedDate);
                    logger.info("添加实际结束结束日期条件: {}", formattedDate);
                }
            } else if (fieldName.equals("totalCost1")) {
                // 处理总成本1范围条件
                String minCost = request.getParameter("field_totalCost1_min");
                String maxCost = request.getParameter("field_totalCost1_max");

                logger.info("处理总成本1条件 - 最小值: {}, 最大值: {}", minCost, maxCost);

                // 处理最小值
                if (minCost != null && !minCost.trim().isEmpty()) {
                    searchCriteria.put("totalCost1_min", minCost.trim());
                    logger.info("添加总成本1最小值条件: {}", minCost.trim());
                }

                // 处理最大值
                if (maxCost != null && !maxCost.trim().isEmpty()) {
                    searchCriteria.put("totalCost1_max", maxCost.trim());
                    logger.info("添加总成本1最大值条件: {}", maxCost.trim());
                }
            } else if (fieldName.equals("totalCost2")) {
                // 处理总成本2范围条件
                String minCost = request.getParameter("field_totalCost2_min");
                String maxCost = request.getParameter("field_totalCost2_max");

                logger.info("处理总成本2条件 - 最小值: {}, 最大值: {}", minCost, maxCost);

                // 处理最小值
                if (minCost != null && !minCost.trim().isEmpty()) {
                    searchCriteria.put("totalCost2_min", minCost.trim());
                    logger.info("添加总成本2最小值条件: {}", minCost.trim());
                }

                // 处理最大值
                if (maxCost != null && !maxCost.trim().isEmpty()) {
                    searchCriteria.put("totalCost2_max", maxCost.trim());
                    logger.info("添加总成本2最大值条件: {}", maxCost.trim());
                }
            } else if (fieldName.equals("visionCost2")) {
                // 处理单机成本2范围条件（保留兼容性）
                String minCost = request.getParameter("field_visionCost2_min");
                String maxCost = request.getParameter("field_visionCost2_max");

                logger.info("处理单机成本2条件 - 最小值: {}, 最大值: {}", minCost, maxCost);

                // 处理最小值
                if (minCost != null && !minCost.trim().isEmpty()) {
                    searchCriteria.put("visionCost2_min", minCost.trim());
                    logger.info("添加单机成本2最小值条件: {}", minCost.trim());
                }

                // 处理最大值
                if (maxCost != null && !maxCost.trim().isEmpty()) {
                    searchCriteria.put("visionCost2_max", maxCost.trim());
                    logger.info("添加单机成本2最大值条件: {}", maxCost.trim());
                }
            } else if (fieldName.equals("ratedDurationDays")) {
                // 处理额定工期范围条件
                String minHours = request.getParameter("field_ratedDurationDays_min");
                String maxHours = request.getParameter("field_ratedDurationDays_max");

                logger.info("处理额定工期条件 - 最小值: {}, 最大值: {}", minHours, maxHours);

                // 处理最小值
                if (minHours != null && !minHours.trim().isEmpty()) {
                    searchCriteria.put("ratedDurationDays_min", minHours.trim());
                    logger.info("添加额定工期最小值条件: {}", minHours.trim());
                }

                // 处理最大值
                if (maxHours != null && !maxHours.trim().isEmpty()) {
                    searchCriteria.put("ratedDurationDays_max", maxHours.trim());
                    logger.info("添加额定工期最大值条件: {}", maxHours.trim());
                }
            } else if (fieldName.equals("difficultyCoefficient")) {
                // 处理难度系数范围条件
                String minCoeff = request.getParameter("field_difficultyCoefficient_min");
                String maxCoeff = request.getParameter("field_difficultyCoefficient_max");

                logger.info("处理难度系数条件 - 最小值: {}, 最大值: {}", minCoeff, maxCoeff);

                // 处理最小值
                if (minCoeff != null && !minCoeff.trim().isEmpty()) {
                    searchCriteria.put("difficultyCoefficient_min", minCoeff.trim());
                    logger.info("添加难度系数最小值条件: {}", minCoeff.trim());
                }

                // 处理最大值
                if (maxCoeff != null && !maxCoeff.trim().isEmpty()) {
                    searchCriteria.put("difficultyCoefficient_max", maxCoeff.trim());
                    logger.info("添加难度系数最大值条件: {}", maxCoeff.trim());
                }
            } else if (fieldName.equals("cameraQuantity")) {
                // 处理相机数量范围条件
                String minCamera = request.getParameter("field_cameraQuantity_min");
                String maxCamera = request.getParameter("field_cameraQuantity_max");

                logger.info("处理相机数量条件 - 最小值: {}, 最大值: {}", minCamera, maxCamera);

                // 处理最小值
                if (minCamera != null && !minCamera.trim().isEmpty()) {
                    searchCriteria.put("cameraQuantity_min", minCamera.trim());
                    logger.info("添加相机数量最小值条件: {}", minCamera.trim());
                }

                // 处理最大值
                if (maxCamera != null && !maxCamera.trim().isEmpty()) {
                    searchCriteria.put("cameraQuantity_max", maxCamera.trim());
                    logger.info("添加相机数量最大值条件: {}", maxCamera.trim());
                }
            } else if (fieldName.equals("quantity")) {
                // 处理设备数量范围条件
                String minQuantity = request.getParameter("field_quantity_min");
                String maxQuantity = request.getParameter("field_quantity_max");

                logger.info("处理设备数量条件 - 最小值: {}, 最大值: {}", minQuantity, maxQuantity);

                // 处理最小值
                if (minQuantity != null && !minQuantity.trim().isEmpty()) {
                    searchCriteria.put("quantity_min", minQuantity.trim());
                    logger.info("添加设备数量最小值条件: {}", minQuantity.trim());
                }

                // 处理最大值
                if (maxQuantity != null && !maxQuantity.trim().isEmpty()) {
                    searchCriteria.put("quantity_max", maxQuantity.trim());
                    logger.info("添加设备数量最大值条件: {}", maxQuantity.trim());
                }
            } else {
                // 处理普通字段
                String value = request.getParameter("field_" + fieldName);
                
                // 对于进行中任务名称字段，空值有特殊含义，需要特殊处理
                if ("inProgressTaskName".equals(fieldName)) {
                    if (value != null) {
                        searchCriteria.put(fieldName, value.trim());
                        logger.info("添加进行中任务名称条件: {} = '{}'", fieldName, value.trim());
                    }
                } else if ("pausedTaskName".equals(fieldName)) {
                    // 对于暂停中任务名称字段，空值有特殊含义，需要特殊处理
                    if (value != null) {
                        searchCriteria.put(fieldName, value.trim());
                        logger.info("添加暂停中任务名称条件: {} = '{}'", fieldName, value.trim());
                    }
                } else {
                    // 其他字段的正常处理逻辑
                    if (value != null && !value.trim().isEmpty()) {
                        searchCriteria.put(fieldName, value.trim());
                        logger.info("添加普通条件: {} = {}", fieldName, value.trim());
                    }
                }
            }
        }

        // 如果没有有效的查询条件，则返回所有项目
        if (searchCriteria.isEmpty()) {
            logger.info("没有有效的搜索条件，返回所有项目");
            return listProjects(page, size, null, model);
        }

        logger.info("执行动态搜索，条件数量: {}", searchCriteria.size());
        Pageable pageable = PageRequest.of(page, size);
        Page<Project> projectPage = projectService.dynamicSearchProjects(searchCriteria, pageable);
        logger.info("搜索结果: 总数 = {}, 当前页项目数 = {}",
                projectPage.getTotalElements(),
                projectPage.getContent().size());

        // 获取进行中项目数量（总数）
        Long inProgressCount = projectService.countProjectsByStatus("进行中");
        model.addAttribute("inProgressProjectCount", inProgressCount);
        
        // 为每个项目添加"进行中任务"信息和"任务额定工期"信息
        for (Project project : projectPage.getContent()) {
            // 调用getter方法以初始化Transient字段
            project.getPlannedStartDateTime();
            project.getPlannedEndDateTime();

            // 获取项目的"进行中"任务
            List<ProjectTask> inProgressTasks = taskService.findTasksByProjectIdAndStatus(project.getProjectId(),
                    "进行中");
            // 将任务名称连接成字符串，用逗号分隔
            String inProgressTaskNames = inProgressTasks.stream()
                    .map(ProjectTask::getTaskName)
                    .collect(java.util.stream.Collectors.joining(", "));
            // 将进行中任务名称字符串添加到项目对象的备注字段中作为临时存储
            project.setNote(inProgressTaskNames);
            
            // 获取项目的"暂停中"任务
            List<ProjectTask> pausedTasks = taskService.findTasksByProjectIdAndStatus(project.getProjectId(),
                    "已暂停");
            // 将暂停中任务名称连接成字符串，用逗号分隔
            String pausedTaskNames = pausedTasks.stream()
                    .map(ProjectTask::getTaskName)
                    .collect(java.util.stream.Collectors.joining(", "));
            // 将暂停中任务名称字符串添加到项目对象的暂停中任务字段中作为临时存储
            project.setPausedTaskNames(pausedTaskNames);
            
            // 计算项目的任务额定工期总数
            Map<String, Double> taskDurationStats = calculateTaskDurationStatistics(project.getProjectId(), project);
            Double totalRatedDuration = taskDurationStats.get("totalRatedDuration");
            // 将任务额定工期保存到项目的 taskRatedDurationTotal 字段中作为临时存储
            project.setTaskRatedDurationTotal(totalRatedDuration != null ? java.math.BigDecimal.valueOf(totalRatedDuration) : java.math.BigDecimal.ZERO);
            
            // 检查项目是否有电脑信息记录，设置编码信息
            boolean hasComputerInfo = computerInfoService.hasComputerInfoByProjectId(project.getProjectId());
            project.setHasComputerInfo(hasComputerInfo ? "有" : "无");
        }        // 计算当前页面所有项目的总成本1总和
        BigDecimal totalCost1 = projectPage.getContent().stream()
                .filter(project -> project.getTotalCost1() != null)
                .map(Project::getTotalCost1)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        model.addAttribute("totalCost1", totalCost1);

        try {
            // 计算所有符合搜索条件的项目总成本1总和
            BigDecimal totalSearchCost1 = BigDecimal.ZERO;
            try {
                totalSearchCost1 = projectService.calculateTotalCost1BySearchCriteria(searchCriteria);
                totalSearchCost1 = totalSearchCost1 != null ? totalSearchCost1 : BigDecimal.ZERO;
            } catch (Exception e) {
                logger.error("计算搜索结果总成本1总额时出错: {}", e.getMessage(), e);
                totalSearchCost1 = BigDecimal.ZERO;
            }
            model.addAttribute("totalSearchCost1", totalSearchCost1);
            logger.info("搜索结果总成本1总额: {}", totalSearchCost1);

            // 计算所有项目的总成本1总和
            BigDecimal globalTotalCost1 = BigDecimal.ZERO;
            try {
                globalTotalCost1 = projectService.calculateTotalCost1();
                globalTotalCost1 = globalTotalCost1 != null ? globalTotalCost1 : BigDecimal.ZERO;
            } catch (Exception e) {
                logger.error("计算全局总成本1总额时出错: {}", e.getMessage(), e);
                globalTotalCost1 = BigDecimal.ZERO;
            }
            model.addAttribute("globalTotalCost1", globalTotalCost1);
            logger.info("全局总成本1总额: {}", globalTotalCost1);

            // 计算当前页总成本1占总成本的百分比
            BigDecimal costPercentage = BigDecimal.ZERO;
            if (globalTotalCost1.compareTo(BigDecimal.ZERO) > 0) {
                try {
                    costPercentage = totalCost1.multiply(new BigDecimal("100"))
                            .divide(globalTotalCost1, 2, RoundingMode.HALF_UP);
                } catch (Exception e) {
                    logger.error("计算成本百分比时出错: {}", e.getMessage());
                    costPercentage = BigDecimal.ZERO;
                }
            }
            model.addAttribute("costPercentage", costPercentage);

            // 计算搜索结果总成本1占总成本的百分比
            BigDecimal searchCostPercentage = BigDecimal.ZERO;
            if (globalTotalCost1.compareTo(BigDecimal.ZERO) > 0) {
                try {
                    searchCostPercentage = totalSearchCost1.multiply(new BigDecimal("100"))
                            .divide(globalTotalCost1, 2, RoundingMode.HALF_UP);
                } catch (Exception e) {
                    logger.error("计算搜索结果成本百分比时出错: {}", e.getMessage());
                    searchCostPercentage = BigDecimal.ZERO;
                }
            }
            model.addAttribute("searchCostPercentage", searchCostPercentage);
            
            logger.info("当前页总成本1占比: {}%, 搜索结果总成本1占比: {}%", costPercentage, searchCostPercentage);
        } catch (Exception e) {
            // 如果计算过程中出现异常，则使用零值作为默认值
            model.addAttribute("totalSearchCost1", BigDecimal.ZERO);
            model.addAttribute("globalTotalCost1", BigDecimal.ZERO);
            model.addAttribute("costPercentage", BigDecimal.ZERO);
            model.addAttribute("searchCostPercentage", BigDecimal.ZERO);
            logger.error("计算总成本1总额或占比时出错: {}", e.getMessage(), e);
        }

        model.addAttribute("projectPage", projectPage);
        model.addAttribute("isAdvancedSearch", true);
        model.addAttribute("searchCriteria", searchCriteria); // 添加搜索条件到模型中，用于页面回显

        // 添加人员列表到模型中，用于负责人下拉选择
        List<String> personnelList = optionsService.getPersonnel();
        model.addAttribute("personnel", personnelList);

        // 添加视觉类型列表到模型中，用于视觉类型下拉选择
        model.addAttribute("visionTypes", optionsService.getVisionTypes());

        // 添加项目类型列表到模型中，用于项目类型下拉选择
        model.addAttribute("projectTypes", optionsService.getProjectTypes());

        return "projects/list";
    }

    /**
     * 显示当前用户负责的项目列表
     */
    @GetMapping("/my")
    public String myProjects(
            @RequestParam(value = "page", defaultValue = "0") int page,
            @RequestParam(value = "size", defaultValue = "20") int size,
            Model model) {

        // 获取当前登录用户
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String currentUsername = authentication.getName();

        // 检查当前用户是否具有管理员或经理权限
        boolean isAdmin = authentication.getAuthorities().stream()
                .anyMatch(a -> a.getAuthority().equals("ROLE_ADMIN"));
        boolean isManager = authentication.getAuthorities().stream()
                .anyMatch(a -> a.getAuthority().equals("ROLE_MANAGER"));

        // 如果既不是管理员也不是经理，则重定向到首页
        if (!isAdmin && !isManager) {
            return "redirect:/dashboard";
        }

        logger.info("查询用户 {} 的项目列表", currentUsername);

        // 创建分页请求，按创建日期降序排序
        Pageable pageable = PageRequest.of(page, Math.min(size, 20), Sort.by(Sort.Direction.DESC, "createdDate"));

        // 获取当前用户负责的项目
        Page<Project> myProjects = projectService.findProjectsByResponsible(currentUsername, pageable);

        logger.info("找到 {} 个项目", myProjects.getTotalElements());

        // 确保所有项目的日期字段都被初始化
        for (Project project : myProjects.getContent()) {
            // 调用getter方法以初始化Transient字段
            project.getPlannedStartDateTime();
            project.getPlannedEndDateTime();

            // 获取项目的“进行中”任务
            List<ProjectTask> inProgressTasks = taskService.findTasksByProjectIdAndStatus(project.getProjectId(),
                    "进行中");
            // 将任务名称连接成字符串，用逗号分隔
            String inProgressTaskNames = inProgressTasks.stream()
                    .map(ProjectTask::getTaskName)
                    .collect(java.util.stream.Collectors.joining(", "));
            // 将进行中任务名称字符串添加到项目对象的备注字段中作为临时存储
            project.setNote(inProgressTaskNames);
            
            // 检查项目是否有电脑信息记录，设置编码信息
            boolean hasComputerInfo = computerInfoService.hasComputerInfoByProjectId(project.getProjectId());
            project.setHasComputerInfo(hasComputerInfo ? "有" : "无");
        }

        // 获取当前用户进行中项目数量
        Long inProgressCount = projectService.countProjectsByResponsibleAndStatus(currentUsername, "进行中");
        model.addAttribute("myInProgressProjectCount", inProgressCount);

        // 添加视觉类型列表到模型中
        model.addAttribute("visionTypes", optionsService.getVisionTypes());

        // 添加到模型
        model.addAttribute("projectPage", myProjects);
        model.addAttribute("activeMenu", "myprojects");

        return "projects/my-projects";
    }

    /**
     * 检查项目是否有未完成任务
     */
    @GetMapping("/{id}/check-uncompleted-tasks")
    @ResponseBody
    public Map<String, Boolean> checkUncompletedTasks(@PathVariable("id") Long id) {
        logger.info("开始检查项目ID: {} 是否有未完成任务", id);
        try {
            boolean hasUncompletedTasks = taskService.hasUncompletedTasks(id);
            logger.info("项目ID: {} 未完成任务检查结果: {}", id, hasUncompletedTasks);

            Map<String, Boolean> response = new HashMap<>();
            response.put("hasUncompletedTasks", hasUncompletedTasks);
            return response;
        } catch (Exception e) {
            logger.error("检查项目ID: {} 未完成任务时发生错误: {}", id, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 归档项目
     */
    @PostMapping("/archive")
    public String archiveProject(
            @RequestParam("projectId") Long id,
            @RequestParam(value = "returnUrl", required = false) String returnUrl,
            RedirectAttributes redirectAttributes) {

        logger.info("尝试归档项目: ID={}", id);

        // 检查当前用户是否具有管理员或经理权限
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String currentUsername = authentication.getName();
        boolean isAdmin = authentication.getAuthorities().stream()
                .anyMatch(a -> a.getAuthority().equals("ROLE_ADMIN"));
        boolean isManager = authentication.getAuthorities().stream()
                .anyMatch(a -> a.getAuthority().equals("ROLE_MANAGER"));

        // 如果既不是管理员也不是经理，则重定向到项目列表页面
        if (!isAdmin && !isManager) {
            redirectAttributes.addFlashAttribute("error", "您没有权限归档项目");
            return "redirect:/projects";
        }

        try {
            Optional<Project> projectOpt = projectService.findProjectById(id);
            if (!projectOpt.isPresent()) {
                redirectAttributes.addFlashAttribute("error", "项目不存在");
                return "redirect:/projects";
            }

            Project project = projectOpt.get();

            // 检查是否有未完成的任务
            if (taskService.hasUncompletedTasks(id)) {
                redirectAttributes.addFlashAttribute("error", "项目还有未完成的任务，无法归档");
                return "redirect:/projects/" + id;
            }

            // 归档项目
            project.setArchive(1);
            Project archivedProject = projectService.saveProject(project);

            // 记录用户活动
            Optional<User> currentUser = userService.findUserByUsername(currentUsername);
            if (currentUser.isPresent()) {
                User user = currentUser.get();
                UserActivityLog log = new UserActivityLog();
                log.setUserId(user.getUserId());
                log.setUsername(currentUsername);
                log.setActivityType(UserActivityLog.ActivityType.UPDATE);
                log.setDescription("归档了项目: " + project.getProjectName());
                log.setEntityType("Project");
                log.setEntityId(archivedProject.getProjectId());
                log.setTimestamp(LocalDateTime.now());
                activityLogService.saveLog(log);
            }

            redirectAttributes.addFlashAttribute("success", "项目已成功归档");

            // 如果提供了返回URL，则返回到该URL
            if (returnUrl != null && !returnUrl.isEmpty()) {
                return "redirect:" + returnUrl;
            }

            return "redirect:/projects";

        } catch (Exception e) {
            logger.error("归档项目时出错: {}", e.getMessage(), e);
            redirectAttributes.addFlashAttribute("error", "归档项目时出错，请稍后再试");
            return "redirect:/projects/" + id;
        }
    }

    /**
     * 取消归档项目
     */
    @PostMapping("/{id}/unarchive")
    public String unarchiveProject(
            @PathVariable("id") Long id,
            RedirectAttributes redirectAttributes) {

        logger.info("尝试取消归档项目: ID={}", id);

        // 检查当前用户是否具有管理员或经理权限
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String currentUsername = authentication.getName();
        boolean isAdmin = authentication.getAuthorities().stream()
                .anyMatch(a -> a.getAuthority().equals("ROLE_ADMIN"));
        boolean isManager = authentication.getAuthorities().stream()
                .anyMatch(a -> a.getAuthority().equals("ROLE_MANAGER"));

        // 如果既不是管理员也不是经理，则重定向到项目列表页面
        if (!isAdmin && !isManager) {
            redirectAttributes.addFlashAttribute("error", "您没有权限取消归档项目");
            return "redirect:/projects";
        }

        try {
            Optional<Project> projectOpt = projectService.findProjectById(id);
            if (!projectOpt.isPresent()) {
                redirectAttributes.addFlashAttribute("error", "项目不存在");
                return "redirect:/projects";
            }

            Project project = projectOpt.get();

            // 取消归档项目
            project.setArchive(0);
            Project unarchivedProject = projectService.saveProject(project);

            // 记录用户活动
            Optional<User> currentUser = userService.findUserByUsername(currentUsername);
            if (currentUser.isPresent()) {
                User user = currentUser.get();
                UserActivityLog log = new UserActivityLog();
                log.setUserId(user.getUserId());
                log.setUsername(currentUsername);
                log.setActivityType(UserActivityLog.ActivityType.UPDATE);
                log.setDescription("取消归档了项目: " + project.getProjectName());
                log.setEntityType("Project");
                log.setEntityId(unarchivedProject.getProjectId());
                log.setTimestamp(LocalDateTime.now());
                activityLogService.saveLog(log);
            }

            redirectAttributes.addFlashAttribute("success", "项目已成功取消归档");
            return "redirect:/projects";

        } catch (Exception e) {
            logger.error("取消归档项目时出错: {}", e.getMessage(), e);
            redirectAttributes.addFlashAttribute("error", "取消归档项目时出错，请稍后再试");
            return "redirect:/projects/" + id;
        }
    }

    @PostMapping("/{id}/complete")
    public String completeProject(
            @PathVariable("id") Long id,
            RedirectAttributes redirectAttributes,
            HttpServletRequest request) {

        logger.info("提交项目请求: ID = {}", id);

        // 获取当前用户信息
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String currentUsername = authentication.getName();
        boolean isAdmin = authentication.getAuthorities().stream()
                .anyMatch(a -> a.getAuthority().equals("ROLE_ADMIN"));

        // 获取项目信息
        Optional<Project> projectOpt = projectService.findProjectById(id);
        if (!projectOpt.isPresent()) {
            redirectAttributes.addFlashAttribute("error", "找不到指定的项目");
            return "redirect:/projects";
        }

        Project project = projectOpt.get();
        boolean isProjectResponsible = currentUsername.equals(project.getResponsible());

        // 权限检查：
        // 1. 管理员可以提交任何项目
        // 2. 项目责任人可以提交自己负责的项目
        
        if (!isAdmin && !isProjectResponsible ) {
            redirectAttributes.addFlashAttribute("error", "您没有权限提交此项目");
            return "redirect:/projects/" + id;
        }

        try {
            // 设置项目状态为已完成
            project.setStatus("已完成");

            // 设置实际结束日期为当前时间
            LocalDateTime now = LocalDateTime.now();
            project.setActualEndDateTime(now);

            // 计算工期（自动计算实际工期）
            calculateDuration(project);

            // 保存项目
            Project savedProject = projectService.saveProject(project);

            // 记录活动日志
            Optional<User> currentUser = userService.findUserByUsername(currentUsername);
            if (currentUser.isPresent()) {
                User user = currentUser.get();
                String ipAddress = getClientIpAddress();
                String description = "提交了项目: " + project.getProjectName();

                activityLogService.logUpdate(
                        user.getUserId(),
                        currentUsername,
                        description,
                        ipAddress,
                        "Project",
                        savedProject.getProjectId(),
                        getAccessType());

                logger.info("用户 {} {} ID: {}", currentUsername, description, savedProject.getProjectId());

                // 记录项目工期到工期日志中
                try {
                    Double hoursChange = savedProject.getDurationDays() != null ? 
                        savedProject.getDurationDays().doubleValue() : 0.0;
                    Double ratedDuration = savedProject.getRatedDurationDays() != null ? 
                        savedProject.getRatedDurationDays().doubleValue() : 0.0;
                    
                    String reason = "项目提交完成，记录实际工期";
                    String remark = String.format("项目[%s]提交完成", 
                        savedProject.getProjectName(), hoursChange, ratedDuration);

                        String responsiblePerson = savedProject.getResponsible() != null ?
                        savedProject.getResponsible() : "未知责任人";
                        
                    // 记录工期日志
                    workHoursLogService.addWorkHours(
                        "项目", 
                        savedProject.getProjectId().intValue(), 
                        hoursChange, 
                        ratedDuration, 
                        reason, 
                        currentUsername, 
                        remark,
                        responsiblePerson,
                        savedProject.getActualStartDateTime() != null ? savedProject.getActualStartDateTime().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : null,
                        savedProject.getActualEndDateTime() != null ? savedProject.getActualEndDateTime().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : null
                    );

                    logger.info("项目提交工期记录成功: 项目ID={}, 实际工期={}天, 额定工期={}天", 
                        savedProject.getProjectId(), hoursChange, ratedDuration);
                } catch (Exception e) {
                    logger.error("记录项目提交工期时出错: {}", e.getMessage(), e);
                    // 工期记录失败不影响项目提交
                }
            }

            redirectAttributes.addFlashAttribute("message", "项目已成功提交");

            // 返回到来源页面
            String referer = request.getHeader("Referer");
            if (referer != null && !referer.isEmpty()) {
                return "redirect:" + referer;
            }

            return "redirect:/projects/" + id;
        } catch (Exception e) {
            logger.error("提交项目时出错: {}", e.getMessage(), e);
            redirectAttributes.addFlashAttribute("error", "提交项目时出错，请稍后再试");
            return "redirect:/projects/" + id;
        }
    }

    /**
     * 查看项目的状态变更日志
     *
     * @param id    项目ID
     * @param model Model对象
     * @return 日志页面
     */
    @GetMapping("/{id}/status-logs")
    public String viewProjectStatusChangeLogs(@PathVariable("id") Long id, Model model) {
        logger.info("查询项目ID={}的状态变更日志", id);

        // 设置activeMenu
        model.addAttribute("activeMenu", "projects");

        Optional<Project> projectOpt = projectService.findProjectById(id);
        if (!projectOpt.isPresent()) {
            model.addAttribute("error", "项目不存在");
            return "error";
        }

        Project project = projectOpt.get();
        model.addAttribute("project", project);

        // 查询该项目的状态变更日志
        Map<String, Object> searchCriteria = new HashMap<>();
        searchCriteria.put("entityType", "Project");
        searchCriteria.put("entityId", id);
        searchCriteria.put("activityType", UserActivityLog.ActivityType.SETTINGS_CHANGE);

        Page<UserActivityLog> logs = activityLogService.searchLogs(
                searchCriteria,
                PageRequest.of(0, 50, Sort.by(Sort.Direction.DESC, "timestamp")));

        logger.info("找到{}条状态变更日志", logs.getTotalElements());

        // 输出所有日志记录
        for (UserActivityLog log : logs.getContent()) {
            logger.info("日志ID: {}, 类型: {}, 描述: {}, 时间: {}",
                    log.getId(), log.getActivityType(), log.getDescription(), log.getTimestamp());
        }

        model.addAttribute("logs", logs);

        return "projects/status-logs";
    }

    /**
     * 计算各任务的额定工期
     */
    @PostMapping("/{id}/calculate-rated-duration")
    @ResponseBody
    public Map<String, Object> calculateFilterTasksRatedDuration(@PathVariable("id") Long projectId) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            logger.info("开始计算项目 {} 的各任务额定工期", projectId);
            
            // 获取项目信息
            Optional<Project> projectOpt = projectService.findProjectById(projectId);
            if (!projectOpt.isPresent()) {
                response.put("success", false);
                response.put("message", "项目不存在");
                return response;
            }
            
            Project project = projectOpt.get();
            
            // 获取项目的额定工期天数
            Double projectRatedDurationDays = project.getRatedDurationDays() != null ? 
                    project.getRatedDurationDays().doubleValue() : 0.0;
            
            if (projectRatedDurationDays <= 0) {
                response.put("success", false);
                response.put("message", "项目额定工期未设置或为0，无法计算任务额定工期");
                return response;
            }
            
            // 获取项目的所有任务
            List<ProjectTask> allTasks = taskService.findAllTasksByProjectId(projectId);
            
            // 使用OptionsService获取前缀列表
            List<String> prefixes = optionsService.getPrefixesByCategoryAndRatio("任务名称", project.getVisionType() != null && project.getVisionType().contains("自制") ? "ratio" : "ratio2");
            
            // 筛选出任务名称前2个字符包含指定前缀的任务
            List<ProjectTask> validTasks = allTasks.stream()
                    .filter(task -> {
                        if (task.getTaskName() != null && task.getTaskName().length() >= 2) {
                            String prefix = task.getTaskName().substring(0, 2);
                            return prefixes.contains(prefix);
                        }
                        return false;
                    })
                    .collect(java.util.stream.Collectors.toList());
            
            // 按前缀分组任务
            Map<String, List<ProjectTask>> tasksByPrefix = validTasks.stream()
                    .collect(java.util.stream.Collectors.groupingBy(task -> task.getTaskName().substring(0, 2)));
            
            // 更新每个符合条件的任务的额定工期
            int updatedCount = 0;
            double totalRatedDuration = 0.0;
            
            for (Map.Entry<String, List<ProjectTask>> entry : tasksByPrefix.entrySet()) {
                String prefix = entry.getKey();
                List<ProjectTask> tasksWithSamePrefix = entry.getValue();
                
                logger.info("处理前缀 {} 的任务，共 {} 个任务", prefix, tasksWithSamePrefix.size());
                
                // 获取该前缀对应的总比例（假设从第一个任务名称获取）
                String firstTaskName = tasksWithSamePrefix.get(0).getTaskName();
                Double totalRatioForPrefix = optionsService.getTaskDurationRatio("任务名称", firstTaskName, project.getVisionType());
                
                if (totalRatioForPrefix != null && totalRatioForPrefix > 0) {
                    // 计算每个任务平均分配的比例
                    Double averageRatio = totalRatioForPrefix / tasksWithSamePrefix.size();
                    
                    logger.info("前缀 {} 的总比例: {}, 平均每个任务比例: {}", 
                               prefix, totalRatioForPrefix, averageRatio);
                    
                    // 为该前缀下的每个任务分配平均比例和计算额定工期
                    for (ProjectTask task : tasksWithSamePrefix) {
                        // 保存原始额定工期值，用于变更检测
                        BigDecimal oldRatedDurationDays = task.getRatedDurationDays();
                        
                        // 计算任务的额定工期
                        Double taskRatedDuration = projectRatedDurationDays * averageRatio;
                        BigDecimal newRatedDurationDays = BigDecimal.valueOf(taskRatedDuration);
                        
                        // 更新任务的额定工期和比例
                        task.setRatio(averageRatio);
                        task.setRatedDurationDays(newRatedDurationDays);
                        
                        // 保存更新后的任务
                        taskService.saveTask(task);
                        
                        // 检查额定工期是否发生变化，如果有变化则记录工时日志
                        if (hasRatedDurationChanged(oldRatedDurationDays, newRatedDurationDays)) {
                            String oldRatedDaysStr = oldRatedDurationDays != null ? 
                                String.format("%.2f天", oldRatedDurationDays.doubleValue()) : "未设置";
                            String newRatedDaysStr = String.format("%.2f天", newRatedDurationDays.doubleValue());
                            
                            try {
                                // 获取当前用户
                                Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
                                String username = authentication != null ? authentication.getName() : "system";
                                String responsiblePerson = task.getResponsible() != null ? task.getResponsible() : "未知责任人";

                                // 记录工时日志
                                workHoursLogService.addWorkHours(
                                    "任务", 
                                    task.getTaskId().intValue(), 
                                    0.0, // 实际工期变化为0，因为这是额定工期的调整
                                    taskRatedDuration, // 新的额定工期                                    
                                    String.format("额定工期从%s变更为%s（自动计算）", oldRatedDaysStr, newRatedDaysStr),
                                    username, 
                                    "额定工期自动计算调整",
                                    responsiblePerson,
                                    task.getActualStartDateTime() != null ? task.getActualStartDateTime().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : null,
                                    task.getActualEndDateTime() != null ? task.getActualEndDateTime().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : null
                                );
                                
                                logger.info("为任务{}记录额定工期变更工时日志: {} -> {}", 
                                           task.getTaskId(), oldRatedDaysStr, newRatedDaysStr);
                            } catch (Exception e) {
                                logger.error("为任务{}记录额定工期变更工时日志时出错: {}", task.getTaskId(), e.getMessage(), e);
                            }
                        }
                        
                        totalRatedDuration += taskRatedDuration;
                        updatedCount++;
                        
                        logger.info("更新任务额定工期: 任务ID={}, 任务名称={}, 平均比例={}, 额定工期={}天", 
                                   task.getTaskId(), task.getTaskName(), averageRatio, taskRatedDuration);
                    }
                } else {
                    logger.warn("前缀 {} 的任务未找到对应的工期分配比例", prefix);
                    for (ProjectTask task : tasksWithSamePrefix) {
                        logger.warn("任务 {} 未找到对应的工期分配比例", task.getTaskName());
                    }
                }
            }
            
            logger.info("项目 {} 计算完成：更新了 {} 个任务，总额定工期 {} 天", 
                       projectId, updatedCount, totalRatedDuration);
            
            response.put("success", true);
            response.put("totalRatedDuration", String.format("%.2f", totalRatedDuration));
            response.put("validTaskCount", validTasks.size());
            response.put("updatedCount", updatedCount);
            response.put("projectRatedDuration", String.format("%.2f", projectRatedDurationDays));
            response.put("message", String.format("计算完成！共找到 %d 个符合条件的任务，成功更新了 %d 个任务的额定工期，总额定工期：%.2f天", 
                                                 validTasks.size(), updatedCount, totalRatedDuration));
            
        } catch (Exception e) {
            logger.error("计算项目 {} 所有任务额定工期时出错: {}", projectId, e.getMessage(), e);
            response.put("success", false);
            response.put("message", "计算过程中发生错误：" + e.getMessage());
        }
        
        return response;
    }

    /**
     * 导出项目列表到Excel（分开下载）
     */
    @GetMapping("/export/projects")
    @PreAuthorize("hasRole('ADMIN')")
    @ResponseBody
    public String exportProjectsExcel(
            HttpServletRequest request,
            HttpServletResponse response) {
        try {
            logger.info("开始导出项目Excel数据");
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String currentUsername = authentication.getName();
            boolean isAdmin = authentication.getAuthorities().stream()
                    .anyMatch(a -> a.getAuthority().equals("ROLE_ADMIN"));
            boolean isManager = authentication.getAuthorities().stream()
                    .anyMatch(a -> a.getAuthority().equals("ROLE_MANAGER"));

            List<Project> projects = getProjectsForExport(request, currentUsername, isAdmin, isManager);
            if (projects.isEmpty()) {
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"success\": false, \"message\": \"没有找到要导出的项目数据\"}");
                return "";
            }

            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            org.apache.poi.ss.usermodel.Workbook projectWorkbook = new org.apache.poi.xssf.usermodel.XSSFWorkbook();
            org.apache.poi.ss.usermodel.Sheet projectSheet = projectWorkbook.createSheet("项目列表");
            createProjectSheet(projectSheet, projects);
            String projectFilename = String.format("projects_%s.xlsx", timestamp);
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=" + projectFilename);
            projectWorkbook.write(response.getOutputStream());
            projectWorkbook.close();
            recordExportActivity(currentUsername, projects.size(), "项目");
            return "";
        } catch (Exception e) {
            logger.error("导出项目Excel时发生错误: {}", e.getMessage(), e);
            try {
                response.setContentType("application/json;charset=UTF-8");
                String escapedErrorMsg = e.getMessage().replace("\"", "\\\"").replace("\\", "\\\\");
                response.getWriter().write("{\"success\": false, \"message\": \"导出失败：" + escapedErrorMsg + "\"}");
            } catch (Exception ignored) {}
            return "";
        }
    }

    /**
     * 导出任务列表到Excel（分开下载）
     */
    @GetMapping("/export/tasks")
    @PreAuthorize("hasRole('ADMIN')")
    @ResponseBody
    public String exportTasksExcel(
            HttpServletRequest request,
            HttpServletResponse response) {
        try {
            logger.info("开始导出任务Excel数据");
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            String currentUsername = authentication.getName();
            boolean isAdmin = authentication.getAuthorities().stream()
                    .anyMatch(a -> a.getAuthority().equals("ROLE_ADMIN"));
            boolean isManager = authentication.getAuthorities().stream()
                    .anyMatch(a -> a.getAuthority().equals("ROLE_MANAGER"));

            List<Project> projects = getProjectsForExport(request, currentUsername, isAdmin, isManager);
            if (projects.isEmpty()) {
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"success\": false, \"message\": \"没有找到要导出的项目数据\"}");
                return "";
            }

            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            org.apache.poi.ss.usermodel.Workbook taskWorkbook = new org.apache.poi.xssf.usermodel.XSSFWorkbook();
            org.apache.poi.ss.usermodel.Sheet taskSheet = taskWorkbook.createSheet("任务列表");
            createTaskSheet(taskSheet, projects);
            String taskFilename = String.format("tasks_%s.xlsx", timestamp);
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=" + taskFilename);
            taskWorkbook.write(response.getOutputStream());
            taskWorkbook.close();
            // 统计所有任务数量
            int totalTaskCount = 0;
            for (Project project : projects) {
                try {
                    List<ProjectTask> tasks = taskService.findAllTasksByProjectId(project.getProjectId());
                    totalTaskCount += (tasks != null ? tasks.size() : 0);
                } catch (Exception e) {
                    logger.warn("统计项目 {} 的任务数量时出错: {}", project.getProjectId(), e.getMessage());
                }
            }
            recordExportActivity(currentUsername, totalTaskCount, "任务");
            return "";
        } catch (Exception e) {
            logger.error("导出任务Excel时发生错误: {}", e.getMessage(), e);
            try {
                response.setContentType("application/json;charset=UTF-8");
                String escapedErrorMsg = e.getMessage().replace("\"", "\\\"").replace("\\", "\\\\");
                response.getWriter().write("{\"success\": false, \"message\": \"导出失败：" + escapedErrorMsg + "\"}");
            } catch (Exception ignored) {}
            return "";
        }
    }

    /**
     * 根据请求参数获取要导出的项目列表
     */
    private List<Project> getProjectsForExport(HttpServletRequest request, String currentUsername, 
                                             boolean isAdmin, boolean isManager) {
        
        // 检查是否有搜索条件
        String[] fieldNames = request.getParameterValues("fieldNames");
        
        if (fieldNames != null && fieldNames.length > 0) {
            // 如果有搜索条件，使用高级搜索
            logger.info("使用搜索条件导出项目");
            Map<String, String> searchCriteria = new HashMap<>();
            
            // 处理搜索条件（复用现有的搜索逻辑）
            for (String fieldName : fieldNames) {
                if (fieldName == null || fieldName.trim().isEmpty()) {
                    continue;
                }
                
                String fieldValue = request.getParameter("field_" + fieldName);
                if (fieldValue != null && !fieldValue.trim().isEmpty()) {
                    searchCriteria.put(fieldName, fieldValue.trim());
                    logger.info("添加搜索条件: {} = {}", fieldName, fieldValue.trim());
                }
                
                // 处理特殊条件（日期范围、数值范围等）
                handleSpecialSearchConditions(request, fieldName, searchCriteria);
            }
            
            return projectService.dynamicSearchProjects(searchCriteria);
        } else {
            // 如果没有搜索条件，获取所有项目
            logger.info("导出所有项目");
            
            if (isAdmin || isManager) {
                return projectService.findAllProjects();
            } else {
                return projectService.findProjectsByResponsible(currentUsername);
            }
        }
    }

    /**
     * 处理特殊搜索条件（复用现有逻辑）
     */
    private void handleSpecialSearchConditions(HttpServletRequest request, String fieldName, 
                                             Map<String, String> searchCriteria) {
        
        if (fieldName.equals("createdDate")) {
            String startDate = request.getParameter("field_createdDate_start");
            String endDate = request.getParameter("field_createdDate_end");
            
            if (startDate != null && !startDate.trim().isEmpty()) {
                searchCriteria.put("createdDate_start", startDate.trim() + " 00:00:00");
            }
            if (endDate != null && !endDate.trim().isEmpty()) {
                searchCriteria.put("createdDate_end", endDate.trim() + " 23:59:59");
            }
        } else if (fieldName.equals("actualEndDate")) {
            String startDate = request.getParameter("field_actualEndDate_start");
            String endDate = request.getParameter("field_actualEndDate_end");
            
            if (startDate != null && !startDate.trim().isEmpty()) {
                searchCriteria.put("actualEndDate_start", startDate.trim() + " 00:00:00");
            }
            if (endDate != null && !endDate.trim().isEmpty()) {
                searchCriteria.put("actualEndDate_end", endDate.trim() + " 23:59:59");
            }
        } else if (fieldName.equals("visionCost")) {
            String minCost = request.getParameter("field_visionCost_min");
            String maxCost = request.getParameter("field_visionCost_max");
            
            if (minCost != null && !minCost.trim().isEmpty()) {
                searchCriteria.put("visionCost_min", minCost.trim());
            }
            if (maxCost != null && !maxCost.trim().isEmpty()) {
                searchCriteria.put("visionCost_max", maxCost.trim());
            }
        } else if (fieldName.equals("cameraQuantity")) {
            String minCamera = request.getParameter("field_cameraQuantity_min");
            String maxCamera = request.getParameter("field_cameraQuantity_max");
            
            if (minCamera != null && !minCamera.trim().isEmpty()) {
                searchCriteria.put("cameraQuantity_min", minCamera.trim());
            }
            if (maxCamera != null && !maxCamera.trim().isEmpty()) {
                searchCriteria.put("cameraQuantity_max", maxCamera.trim());
            }
        } else if (fieldName.equals("quantity")) {
            String minQuantity = request.getParameter("field_quantity_min");
            String maxQuantity = request.getParameter("field_quantity_max");
            
            if (minQuantity != null && !minQuantity.trim().isEmpty()) {
                searchCriteria.put("quantity_min", minQuantity.trim());
            }
            if (maxQuantity != null && !maxQuantity.trim().isEmpty()) {
                searchCriteria.put("quantity_max", maxQuantity.trim());
            }
        }
    }

    /**
     * 创建项目列表工作表
     */
    private void createProjectSheet(org.apache.poi.ss.usermodel.Sheet sheet, List<Project> projects) {
        
        // 创建标题行
        org.apache.poi.ss.usermodel.Row headerRow = sheet.createRow(0);
        String[] headers = {
            "项目ID", "项目编号", "项目名称", "客户名称", "项目类型", "视觉类型", 
            "负责人", "状态", "风险等级", "单机成本1", "单机成本2", "总成本1", "总成本2", "额定工期(天)", "绩效分",
            "难度系数", "相机数量", "销售订单号", "料号",
            "创建时间", "计划开始时间", 
            "计划结束时间", "实际开始时间", "实际结束时间", "备注"
        };
        
        for (int i = 0; i < headers.length; i++) {
            org.apache.poi.ss.usermodel.Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
        }
        
        // 填充数据行
        int rowNum = 1;
        for (Project project : projects) {
            org.apache.poi.ss.usermodel.Row row = sheet.createRow(rowNum++);
            int col = 0;
            row.createCell(col++).setCellValue(project.getProjectId() != null ? project.getProjectId() : 0);
            row.createCell(col++).setCellValue(project.getProjectCode() != null ? project.getProjectCode() : "");
            row.createCell(col++).setCellValue(project.getProjectName() != null ? project.getProjectName() : "");
            row.createCell(col++).setCellValue(project.getCustomerName() != null ? project.getCustomerName() : "");
            row.createCell(col++).setCellValue(project.getProjectType() != null ? project.getProjectType() : "");
            row.createCell(col++).setCellValue(project.getVisionType() != null ? project.getVisionType() : "");
            row.createCell(col++).setCellValue(project.getResponsible() != null ? project.getResponsible() : "");
            row.createCell(col++).setCellValue(project.getStatus() != null ? project.getStatus() : "");
            row.createCell(col++).setCellValue(project.getRisk() != null ? project.getRisk() : "");
            row.createCell(col++).setCellValue(project.getVisionCost() != null ? project.getVisionCost().doubleValue() : 0.0);
            row.createCell(col++).setCellValue(project.getVisionCost2() != null ? project.getVisionCost2().doubleValue() : 0.0);
            row.createCell(col++).setCellValue(project.getTotalCost1() != null ? project.getTotalCost1().doubleValue() : 0.0);
            row.createCell(col++).setCellValue(project.getTotalCost2() != null ? project.getTotalCost2().doubleValue() : 0.0);
            row.createCell(col++).setCellValue(project.getRatedDurationDays() != null ? project.getRatedDurationDays().doubleValue() : 0.0);
            row.createCell(col++).setCellValue(project.getBonus() != null ? project.getBonus().doubleValue() : 0.0);
            row.createCell(col++).setCellValue(project.getDifficultyCoefficient() != null ? project.getDifficultyCoefficient().doubleValue() : 0.0);
            row.createCell(col++).setCellValue(project.getCameraQuantity() != null ? project.getCameraQuantity().doubleValue() : 0.0);
            row.createCell(col++).setCellValue(project.getSalesOrderNumber() != null ? project.getSalesOrderNumber() : "");
            row.createCell(col++).setCellValue(project.getProductPartNumber() != null ? project.getProductPartNumber() : "");
            row.createCell(col++).setCellValue(project.getCreatedDate() != null ? project.getCreatedDate() : "");
            row.createCell(col++).setCellValue(project.getPlannedStartDate() != null ? project.getPlannedStartDate() : "");
            row.createCell(col++).setCellValue(project.getPlannedEndDate() != null ? project.getPlannedEndDate() : "");
            row.createCell(col++).setCellValue(project.getActualStartDate() != null ? project.getActualStartDate() : "");
            row.createCell(col++).setCellValue(project.getActualEndDate() != null ? project.getActualEndDate() : "");
            row.createCell(col++).setCellValue(project.getRemarks() != null ? project.getRemarks() : "");
        }
        
        // 自动调整列宽
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }
    }

    /**
     * 创建任务列表工作表
     */
    private void createTaskSheet(org.apache.poi.ss.usermodel.Sheet sheet, List<Project> projects) {
        
        // 创建标题行
        org.apache.poi.ss.usermodel.Row headerRow = sheet.createRow(0);
        String[] headers = {
            "任务ID", "项目编号", "项目名称", "任务名称", "视觉类型", "任务类型", "负责人", 
            "状态", "进度(%)", "风险等级", "额定工期(天)", "实际工期", "实际开始时间", "实际结束时间", "绩效分", "比例", "评论天数", 
            "创建时间", "备注"
        };
        for (int i = 0; i < headers.length; i++) {
            org.apache.poi.ss.usermodel.Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
        }
        
        // 填充任务数据
        int rowNum = 1;
        for (Project project : projects) {
            try {
                // 获取项目的所有任务
                List<ProjectTask> tasks = taskService.findAllTasksByProjectId(project.getProjectId());
                
                for (ProjectTask task : tasks) {
                    org.apache.poi.ss.usermodel.Row row = sheet.createRow(rowNum++);
                    
                    row.createCell(0).setCellValue(task.getTaskId() != null ? task.getTaskId() : 0);
                    row.createCell(1).setCellValue(project.getProjectCode() != null ? project.getProjectCode() : "");
                    row.createCell(2).setCellValue(project.getProjectName() != null ? project.getProjectName() : "");
                    row.createCell(3).setCellValue(task.getTaskName() != null ? task.getTaskName() : "");
                    row.createCell(4).setCellValue(project.getVisionType() != null ? project.getVisionType() : "");
                    row.createCell(5).setCellValue(task.getType() != null ? task.getType() : "");
                    row.createCell(6).setCellValue(task.getResponsible() != null ? task.getResponsible() : "");
                    row.createCell(7).setCellValue(task.getStatus() != null ? task.getStatus() : "");
                    row.createCell(8).setCellValue(task.getProgress() != null ? task.getProgress() : 0);
                    row.createCell(9).setCellValue(task.getRisk() != null ? task.getRisk() : "");
                    row.createCell(10).setCellValue(task.getRatedDurationDays() != null ? task.getRatedDurationDays().doubleValue() : 0.0);
                    row.createCell(11).setCellValue(task.getDurationDays() != null ? task.getDurationDays().doubleValue() : 0.0);
                    row.createCell(12).setCellValue(task.getActualStartDate() != null ? task.getActualStartDate() : "");
                    row.createCell(13).setCellValue(task.getActualEndDate() != null ? task.getActualEndDate() : "");
                    row.createCell(14).setCellValue(task.getBonus() != null ? task.getBonus().doubleValue() : 0.0);
                    row.createCell(15).setCellValue(task.getRatio() != null ? task.getRatio() : 0.0);
                    row.createCell(16).setCellValue(task.getCommentDays() != null ? task.getCommentDays() : 0.0);
                    row.createCell(17).setCellValue(task.getCreatedDate() != null ? task.getCreatedDate() : "");
                    row.createCell(18).setCellValue(task.getRemarks() != null ? task.getRemarks() : "");
                }
            } catch (Exception e) {
                logger.warn("获取项目 {} 的任务列表时出错: {}", project.getProjectId(), e.getMessage());
            }
        }
        
        // 自动调整列宽
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }
    }

    /**
     * 记录导出活动日志
     */
    private void recordExportActivity(String currentUsername, int count, String type) {
        try {
            Optional<User> currentUser = userService.findUserByUsername(currentUsername);
            if (currentUser.isPresent()) {
                User user = currentUser.get();
                UserActivityLog log = new UserActivityLog();
                log.setUserId(user.getUserId());
                log.setUsername(currentUsername);
                log.setActivityType(UserActivityLog.ActivityType.EXPORT);
                if ("项目".equals(type)) {
                    log.setDescription(String.format("导出了 %d 个项目的Excel数据", count));
                    log.setEntityType("Project");
                } else if ("任务".equals(type)) {
                    log.setDescription(String.format("导出了 %d 条任务的Excel数据", count));
                    log.setEntityType("Task");
                } else {
                    log.setDescription(String.format("导出了 %d 条数据的Excel", count));
                    log.setEntityType(type);
                }
                log.setTimestamp(LocalDateTime.now());
                log.setIpAddress(""); // 可以通过HttpServletRequest获取IP
                log.setEntityId(0L); // 批量导出，设置为0
                activityLogService.saveLog(log);
                logger.info("已记录用户 {} 的导出活动日志", currentUsername);
            }
        } catch (Exception e) {
            logger.warn("记录导出活动日志时出错: {}", e.getMessage());
        }
    }
    
    /**
     * 检查额定工期是否发生变化
     * 专门处理BigDecimal的比较，避免NULL和ZERO的比较问题
     *
     * @param oldValue 旧的额定工期值
     * @param newValue 新的额定工期值
     * @return true如果发生了实际变化，false如果没有变化
     */
    private boolean hasRatedDurationChanged(BigDecimal oldValue, BigDecimal newValue) {
        // 如果两个值都为null，认为没有变化
        if (oldValue == null && newValue == null) {
            return false;
        }
        
        // 如果一个为null，另一个不为null，认为发生变化
        if (oldValue == null || newValue == null) {
            // 但是要特殊处理：如果其中一个是null，另一个是0，认为没有实际变化
            BigDecimal nonNullValue = oldValue != null ? oldValue : newValue;
            if (nonNullValue != null) {
                return nonNullValue.compareTo(BigDecimal.ZERO) != 0;
            }
            return false;
        }
        
        // 两个都不为null时，使用compareTo进行精确比较
        // compareTo方法会正确处理精度问题
        return oldValue.compareTo(newValue) != 0;
    }

    /**
     * 计算项目中符合前缀条件的任务的工期统计信息
     * 
     * @param projectId 项目ID
     * @param project 项目对象
     * @return 包含totalRatedDuration, totalDuration, totalRatio的Map
     */
    private Map<String, Double> calculateTaskDurationStatistics(Long projectId, Project project) {
        // 获取所有任务名称中前2个字符包含指定前缀的任务的额定工期总数和工期总数（不分页，获取所有任务）
        List<ProjectTask> allTasks = taskService.findAllTasksByProjectId(projectId);

        // 使用OptionsService获取前缀列表
        List<String> prefixes = optionsService.getPrefixesByCategoryAndRatio("任务名称", 
            project.getVisionType() != null && project.getVisionType().contains("自制") ? "ratio" : "ratio2");

        // 过滤出前缀匹配的任务
        java.util.function.Predicate<ProjectTask> prefixFilter = task -> {
            if (task.getTaskName() != null && task.getTaskName().length() >= 2) {
                String prefix = task.getTaskName().substring(0, 2);
                return prefixes.contains(prefix);
            }
            return false;
        };

        Double totalRatedDuration = allTasks.stream()
                .filter(prefixFilter)
                .mapToDouble(task -> task.getRatedDurationDays() != null ? task.getRatedDurationDays().doubleValue() : 0.0)
                .sum();

        Double totalDuration = allTasks.stream()
                .filter(prefixFilter)
                .mapToDouble(task -> task.getDurationDays() != null ? task.getDurationDays().doubleValue() : 0.0)
                .sum();

        // 计算比例总数
        Double totalRatio = allTasks.stream()
                .filter(prefixFilter)
                .mapToDouble(task -> task.getRatio() != null ? task.getRatio() : 0.0)
                .sum();

        // 计算绩效分总数
        Double totalBonus = allTasks.stream()
                .filter(prefixFilter)
                .mapToDouble(task -> task.getBonus() != null ? task.getBonus().doubleValue() : 0.0)
                .sum();

        // 将结果封装到Map中返回
        Map<String, Double> statistics = new HashMap<>();
        statistics.put("totalRatedDuration", totalRatedDuration);
        statistics.put("totalDuration", totalDuration);
        statistics.put("totalRatio", totalRatio);
        statistics.put("totalBonus", totalBonus);

        return statistics;
    }
}
