/* 全局样式 */
body {
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* 全局字体大小调整 */
html {
    font-size: 80%; /* 将默认字体大小缩小20% */
}

/* 侧边栏样式 */
.sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
    height: 100vh;
    overflow-y: auto;
    transition: width 0.3s ease, min-width 0.3s ease;
}

/* 覆盖Bootstrap的默认宽度 */
@media (min-width: 768px) {
    /* 侧边栏正常状态的宽度 - 优化为12%减少空白区域 */
    .sidebar {
        width: 12% !important;
        flex: 0 0 12% !important;
    }
    
    /* 主内容区域正常状态的宽度 */
    main.col-md-10 {
        width: 88% !important;
        flex: 0 0 88% !important;
        margin-left: 12% !important;
    }
    
    /* 日历页面使用标准Bootstrap栅格 */
    .calendar-layout .sidebar {
        width: 16.66666667% !important;
        flex: 0 0 16.66666667% !important;
    }
    
    .calendar-layout main.col-md-10 {
        width: 83.33333333% !important;
        flex: 0 0 83.33333333% !important;
        margin-left: 16.66666667% !important;
    }
}

@media (min-width: 992px) {
    .col-lg-1 {
        width: 10% !important; /* 优化为10%减少空白区域 */
    }

    .col-lg-11 {
        width: 90% !important; /* 主内容区域宽度调整 */
        margin-left: 10% !important;
    }
}

/* 收缩侧边栏样式 */
.sidebar.collapsed {
    width: 60px !important;
    min-width: 60px !important;
}

.sidebar.collapsed .nav-link span,
.sidebar.collapsed .sidebar-title,
.sidebar.collapsed .sidebar-username,
.sidebar.collapsed .badge,
.sidebar.collapsed .bi-chevron-down,
.sidebar.collapsed .my-work-arrow,
.sidebar.collapsed .collapse:not(#myWorkSubmenu),
.sidebar.collapsed .nav-item form {
    display: none;
}

/* 侧边栏收缩时按钮区域的样式 */
.sidebar.collapsed .d-flex.justify-content-between {
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.sidebar.collapsed .text-center {
    order: 2;
}

.sidebar.collapsed .d-flex.align-items-center {
    order: 1;
    flex-direction: column;
    gap: 0.25rem;
}

.sidebar.collapsed #soundToggleBtn,
.sidebar.collapsed #sidebarCollapseBtn {
    width: 32px;
    height: 32px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0;
}

.sidebar.collapsed .nav-link {
    text-align: center;
    padding: 0.5rem 0;
}

.sidebar.collapsed .nav-link i.me-2 {
    margin-right: 0 !important;
    font-size: 1.2rem;
}

.sidebar.collapsed #sidebarCollapseBtn i {
    transform: rotate(180deg);
}

.sidebar-collapse-btn {
    display: block;
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    border-radius: 0.2rem;
    transition: all 0.3s;
}

/* 主内容区域过渡效果 */
main {
    transition: margin-left 0.3s ease, width 0.3s ease;
    padding-top: 1.5rem;
}

.sidebar .position-sticky {
    height: auto;
    max-height: calc(100vh - 48px);
    overflow-y: auto;
    padding-bottom: 1.5rem;
}

.sidebar .nav-link {
    font-weight: 500;
    color: rgba(255, 255, 255, 0.8);
    padding: 0.5rem 1rem;
    margin-bottom: 0.2rem;
}

.sidebar .nav-link i {
    margin-right: 4px;
    color: rgba(255, 255, 255, 0.6);
}

.sidebar .nav-link:hover {
    color: #ffffff;
    background-color: rgba(255, 255, 255, 0.1);
}

.sidebar .nav-link:hover i {
    color: #ffffff;
}

.sidebar .nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
}

/* 侧边栏收缩时的主内容区域样式 */
@media (min-width: 768px) {
    /* 侧边栏收缩时的样式调整 */
    .sidebar.collapsed {
        width: 60px !important;
        flex: 0 0 60px !important;
    }
    
    /* 主内容区域在侧边栏收缩时的样式 */
    main.sidebar-collapsed {
        width: calc(100% - 60px) !important;
        flex: 0 0 calc(100% - 60px) !important;
        margin-left: 60px !important;
    }
    
    /* 确保在所有页面上都能正确应用 */
    .sidebar.collapsed + main {
        margin-left: 60px !important;
        width: calc(100% - 60px) !important;
        flex: 0 0 calc(100% - 60px) !important;
    }
}

/* 表格样式 */
.table-responsive {
    overflow-x: auto;
}

.table th {
    background-color: #f8f9fa;
    font-weight: 600;
}

/* 卡片样式 */
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1.5rem;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    padding: 0.75rem 1.25rem;
}

/* 表单样式 */
.form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.form-label {
    font-weight: 500;
}

.invalid-feedback {
    font-size: 0.875rem;
}

/* 按钮样式 */
.btn {
    font-weight: 500;
}

.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
}

/* 登录页面样式 */
.login-container {
    max-width: 400px;
    margin: 100px auto;
}

/* 响应式调整 */
@media (max-width: 767.98px) {
    .sidebar {
        position: fixed;
        max-width: 100%;
        width: 85%;
        z-index: 1030;
        height: 100vh;
        padding-top: 0;
        transform: translateX(-100%);
        transition: transform 0.3s ease-in-out;
        background-color: #343a40; /* 确保背景颜色 */
    }

    .sidebar.show {
        transform: translateX(0);
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.2); /* 添加阴影突出层次 */
    }

    .sidebar .position-sticky {
        height: auto;
        max-height: 100vh;
        overflow-y: auto;
        padding-top: 1rem; /* 添加顶部内边距 */
    }

    main {
        margin-left: 0 !important;
        width: 100% !important;
        max-width: 100% !important;
        flex: 0 0 100% !important;
        transition: margin-left 0.3s ease-in-out; /* 添加过渡效果 */
    }

    /* 添加遮罩层，点击时关闭侧边栏 */
    body::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 90;
        display: none;
    }

    body.sidebar-open::before {
        display: block;
    }

    /* 在小屏幕上减小表格字体大小 */
    .table {
        font-size: 0.85rem;
    }

    /* 确保小屏幕上表格内容不重叠 */
    .table-responsive {
        min-height: .01%;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.875rem;
}

/* 进度条样式调整 */
.progress {
    height: 24px; /* 统一进度条高度 */
    margin: 0;
    vertical-align: middle;
    display: flex;
    align-items: center;
}

.progress-bar {
    line-height: 24px; /* 与进度条高度一致 */
    font-weight: 500;
    font-size: 0.85rem;
}

/* 表格中的进度条适配 */
td .progress {
    margin-top: 0;
    margin-bottom: 0;
    min-width: 100px; /* 确保进度条有最小宽度 */
}

/* 侧边栏收缩时的子菜单样式 */
.submenu-hover {
    display: block !important;
    background-color: #343a40;
    border-radius: 0 4px 4px 0;
    box-shadow: 0 0 10px rgba(0,0,0,0.2);
}

.submenu-hover .nav-link {
    padding: 0.5rem 1rem;
    white-space: nowrap;
}

.sidebar.collapsed .submenu-hover {
    display: block !important;
    position: absolute;
    left: 100%;
    z-index: 1000;
    min-width: 200px;
}

/* 侧边栏收缩时的我的工作相关样式 */
/* 隐藏"我的工作"菜单项 */
.sidebar.collapsed .my-work-toggle {
    display: none !important;
}

/* 显示子菜单项为一级菜单 */
.sidebar.collapsed #myWorkSubmenu {
    display: block !important;
    position: static;
    margin-left: 0 !important;
    padding-left: 0 !important;
    background-color: transparent;
    box-shadow: none;
}

.sidebar.collapsed .my-work-submenu {
    margin-left: 0 !important;
    padding-left: 0 !important;
}

.sidebar.collapsed .my-work-item {
    margin-bottom: 0.5rem;
}

.sidebar.collapsed .my-work-item .nav-link {
    padding: 0.5rem 0;
    text-align: center;
}

.sidebar.collapsed .my-work-item .nav-link span {
    display: none;
}

.sidebar.collapsed .my-work-item .nav-link i.me-2 {
    margin-right: 0 !important;
    font-size: 1.2rem;
}

.sidebar.collapsed .my-work-item .badge {
    display: none;
}

/* 初始化时的样式 */
.sidebar-init-collapsed .sidebar .my-work-toggle {
    display: none !important;
}

.sidebar-init-collapsed .sidebar #myWorkSubmenu {
    display: block !important;
    position: static;
    margin-left: 0 !important;
    padding-left: 0 !important;
    background-color: transparent;
    box-shadow: none;
}

.sidebar-init-collapsed .sidebar .my-work-submenu {
    margin-left: 0 !important;
    padding-left: 0 !important;
}

.sidebar-init-collapsed .sidebar .my-work-item {
    margin-bottom: 0.5rem;
}

.sidebar-init-collapsed .sidebar .my-work-item .nav-link {
    padding: 0.5rem 0;
    text-align: center;
}

.sidebar-init-collapsed .sidebar .my-work-item .nav-link span {
    display: none;
}

.sidebar-init-collapsed .sidebar .my-work-item .nav-link i.me-2 {
    margin-right: 0 !important;
    font-size: 1.2rem;
}

.sidebar-init-collapsed .sidebar .my-work-item .badge {
    display: none;
}

/* 页面加载时立即应用侧边栏收缩状态，避免闪烁 */
.sidebar-init-collapsed .sidebar {
    width: 60px !important;
    min-width: 60px !important;
}

.sidebar-init-collapsed .sidebar .nav-link span,
.sidebar-init-collapsed .sidebar .sidebar-title,
.sidebar-init-collapsed .sidebar .sidebar-username,
.sidebar-init-collapsed .sidebar .badge,
.sidebar-init-collapsed .sidebar .bi-chevron-down,
.sidebar-init-collapsed .sidebar .my-work-arrow,
.sidebar-init-collapsed .sidebar .collapse:not(#myWorkSubmenu),
.sidebar-init-collapsed .sidebar .nav-item form {
    display: none;
}

/* 初始化收缩状态时按钮区域的样式 */
.sidebar-init-collapsed .sidebar .d-flex.justify-content-between {
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.sidebar-init-collapsed .sidebar .text-center {
    order: 2;
}

.sidebar-init-collapsed .sidebar .d-flex.align-items-center {
    order: 1;
    flex-direction: column;
    gap: 0.25rem;
}

.sidebar-init-collapsed .sidebar #soundToggleBtn,
.sidebar-init-collapsed .sidebar #sidebarCollapseBtn {
    width: 32px;
    height: 32px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0;
}

.sidebar-init-collapsed .sidebar .nav-link {
    text-align: center;
    padding: 0.5rem 0;
}

.sidebar-init-collapsed .sidebar .nav-link i.me-2 {
    margin-right: 0 !important;
    font-size: 1.2rem;
}

.sidebar-init-collapsed .sidebar #sidebarCollapseBtn i {
    transform: rotate(180deg);
}

.sidebar-init-collapsed main {
    margin-left: 60px !important;
}

@media (min-width: 768px) {
    .sidebar-init-collapsed main {
        width: calc(100% - 60px) !important;
        flex: 0 0 calc(100% - 60px) !important;
    }
}

.bg-mycolor {
    background-color: rgb(192, 93, 12); /* 此例为橙色 */
}