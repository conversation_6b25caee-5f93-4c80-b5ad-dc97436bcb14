package com.mylog.service;

import com.mylog.model.ProjectTask;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 报表服务接口
 */
public interface ReportService {

    /**
     * 获取任务状态分布数据
     */
    Map<String, Long> getTaskStatusDistribution(Long projectId, LocalDate startDate, LocalDate endDate);

    /**
     * 获取任务风险分布数据
     */
    Map<String, Long> getTaskRiskDistribution(Long projectId, LocalDate startDate, LocalDate endDate);

    /**
     * 获取任务负责人分布数据
     */
    Map<String, Long> getTaskResponsibleDistribution(Long projectId, LocalDate startDate, LocalDate endDate);

    /**
     * 根据筛选条件获取任务列表
     */
    List<ProjectTask> getTasksByFilter(Long projectId, LocalDate startDate, LocalDate endDate);

    /**
     * 获取项目进度数据
     */
    Map<String, Double> getProjectProgressData(Long projectId, LocalDate startDate, LocalDate endDate);

    /**
     * 获取任务完成率数据
     */
    Map<String, Double> getTaskCompletionData(Long projectId, LocalDate startDate, LocalDate endDate);

    /**
     * 获取进度延迟任务
     */
    List<ProjectTask> getDelayedTasks(Long projectId, LocalDate startDate, LocalDate endDate);

    /**
     * 获取高风险任务列表
     */
    List<ProjectTask> getHighRiskTasks(Long projectId, LocalDate startDate, LocalDate endDate);

    /**
     * 获取风险趋势数据
     */
    Map<String, Map<String, Long>> getRiskTrendData(Long projectId, LocalDate startDate, LocalDate endDate);

    /**
     * 获取各负责人工作量分析数据
     */
    Map<String, Map<String, Long>> getResponsibleWorkloadData(Long projectId, LocalDate startDate, LocalDate endDate);

    /**
     * 获取人员效率对比数据
     */
    Map<String, Double> getResponsibleEfficiencyData(Long projectId, LocalDate startDate, LocalDate endDate);

    /**
     * 获取违规登记任务列表
     * 条件：
     * 1. 任务状态为"进行中"
     * 2. 任务名称不包含"待调试"
     * 3. 任务名称不包含"人"
     * 4. (评论天数>7 且 任务名称不包含"培训") 或 (评论天数>15 且 任务名称包含"培训")
     */
    List<List<ProjectTask>> getViolationTasks();

    /**
     * 获取自定义报表数据
     */
    Map<String, Object> getCustomReportData(String reportType, String groupBy, LocalDate startDate, LocalDate endDate);
} 