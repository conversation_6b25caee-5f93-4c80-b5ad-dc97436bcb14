package com.mylog.task;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.mylog.model.SubTask;
import com.mylog.model.ProjectTask;
import com.mylog.model.user.User;
import com.mylog.service.SubTaskService;
import com.mylog.service.TaskService;
import com.mylog.service.UserService;
import com.mylog.util.WeixinMessageUtil;

/**
 * 微信@提及定时任务
 * 每天上午10点自动执行，搜索所有状态为进行中的subtask，
 * 提取评论中包含@的用户并发送微信通知
 */
@Component
public class WeixinMentionScheduledTask {

    private static final Logger logger = LoggerFactory.getLogger(WeixinMentionScheduledTask.class);
    
    @Autowired
    private SubTaskService subTaskService;
    
    @Autowired
    private TaskService taskService;
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private WeixinMessageUtil weixinMessageUtil;
    
    /**
     * 每天8点30分自动执行
     */
    @Scheduled(cron = "0 30 08 * * ?")             // 每天8点30分执行
    public void processWeixinMentionNotifications() {
        try {
            logger.info("开始执行微信@提及定时任务");
            
            // 搜索所有状态为"进行中"的subtask
            Map<String, String> searchCriteria = new HashMap<>();
            searchCriteria.put("status", "进行中");
            
            // 由于是后台任务，获取所有符合条件的subtask，不分页
            List<SubTask> allInProgressSubTasks = findAllSubTasksByCriteria(searchCriteria);
            
            logger.info("找到 {} 个状态为'进行中'的subtask", allInProgressSubTasks.size());
            
            int processedCount = 0;
            int mentionedCount = 0;
            
            // 遍历所有subtask
            for (SubTask subTask : allInProgressSubTasks) {
                String logContent = subTask.getLogContent();
                
                // 检查评论内容是否包含@符号
                if (logContent != null && logContent.contains("@")) {
                    // 提取所有@后面的姓名
                    List<String> mentionedNames = extractMentionedNames(logContent);
                    
                    if (!mentionedNames.isEmpty()) {
                        mentionedCount++;
                        logger.debug("在subtask {} 中发现 {} 个被@的用户: {}", 
                                subTask.getSubTaskId(), mentionedNames.size(), mentionedNames);
                        
                        // 获取任务名称
                        String taskName = "未知任务";
                        Optional<ProjectTask> taskOpt = taskService.findTaskById(subTask.getTaskId());
                        if (taskOpt.isPresent()) {
                            taskName = taskOpt.get().getTaskName();
                        }
                        
                        // 获取创建者名称
                        String creatorName = subTask.getCreatedBy() != null ? subTask.getCreatedBy() : "系统";
                        
                        // 发送微信通知
                        sendWeixinMentionNotification(mentionedNames, creatorName, taskName, subTask);
                        processedCount++;
                    }
                }
            }
            
            logger.info("微信@提及定时任务执行完成: 共处理 {} 个包含@的subtask，发送了 {} 条通知", 
                    mentionedCount, processedCount);
            
        } catch (Exception e) {
            logger.error("执行微信@提及定时任务时发生错误", e);
        }
    }
    
    /**
     * 根据搜索条件查找所有子任务
     */
    private List<SubTask> findAllSubTasksByCriteria(Map<String, String> searchCriteria) {
        try {
            List<SubTask> allSubTasks = new ArrayList<>();
            
            // 使用Pageable设置为每页1000条记录，获取第一页数据
            // 根据项目实际情况，1000条应该足够处理日常任务需求
            org.springframework.data.domain.Pageable pageable = org.springframework.data.domain.PageRequest.of(0, 1000);
            org.springframework.data.domain.Page<SubTask> page = subTaskService.dynamicSearchSubTasks(searchCriteria, pageable);
            
            allSubTasks.addAll(page.getContent());
            
            logger.debug("通过dynamicSearchSubTasks查询到 {} 个符合条件的子任务", allSubTasks.size());
            
            return allSubTasks;
        } catch (Exception e) {
            logger.error("查询子任务时发生错误", e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 从评论内容中提取所有@后面的姓名
     */
    private List<String> extractMentionedNames(String content) {
        Set<String> mentionedNames = new HashSet<>();
        
        // 正则表达式匹配@后面的中文姓名（假设姓名由2-4个中文字符组成）
        Pattern pattern = Pattern.compile("@([\u4e00-\u9fa5]{2,4})[\s,，。.]?");
        Matcher matcher = pattern.matcher(content);
        
        while (matcher.find()) {
            String name = matcher.group(1);
            mentionedNames.add(name);
        }
        
        return new ArrayList<>(mentionedNames);
    }
    
    /**
     * 发送微信@提及通知
     */
    private void sendWeixinMentionNotification(List<String> recipientName, String senderName, String taskName, 
            SubTask subTask) {
        try {
            // 构建所有微信ID的@链接
            List<String> weixinIDs = new ArrayList<>();
            for (String name : recipientName) {
                Optional<User> receiverUser = userService.findUserByUsername(name);
                if (receiverUser.isPresent() && receiverUser.get().getWeixinID() != null
                        && !receiverUser.get().getWeixinID().isEmpty()) {
                    weixinIDs.add(receiverUser.get().getWeixinID());
                    logger.debug("找到用户 {} 的微信ID: {}", name, receiverUser.get().getWeixinID());
                } else {
                    logger.warn("未找到用户 {} 的微信ID，使用用户名代替", name);
                    weixinIDs.add(name); // 如果没有weixinID，则使用用户名
                }
            }
            
            StringBuilder contentBuilder = new StringBuilder();
            // 拼接所有@ID，格式为 <@张三>，<@李四>
            List<String> atList = new ArrayList<>();
            for (String id : weixinIDs) {
                atList.add("<@" + id + ">");
            }
            contentBuilder.append(String.join("，", atList)).append("\n")
                    .append("创建人：").append(senderName).append("   任务：")
                    .append(String.format("[%s](https://prj.cpolar.cn/task/%d)", taskName, subTask.getTaskId()))
                    .append("\n")
                    .append("评论内容：").append(truncateContent(subTask.getLogContent(), 100)).append("\n");
            
            // 如果子任务状态为进行中且有实际开始时间，计算已经开始的小时数
            if ("进行中".equals(subTask.getStatus()) && subTask.getActualStartDate() != null) {
                LocalDateTime now = LocalDateTime.now();
                long hours = java.time.Duration.between(subTask.getActualStartDate(), now).toHours();
                if (hours > 0) {
                    contentBuilder.append("本子任务已经开始了").append(hours).append("个小时，请及时处理。\n");
                }
            }
            
            // 调用微信消息发送工具
            weixinMessageUtil.sendWeixinMessage("任务评论提醒", contentBuilder.toString(), recipientName);
            logger.debug("成功发送微信@提及通知给 {}", recipientName);
            
        } catch (Exception e) {
            logger.error("发送微信@提及通知失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 截断文本内容，避免消息过长
     */
    private String truncateContent(String content, int maxLength) {
        if (content == null)
            return "";
        if (content.length() <= maxLength)
            return content;
        return content.substring(0, maxLength) + "...";
    }
}