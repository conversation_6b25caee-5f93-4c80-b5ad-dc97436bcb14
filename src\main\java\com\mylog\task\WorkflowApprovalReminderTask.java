package com.mylog.task;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.mylog.model.Message;
import com.mylog.model.RewardPenaltyRecord;
import com.mylog.model.user.User;
import com.mylog.model.workflow.ApprovalRecord;
import com.mylog.model.workflow.WorkflowInstance;
import com.mylog.model.workflow.WorkflowInstance.WorkflowStatus;
import com.mylog.service.ApprovalRecordService;
import com.mylog.service.MessageService;
import com.mylog.service.RewardPenaltyRecordService;
import com.mylog.service.UserService;
import com.mylog.service.WorkflowInstanceService;
import com.mylog.util.WeixinMessageUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 流程审批超时提醒定时任务
 * 检查处理中状态的流程实例，若审批时间超过8小时，则发送提醒通知
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WorkflowApprovalReminderTask {

    private static final Logger logger = LoggerFactory.getLogger(WorkflowApprovalReminderTask.class);

    // 审批超时时间设置为4小时（单位：小时）
    private static final long APPROVAL_TIMEOUT_HOURS = 4;

    private final WorkflowInstanceService workflowInstanceService;
    private final ApprovalRecordService approvalRecordService;

    @Autowired
    private MessageService messageService;

    @Autowired
    private UserService userService;

    @Autowired
    private WeixinMessageUtil weixinMessageUtil;

    @Autowired
    private RewardPenaltyRecordService rewardPenaltyRecordService;

    /**
     * 构造函数执行后的初始化方法
     */
    @jakarta.annotation.PostConstruct
    public void init() {
        logger.info("流程审批超时提醒定时任务已初始化");
    }

    /**
     * 计算两个时间点之间的工作时间（排除晚上8点到早上8点）
     * 
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 工作时间小时数
     */
    private long calculateWorkingHours(LocalDateTime startTime, LocalDateTime endTime) {
        if (startTime.isAfter(endTime)) {
            return 0;
        }

        long totalWorkingMinutes = 0;

        // 定义工作时间段（早上8点到晚上8点）
        final int WORK_START_HOUR = 8;
        final int WORK_END_HOUR = 20;

        LocalDateTime current = startTime;

        // 按天分段计算，提高效率
        while (current.isBefore(endTime)) {
            LocalDateTime currentDateStart = current.toLocalDate().atStartOfDay();
            LocalDateTime currentDateEnd = currentDateStart.plusDays(1);
            LocalDateTime actualEnd = currentDateEnd.isBefore(endTime) ? currentDateEnd : endTime;

            // 计算当天的工作时间段
            LocalDateTime workStart = currentDateStart.plusHours(WORK_START_HOUR);
            LocalDateTime workEnd = currentDateStart.plusHours(WORK_END_HOUR);

            // 如果当前时间晚于当天工作开始时间，调整工作开始时间
            if (current.isAfter(workStart)) {
                workStart = current;
            }

            // 如果实际结束时间早于当天工作结束时间，调整工作结束时间
            if (actualEnd.isBefore(workEnd)) {
                workEnd = actualEnd;
            }

            // 如果工作时间段有效，计算分钟数
            if (workStart.isBefore(workEnd)) {
                totalWorkingMinutes += java.time.Duration.between(workStart, workEnd).toMinutes();
            }

            current = actualEnd;
        }

        // 将分钟转换为小时，向下取整（小于60分钟的不计算）
        return totalWorkingMinutes / 60;
    }

    /**
     * 定时检查处理中的流程实例，对超时未审批的实例发送提醒
     * 早上8:30执行一次，9点到22点每整点执行一次
     */
    @Scheduled(cron = "0 30 8 * * ?") // 早上8:30执行一次
    @Scheduled(cron = "0 0 9-22 * * ?") // 9点到22点每整点执行一次
    @Transactional(readOnly = true)
    public void checkWorkflowApprovalTimeout() {
        logger.info("开始执行流程审批超时检查任务，当前时间: {}", LocalDateTime.now());

        try {
            // 查找所有状态为"处理中"的流程实例
            List<WorkflowInstance> processingInstances = workflowInstanceService
                    .findInstancesByStatus(WorkflowStatus.PROCESSING);

            if (processingInstances.isEmpty()) {
                logger.info("当前没有处理中的流程实例，跳过本次检查");
                return;
            }

            logger.info("找到 {} 个处理中的流程实例", processingInstances.size());
            int reminderCount = 0;

            // 遍历所有处理中的流程实例
            for (WorkflowInstance instance : processingInstances) {
                try {
                    // 获取当前流程实例的最新审批记录
                    Optional<ApprovalRecord> latestRecordOpt = approvalRecordService
                            .findLatestRecord(instance.getInstanceId());

                    if (!latestRecordOpt.isPresent()) {
                        logger.warn("流程实例 [{}] 没有审批记录，跳过", instance.getInstanceId());
                        continue;
                    }

                    ApprovalRecord latestRecord = latestRecordOpt.get();
                    LocalDateTime recordCreatedTime = latestRecord.getCreatedDateTime();

                    if (recordCreatedTime == null) {
                        logger.warn("流程实例 [{}] 的最新审批记录 [{}] 没有创建时间，跳过",
                                instance.getInstanceId(), latestRecord.getRecordId());
                        continue;
                    }

                    // 计算审批记录创建时间到现在的工作时间（排除晚上8点到早上8点）
                    LocalDateTime now = LocalDateTime.now();
                    long elapsedHours = calculateWorkingHours(recordCreatedTime, now);

                    logger.debug("流程实例 [{}] 的最新审批记录创建于 {}, 已等待 {} 小时",
                            instance.getInstanceId(), recordCreatedTime, elapsedHours);

                    // 构造审批信息字符串
                    String approveInfo = "上一步审批人: " + latestRecord.getApprover() + ", 审批时间: " +
                            (recordCreatedTime != null
                                    ? recordCreatedTime
                                            .format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
                                    : "无");

                    // 如果审批时间超过4小时，发送提醒通知
                    if (elapsedHours >= 1) {
                        // 获取当前审批人
                        String currentApprover = instance.getCurrentApprover();

                        if (currentApprover == null || currentApprover.isEmpty()) {
                            logger.warn("流程实例 [{}] 当前审批人为空，无法发送提醒", instance.getInstanceId());
                            continue;
                        }

                        // 发送提醒通知，传递 approveInfo
                        sendApprovalReminderNotification(instance, elapsedHours, approveInfo);
                        reminderCount++;

                        // 如果审批时间超过小时，扣除审批人50分
                        if (elapsedHours >= APPROVAL_TIMEOUT_HOURS) {
                            Optional<User> approverUser = userService.findUserByUsername(currentApprover);
                            if (approverUser.isPresent()) {
                                createPenaltyRecord(approverUser.get(), instance, elapsedHours);
                            }
                        }
                    }
                } catch (Exception e) {
                    logger.error("处理流程实例 [{}] 时发生错误: {}", instance.getInstanceId(), e.getMessage(), e);
                }
            }

            logger.info("流程审批超时检查任务完成，共发送 {} 条提醒通知", reminderCount);

        } catch (Exception e) {
            logger.error("执行流程审批超时检查任务时发生错误: {}", e.getMessage(), e);
        }
    }

    /**
     * 发送流程审批超时提醒通知
     *
     * @param instance     流程实例
     * @param elapsedHours 已等待时间（小时）
     * @param approveInfo  审批信息
     */
    private void sendApprovalReminderNotification(WorkflowInstance instance, long elapsedHours, String approveInfo) {
        try {
            String approver = instance.getCurrentApprover();
            logger.info("发送审批超时提醒给用户: {}, 流程实例: {}, 已等待: {} 小时",
                    approver, instance.getInstanceId(), elapsedHours);

            // 查找审批人的微信ID
            String weixinID = "";
            Optional<User> approverUser = userService.findUserByUsername(approver);
            if (approverUser.isPresent() && approverUser.get().getWeixinID() != null
                    && !approverUser.get().getWeixinID().isEmpty()) {
                weixinID = approverUser.get().getWeixinID();
                logger.debug("找到用户 {} 的微信ID: {}", approver, weixinID);
            } else {
                logger.warn("未找到用户 {} 的微信ID，使用用户名代替", approver);
                weixinID = approver;
            }

            // 构建微信消息内容
            StringBuilder contentBuilder = new StringBuilder();
            contentBuilder.append("<@").append(weixinID).append("> ，您有一个待审批的流程已超过 ")
                    .append(elapsedHours).append(" 小时未处理\n\n");

            contentBuilder.append("**流程标题**: ").append(instance.getTitle()).append("\n");
            contentBuilder.append(approveInfo).append("\n");
            contentBuilder.append("**发起人**: ").append(instance.getInitiator()).append("  ");
            // 添加提交时间，如果存在的话
            LocalDateTime submittedTime = instance.getSubmittedDateTime();
            if (submittedTime != null) {
                String formattedSubmittedTime = submittedTime
                        .format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                contentBuilder.append("**提交时间**: ").append(formattedSubmittedTime).append("\n\n");
            } else {
                contentBuilder.append("\n");
            }

            // 发送企业微信消息
            List<String> mentionList = new ArrayList<>();
            mentionList.add(weixinID);
            weixinMessageUtil.sendWeixinMessage("流程审批超时提醒", contentBuilder.toString(), mentionList);

            // 同时发送系统内部消息
            Message systemMessage = new Message();
            systemMessage.setReceiver(approver);
            systemMessage.setMessageTitle("流程审批超时提醒");
            systemMessage.setMessageContent("您有一个流程 [" + instance.getTitle() + "] 已超过 "
                    + elapsedHours + " 小时未审批，请及时处理。");
            systemMessage.setRelatedType("WorkflowInstance");
            systemMessage.setRelatedId(instance.getInstanceId());
            systemMessage.setRead(false);
            systemMessage.setCreatedDate(LocalDateTime.now());
            messageService.saveMessage(systemMessage);

            logger.info("已发送流程审批超时提醒给用户: {}", approver);

        } catch (Exception e) {
            logger.error("发送流程审批超时提醒失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 为审批超时的用户创建积分扣除记录
     *
     * @param approverUser 审批人用户对象
     * @param instance     流程实例
     * @param elapsedHours 超时小时数
     */
    private void createPenaltyRecord(User approverUser, WorkflowInstance instance, long elapsedHours) {
        try {
            // 创建奖罚记录
            RewardPenaltyRecord penaltyRecord = new RewardPenaltyRecord();
            penaltyRecord.setName(approverUser.getUsername());
            penaltyRecord.setType("审批超时");
            penaltyRecord.setReason("流程审批超时 - " + instance.getTitle() + " - 流程ID：" + instance.getInstanceId());
            penaltyRecord.setPoints(-50); // 扣除50分
            penaltyRecord.setRemarks("审批超时" + elapsedHours + "小时，系统自动扣分");

            // 使用服务保存记录（自动计算存量积分）
            rewardPenaltyRecordService.createRecordWithTotalPoints(penaltyRecord);

            logger.info("为用户 {} 创建审批超时扣分记录，流程: {}, 超时: {} 小时, 扣分: 50",
                    approverUser.getUsername(), instance.getTitle(), elapsedHours);

        } catch (Exception e) {
            logger.error("创建审批超时扣分记录失败，用户: {}, 流程: {}, 错误: {}",
                    approverUser.getUsername(), instance.getTitle(), e.getMessage(), e);
        }
    }
}
