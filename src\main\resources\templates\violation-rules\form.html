<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      xmlns:sec="http://www.thymeleaf.org/extras/spring-security"
      layout:decorate="~{fragments/layout}">
<head th:replace="~{fragments/layout :: head(${rule.ruleId == null ? '新建违规规则' : '编辑违规规则'})}">
    <meta charset="UTF-8">
    <title>违规规则管理</title>
</head>
<body th:replace="~{fragments/layout :: body(~{::div.content-wrapper}, ~{})}">
    <div class="content-wrapper">
            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title" th:text="${rule.ruleId == null ? '新建违规规则' : '编辑违规规则'}"></h3>
                            <div class="card-tools">
                                <a th:href="@{/violation-rules}" class="btn btn-outline-secondary btn-sm">
                                    <i class="bi bi-arrow-left"></i> 返回列表
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- 错误消息 -->
                            <div th:if="${errorMessage}" class="alert alert-danger alert-dismissible">
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                <h5><i class="bi bi-exclamation-triangle"></i> 错误!</h5>
                                <span th:text="${errorMessage}"></span>
                            </div>

                            <!-- 表单 -->
                            <form th:action="${rule.ruleId == null ? '/violation-rules' : '/violation-rules/update/' + rule.ruleId}"
                                  method="post" th:object="${rule}">
                                <input type="hidden" name="currentUsername" th:value="${currentUsername}">

                                <div class="form-group mb-4">
                                    <label for="ruleName">规则名称 <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="ruleName" th:field="*{ruleName}" required>
                                </div>

                                <div class="form-group mb-4">
                                    <label for="description">规则描述</label>
                                    <textarea class="form-control" id="description" th:field="*{description}" rows="3"></textarea>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-4">
                                            <label for="taskStatus">任务状态</label>
                                            <select class="form-control" id="taskStatus" th:field="*{taskStatus}">
                                                <option value="">所有状态</option>
                                                <option value="未开始">未开始</option>
                                                <option value="进行中">进行中</option>
                                                <option value="已完成">已完成</option>
                                                <option value="已暂停">已暂停</option>
                                            </select>
                                        </div>

                                        <div class="form-group mb-4">
                                            <label for="taskNameExcludes">排除任务名称（多个用逗号分隔）</label>
                                            <input type="text" class="form-control" id="taskNameExcludes" th:field="*{taskNameExcludes}"
                                                   placeholder="例如: 待调试,人">
                                            <small class="form-text text-muted">任务名称包含这些字符串的将被排除</small>
                                        </div>

                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group mb-4">
                                            <label for="commentDaysThreshold">评论天数阈值</label>
                                            <input type="number" class="form-control" id="commentDaysThreshold" th:field="*{commentDaysThreshold}"
                                                   step="0.1" min="0">
                                            <small class="form-text text-muted">超过此天数未评论的任务将被视为违规（非培训任务）</small>
                                        </div>
                                        <div class="form-group mb-4">
                                            <label for="taskNameIncludes">包含任务名称（多个用逗号分隔）</label>
                                            <input type="text" class="form-control" id="taskNameIncludes" th:field="*{taskNameIncludes}"
                                                   placeholder="留空表示包含所有">
                                            <small class="form-text text-muted">任务名称包含这些字符串的将被包含，留空表示包含所有</small>
                                        </div>

                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group mb-4">
                                            <label for="priority">优先级</label>
                                            <input type="number" class="form-control" id="priority" th:field="*{priority}"
                                                   value="0" min="0">
                                            <small class="form-text text-muted">数字越小优先级越高</small>
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <div class="form-group mb-4">
                                            <label for="score">违规扣分</label>
                                            <input type="number" class="form-control" id="score" th:field="*{score}"
                                                   value="0" min="0">
                                            <small class="form-text text-muted">违规时扣除的分数</small>
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <div class="form-check form-switch">
                                                <input type="checkbox" class="form-check-input" id="enabled" th:field="*{enabled}">
                                                <label class="form-check-label" for="enabled">启用规则</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group text-right">
                                    <a th:href="@{/violation-rules}" class="btn btn-secondary">取消</a>
                                    <button type="submit" class="btn btn-primary">保存</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
    </div>
</body>
</html>
